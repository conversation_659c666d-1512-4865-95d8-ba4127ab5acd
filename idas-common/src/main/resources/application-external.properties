# log
logging.level.root=warn
logging.level.org.apache=warn
logging.level.org.eclipse=warn
logging.level.org.springframework=warn
logging.level.org.spark_project=warn
# datasource
mybatis.mapper-locations=classpath*:mapper/*.xml
spring.datasource.hikari.max-lifetime=50000
spring.datasource.hikari.connection-timeout=60000
spring.datasource.url=jdbc:mysql://**********:3306/lg_interface_tem_hum?useUnicode=true&characterEncoding=utf-8&characterSetResults=utf8&autoReconnect=true&failOverReadOnly=false&useSSL=false&serverTimezone=GMT%2B8
spring.datasource.username=admin
spring.datasource.password=IdasRds#2021
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
mybatis.type-aliases-package=com.gtg56.idas.api.external.entity