<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 全局映射器启用缓存 -->
        <setting name="cacheEnabled" value="true" />
        <!-- 查询时，关闭关联对象即时加载以提高性能 -->
        <setting name="lazyLoadingEnabled" value="true" />
        <!-- 设置关联对象加载的形态，此处为按需加载字段(加载字段由SQL指定)，不会加载关联表的所有字段，以提高性能 -->
        <setting name="aggressiveLazyLoading" value="false" />
        <!-- 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果 -->
        <setting name="multipleResultSetsEnabled" value="true" />
        <!-- 允许使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true" />
        <!-- 允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖 -->
        <setting name="useGeneratedKeys" value="true" />
        <!-- 给予被嵌套的resultMap以字段-属性的映射支持 -->
        <setting name="autoMappingBehavior" value="FULL" />
        <!-- 对于批量更新操作缓存SQL以提高性能  -->
        <setting name="defaultExecutorType" value="BATCH" />
        <!-- 数据库超过25000秒仍未响应则超时 -->
        <setting name="defaultStatementTimeout" value="25000" />
    </settings>
    <!-- 全局别名设置，在映射文件中只需写别名，而不必写出整个类路径  -->
    <!--<typeAliases>-->

    <!--</typeAliases>-->
    <!--&lt;!&ndash; 非注解的sql映射文件配置，如果使用mybatis注解，该mapper无需配置，但是如果mybatis注解中包含@resultMap注解，则mapper必须配置，给resultMap注解使用 &ndash;&gt;-->
    <!--<mappers>-->
    <!--</mappers>-->

    <!--分页插件-->
    <!--    <plugins>-->
    <!--        <plugin interceptor="com.github.pagehelper.PageInterceptor">-->
    <!--            <property name="pageSizeZero" value="true"/>-->
    <!--            <property name="reasonable" value="false"/>-->
    <!--            <property name="supportMethodsArguments" value="false"/>-->
    <!--        </plugin>-->
    <!--    </plugins>-->
</configuration>