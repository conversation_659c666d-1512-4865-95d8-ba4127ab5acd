# 激活
spring.profiles.active=dev
logging.level.com.gargoylesoftware.htmlunit=off
logging.level.org.apache.http.client=warn

# datasource
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=hikariCP
spring.datasource.hikari.max-lifetime=900000
spring.datasource.hikari.connection-timeout=15000
spring.datasource.hikari.connection-test-query=SELECT 1

# mybatis
mybatis.mapper-locations=classpath:/mapper/*.xml
mybatis.config-location=classpath:/config/mybatis.xml
mybatis.type-aliases-package=com.gtg56.idas.common.entity.jdbc

# dubbo
# dubbo服务超时时间
dubbo.consumer.timeout=10000000