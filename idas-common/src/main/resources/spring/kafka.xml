<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="kafkaProducerProperties" class="com.gtg56.idas.common.core.kafka.KafkaProperties" init-method="init">
        <constructor-arg name="propertiesFile" value="/config/kafka-producer.properties"/>
    </bean>
    <bean id="kafkaConsumerProperties" class="com.gtg56.idas.common.core.kafka.KafkaProperties" init-method="init">
        <constructor-arg name="propertiesFile" value="/config/kafka-consumer.properties"/>
    </bean>


</beans>