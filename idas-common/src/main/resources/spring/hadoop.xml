<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="hdfsConf" class="com.gtg56.idas.common.core.hadoop.WarpHDFSConf" init-method="init"/>
    <bean id="hdfs" class="org.apache.hadoop.fs.FileSystem" factory-method="get">
        <constructor-arg name="conf" ref="hdfsConf"/>
    </bean>

</beans>