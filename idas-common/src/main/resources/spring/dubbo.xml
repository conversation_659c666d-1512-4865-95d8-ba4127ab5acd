<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">
    <dubbo:protocol id="dubbo" name="dubbo" port="-1" threadpool="fixed" payload="33554432"/>
    <dubbo:protocol id="hessian" name="hessian" port="-1" threadpool="fixed" payload="33554432"/>

    <dubbo:registry id="zookeeper" protocol="zookeeper" address="${zookeeper.address}?timeout=20000"/>
</beans>