<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
    <property>
        <name>mapreduce.job.split.metainfo.maxsize</name>
        <value>10000000</value>
    </property>
    <property>
        <name>mapreduce.job.counters.max</name>
        <value>120</value>
    </property>
    <property>
        <name>mapreduce.job.counters.groups.max</name>
        <value>50</value>
    </property>
    <property>
        <name>mapreduce.output.fileoutputformat.compress</name>
        <value>false</value>
    </property>
    <property>
        <name>mapreduce.output.fileoutputformat.compress.type</name>
        <value>BLOCK</value>
    </property>
    <property>
        <name>mapreduce.output.fileoutputformat.compress.codec</name>
        <value>org.apache.hadoop.io.compress.DefaultCodec</value>
    </property>
    <property>
        <name>mapreduce.map.output.compress.codec</name>
        <value>org.apache.hadoop.io.compress.SnappyCodec</value>
    </property>
    <property>
        <name>mapreduce.map.output.compress</name>
        <value>true</value>
    </property>
    <property>
        <name>zlib.compress.level</name>
        <value>DEFAULT_COMPRESSION</value>
    </property>
    <property>
        <name>mapreduce.task.io.sort.factor</name>
        <value>64</value>
    </property>
    <property>
        <name>mapreduce.map.sort.spill.percent</name>
        <value>0.8</value>
    </property>
    <property>
        <name>mapreduce.reduce.shuffle.parallelcopies</name>
        <value>10</value>
    </property>
    <property>
        <name>mapreduce.task.timeout</name>
        <value>600000</value>
    </property>
    <property>
        <name>mapreduce.client.submit.file.replication</name>
        <value>2</value>
    </property>
    <property>
        <name>mapreduce.job.reduces</name>
        <value>8</value>
    </property>
    <property>
        <name>mapreduce.task.io.sort.mb</name>
        <value>256</value>
    </property>
    <property>
        <name>mapreduce.map.speculative</name>
        <value>true</value>
    </property>
    <property>
        <name>mapreduce.reduce.speculative</name>
        <value>true</value>
    </property>
    <property>
        <name>mapreduce.job.reduce.slowstart.completedmaps</name>
        <value>0.8</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.address</name>
        <value>Dev-CDH-Master-9-61:10020</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.webapp.address</name>
        <value>Dev-CDH-Master-9-61:19888</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.webapp.https.address</name>
        <value>Dev-CDH-Master-9-61:19890</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.admin.address</name>
        <value>Dev-CDH-Master-9-61:10033</value>
    </property>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.staging-dir</name>
        <value>/user</value>
    </property>
    <property>
        <name>mapreduce.am.max-attempts</name>
        <value>2</value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.resource.mb</name>
        <value>2048</value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.resource.cpu-vcores</name>
        <value>1</value>
    </property>
    <property>
        <name>mapreduce.job.ubertask.enable</name>
        <value>true</value>
    </property>
    <property>
        <name>mapreduce.job.ubertask.maxmaps</name>
        <value>9</value>
    </property>
    <property>
        <name>mapreduce.job.ubertask.maxreduces</name>
        <value>1</value>
    </property>
    <property>
        <name>mapreduce.job.map.output.collector.class</name>
        <value>
            org.apache.hadoop.mapred.nativetask.NativeMapOutputCollectorDelegator,org.apache.hadoop.mapred.MapTask$MapOutputBuffer
        </value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.command-opts</name>
        <value>-Djava.net.preferIPv4Stack=true -Xmx1717986918</value>
    </property>
    <!--'mapreduce.map.java.opts', originally set to '-Djava.net.preferIPv4Stack=true -Xmx1717986918' (non-final), is overridden below by a safety valve-->
    <property>
        <name>mapreduce.reduce.java.opts</name>
        <value>-Djava.net.preferIPv4Stack=true -Xmx1717986918</value>
    </property>
    <property>
        <name>yarn.app.mapreduce.am.admin.user.env</name>
        <value>LD_LIBRARY_PATH=$HADOOP_COMMON_HOME/lib/native:$JAVA_LIBRARY_PATH</value>
    </property>
    <property>
        <name>mapreduce.map.memory.mb</name>
        <value>2048</value>
    </property>
    <property>
        <name>mapreduce.map.cpu.vcores</name>
        <value>1</value>
    </property>
    <property>
        <name>mapreduce.reduce.memory.mb</name>
        <value>4096</value>
    </property>
    <property>
        <name>mapreduce.reduce.cpu.vcores</name>
        <value>2</value>
    </property>
    <property>
        <name>mapreduce.job.heap.memory-mb.ratio</name>
        <value>0.8</value>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>$HADOOP_CLIENT_CONF_DIR,$PWD/mr-framework/*,$MR2_CLASSPATH</value>
    </property>
    <property>
        <name>mapreduce.application.framework.path</name>
        <value>
            hdfs://Dev-CDH-Master-9-61:8020//user/yarn/mapreduce/mr-framework/3.0.0-cdh6.3.2-mr-framework.tar.gz#mr-framework
        </value>
    </property>
    <property>
        <name>mapreduce.jobhistory.jhist.format</name>
        <value>binary</value>
    </property>
    <property>
        <name>mapreduce.admin.user.env</name>
        <value>LD_LIBRARY_PATH=$HADOOP_COMMON_HOME/lib/native:$JAVA_LIBRARY_PATH</value>
    </property>
    <property>
        <name>mapreduce.job.redacted-properties</name>
        <value>
            fs.s3a.access.key,fs.s3a.secret.key,fs.adl.oauth2.credential,dfs.adls.oauth2.credential,fs.azure.account.oauth2.client.secret
        </value>
    </property>
    <property>
        <name>mapreduce.job.acl-view-job</name>
        <value></value>
    </property>
    <property>
        <name>mapreduce.job.acl-modify-job</name>
        <value></value>
    </property>
    <property>
        <name>mapreduce.cluster.acls.enabled</name>
        <value>false</value>
    </property>
    <property>
        <name>mapreduce.map.java.opts</name>
        <value>-Djava.net.preferIPv4Stack=true -Dfile.encoding=utf-8 -Duser.language=zh -Xmx1717986918</value>
    </property>
</configuration>
