<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>file:///data/dfs/nn</value>
    </property>
    <property>
        <name>dfs.namenode.servicerpc-address</name>
        <value>Dev-CDH-Master-9-61:8022</value>
    </property>
    <property>
        <name>dfs.https.address</name>
        <value>Dev-CDH-Master-9-61:9871</value>
    </property>
    <property>
        <name>dfs.https.port</name>
        <value>9871</value>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>Dev-CDH-Master-9-61:9870</value>
    </property>
    <property>
        <name>dfs.replication</name>
        <value>2</value>
    </property>
    <property>
        <name>dfs.blocksize</name>
        <value>134217728</value>
    </property>
    <property>
        <name>dfs.client.use.datanode.hostname</name>
        <value>false</value>
    </property>
    <property>
        <name>fs.permissions.umask-mode</name>
        <value>022</value>
    </property>
    <property>
        <name>dfs.client.block.write.locateFollowingBlock.retries</name>
        <value>7</value>
    </property>
    <property>
        <name>dfs.namenode.acls.enabled</name>
        <value>false</value>
    </property>
    <property>
        <name>dfs.client.read.shortcircuit</name>
        <value>false</value>
    </property>
    <property>
        <name>dfs.domain.socket.path</name>
        <value>/var/run/hdfs-sockets/dn</value>
    </property>
    <property>
        <name>dfs.client.read.shortcircuit.skip.checksum</name>
        <value>false</value>
    </property>
    <property>
        <name>dfs.client.domain.socket.data.traffic</name>
        <value>false</value>
    </property>
    <property>
        <name>dfs.datanode.hdfs-blocks-metadata.enabled</name>
        <value>true</value>
    </property>
</configuration>
