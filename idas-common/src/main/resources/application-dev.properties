# log
logging.level.root=warn
logging.level.org.apache=warn
logging.level.org.eclipse=warn
logging.level.org.springframework=warn
logging.level.org.spark_project=warn
logging.level.com.gtg56.idas.client.inner=info
org.apache.kafka.clients.NetworkClient=error
logging.level.com.gtg56.idas.common.mapper=debug

# datasource
spring.datasource.url=********************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=CdhDB_321
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# redis
spring.redis.host=*********
spring.redis.port=6379
spring.redis.password=CdhRedis_321
spring.redis.timeout=10000
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0 

# external properties
spark.master=yarn
spark.appName=SparkService-onYarn
spark.scheduler.parallelize=6
spark.scheduler.workQueueCapacity=1024
hadoop.coreSite.path=/etc/hadoop/conf/core-site.xml
hadoop.hdfsSite.path=/etc/hadoop/conf/hdfs-site.xml
hadoop.mapredSite.path=/etc/hadoop/conf/mapred-site.xml
hadoop.yarnSite.path=/etc/hadoop/conf/yarn-site.xml
hbase.site.path=/etc/hbase/conf/hbase-site.xml
hbase.multiGet.threads=8
zookeeper.address=*********:2181,*********:2181,*********:2181
kafka.broker.address=*********:9092,*********:9092
tsdb.address=http://*********:4242

### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses=http://**********:9070/xxl-job-admin
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=idas-app-dev
### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9999
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=/home/<USER>/xxl-job/jobhandler
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays=30

dingding.idas.alarm.url=https://oapi.dingtalk.com/robot/send?access_token=65812bcac8c11d06eae703a27d95b26cded33466cde00335d327b9b7b537b259
dingding.alarm.alarm.secret=SECcf2302b528c32f6cb06d8fb57cb3d287962fbe2b92b2ca169e6bc7c0f9d286f3

#nacos配置
spring.cloud.nacos.discovery.server-addr=**********:8848

