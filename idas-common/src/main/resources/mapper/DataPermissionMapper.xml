<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataPermission">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="warehouseCode" column="warehouse_code" jdbcType="CHAR"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="CHAR"/>
        <result property="regionCode" column="region_code" jdbcType="CHAR"/>
        <result property="regionName" column="region_name" jdbcType="CHAR"/>
        <result property="sensorCode" column="sensor_code" jdbcType="CHAR"/>
        <result property="sensorName" column="sensor_name" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.create_time,rs.deleted_flag,
        rs.type,rs.warehouse_code, rs.warehouse_name, rs.region_code, rs.region_name, rs.sensor_code, rs.sensor_name, rs.creator_id, rs.modifier, rs.modify_time
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{roleId}
    </select>

    <select id="listVOByRoleId" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs left join data_role_permission rp on rs.id = rp.data_permission_id
        WHERE rp.data_role_id =#{roleId}
    </select>

    <select id="listVOByUserIdAndType" resultMap="resultMapVO">
        select DISTINCT s.*
        from data_role_user ru
        join data_role r on ru.data_role_id =  r.id and r.status = 1
        join data_role_permission rp on rp.data_role_id = r.id
        join data_permission dp on dp.id = rp.data_permission_id
        <if test="type == 'WAREHOUSE'">
            join warehouse_sensor s on s.warehouse_code = dp.warehouse_code and  r.type = 'WAREHOUSE'
        </if>
        <if test="type == 'REGION'">
            join warehouse_sensor s on s.warehouse_code = dp.warehouse_code and s.region_code = dp.region_code and r.type = 'REGION'
        </if>
        <if test="type == 'SENSOR'">
            join warehouse_sensor s on s.warehouse_code = dp.warehouse_code and s.region_code = dp.region_code and s.sensor_code = dp.sensor_code and r.type = 'SENSOR'
        </if>
        where  ru.user_id= #{userId}
    </select>

    <select id="listVOByType" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs where rs.type = #{type}
    </select>

    <select id="findNewPermissionForWarehouse" resultMap="baseResultMap">
        select distinct 'WAREHOUSE' as type, s.warehouse_code,s.warehouse_name from warehouse_sensor s
            left join data_permission p on s.warehouse_code = p.warehouse_code and p.type = 'WAREHOUSE'
        WHERE p.id is null
    </select>

    <select id="findNewPermissionForRegion" resultMap="baseResultMap">
        select distinct 'REGION' as type, s.warehouse_code,s.warehouse_name, s.region_code,s.region_name from warehouse_sensor s
               left join data_permission p on s.warehouse_code = p.warehouse_code and s.region_code = p.region_code and p.type = 'REGION'
        WHERE p.id is null
    </select>

    <select id="findNewPermissionForSensor" resultMap="baseResultMap">
        select distinct 'SENSOR' as type,s.warehouse_code,s.warehouse_name,s.region_code,s.region_name,s.sensor_code,s.sensor_name
        from warehouse_sensor s
        left join data_permission p on s.warehouse_code = p.warehouse_code and s.region_code = p.region_code and s.sensor_code = p.sensor_code and p.type = 'SENSOR'
        WHERE p.id is null
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_permission rs
        <include refid="commonConditions"/>
    </select>

    <sql id="commonConditions">
        <where>
                <if test="params.warehouseCode != null AND params.warehouseCode != ''">
                    AND rs.warehouse_code LIKE CONCAT('%', #{params.warehouseCode}, '%')
                </if>
                <if test="params.warehouseName != null AND params.warehouseName != ''">
                    AND rs.warehouse_name LIKE CONCAT('%', #{params.warehouseName}, '%')
                </if>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataPermission">
        UPDATE data_permission
        <set>
            warehouse_code=#{warehouseCode},
            warehouse_name=#{warehouseName},
            region_code=#{regionCode},
            region_name=#{regionName},
            sensor_code=#{sensorCode},
            sensor_name=#{sensorName},
            creator=#{creator},
            modifier=#{modifier},
            mofidy_time=#{mofidyTime},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
