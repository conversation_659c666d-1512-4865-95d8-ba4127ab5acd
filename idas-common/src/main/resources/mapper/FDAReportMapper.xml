<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.FDAReportMapper">
    <resultMap id="baseMap" type="com.gtg56.idas.common.entity.jdbc.FDAReport">
        <id property="id" column="id"/>
        <result property="fdaCode" column="fda_code"/>
        <result property="reportTime" column="report_time"/>
        <result property="reportCount" column="report_count"/>
        <result property="reportExtInfo" column="report_ext_info"/>
        <result property="reportContent" column="report_content"/>
        <result property="reportContentCompress" column="report_content_compress"/>
        <result property="statusMsg" column="status_msg"/>
        <result property="statusFlag" column="status_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <select id="find" resultMap="baseMap">
        SELECT *
        FROM fda_report
        <where>
            <if test="startTime != null">
                and report_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and report_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="fdaCode != null and fdaCode != ''">
                and fda_code = #{fdaCode}
            </if>
        </where>
        ORDER BY report_time desc
    </select>
</mapper>