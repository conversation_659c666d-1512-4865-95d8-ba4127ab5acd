<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataRoleUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataRoleUser">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="dataRoleId" column="data_role_id" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="dataRoleCode" column="data_role_code" jdbcType="CHAR"/>
        <result property="dataRoleName" column="data_role_name" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.deleted_flag,rs.creator_id,rs.create_time,rs.modifier,rs.modify_time,
        rs.user_id, rs.data_role_id
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_user rs
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_user rs
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
            rs.id,rs.deleted_flag,rs.creator_id, DATE_FORMAT(rs.create_time,'%y-%m-%d') as create_time,rs.modifier, DATE_FORMAT(rs.modify_time,'%y-%m-%d') as modify_time,
            rs.user_id, rs.data_role_id,dr.name as data_role_name,dr.code as data_role_code
        FROM data_role_user rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_user rs
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByUserId" resultMap="resultMapVO">
        SELECT
            rs.id,rs.deleted_flag,rs.creator_id, rs.create_time,rs.modifier, rs.modify_time,
            rs.user_id, rs.data_role_id,dr.name as data_role_name,dr.code as data_role_code
        FROM data_role_user rs  join data_role dr on dr.id = rs.data_role_id and dr.status = 1
        WHERE rs.user_id=#{userId}
    </select>

    <sql id="commonConditions">
        <where>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataRoleUser">
        UPDATE data_role_user
        <set>
            user_id=#{userId},
            data_role_id=#{dataRoleId},
            modifier=#{modifier},
            modify_time=#{modifyTime},
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteByUserId">
        delete from data_role_user
        WHERE user_id = #{UserId}
    </delete>

</mapper>
