<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.WarehouseSensorMapper">
    <resultMap id="BaseResultMap" type="com.gtg56.idas.common.entity.jdbc.WarehouseSensor">
        <id column="id" property="id"/>
        <result column="corp_code" property="corpCode"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="region_code" property="regionCode"/>
        <result column="region_name" property="regionName"/>
        <result column="sensor_code_origin" property="sensorCodeOrigin"/>
        <result column="sensor_code" property="sensorCode"/>
        <result column="sensor_name" property="sensorName"/>
        <result column="sensor_type" property="sensorType"/>
        <result column="temperature_high_limit" property="temperatureHighLimit"/>
        <result column="temperature_low_limit" property="temperatureLowLimit"/>
        <result column="humidity_high_limit" property="humidityHighLimit"/>
        <result column="humidity_low_limit" property="humidityLowLimit"/>
        <result column="sensor_function" property="sensorFunction"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.sensor.WarehouseSensorDTO">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result column="corp_code" property="corpCode"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="region_code" property="regionCode"/>
        <result column="region_name" property="regionName"/>
        <result column="sensor_code_origin" property="sensorCodeOrigin"/>
        <result column="sensor_code" property="sensorCode"/>
        <result column="sensor_name" property="sensorName"/>
        <result column="sensor_type" property="sensorType"/>
        <result column="temperature_high_limit" property="temperatureHighLimit"/>
        <result column="temperature_low_limit" property="temperatureLowLimit"/>
        <result column="humidity_high_limit" property="humidityHighLimit"/>
        <result column="humidity_low_limit" property="humidityLowLimit"/>
        <result column="sensor_function" property="sensorFunction"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <sql id="zdbxTypes">
        IF(corp_code='GZ_ZDBX',sensor_type in ('0','3','8','9'), true)
    </sql>

    <select id="listWarehouse" resultMap="BaseResultMap">
        select warehouse_code , warehouse_name
        from warehouse_sensor
        group by warehouse_code , warehouse_name
    </select>

    <select id="getBy" resultMap="BaseResultMap">
        select *
        from warehouse_sensor
        where warehouse_code = #{warehouseCode}
        and region_code = #{regionCode} and sensor_code = #{sensorCode}
        limit 1
    </select>
    <select id="findBy" resultMap="BaseResultMap">
        select *
        from warehouse_sensor
        <where>
            <if test="corpCode != null and corpCode != ''">
                AND corp_code = #{corpCode}
            </if>
            <if test="warehouseCode != null and warehouseCode != ''">
                AND warehouse_code = #{warehouseCode}
            </if>
            <if test="regionCode != null and regionCode != ''">
                AND region_code = #{regionCode}
            </if>
            <if test="sensorCode != null and sensorCode != ''">
                AND sensor_code = #{sensorCode}
            </if>
            <if test="sensorFunction != null and sensorFunction != ''">
                AND sensor_function = #{sensorFunction}
            </if>
            and
            <include refid="zdbxTypes"/>
        </where>
        ORDER BY modify_time desc,id asc
    </select>

    <select id="findBySensorCodes" resultMap="BaseResultMap">
        select *
        from warehouse_sensor
        where
        <if test="sensorCodes != null and sensorCodes.size() > 0">
            sensor_code in
            <foreach collection="sensorCodes" item="sensorCode" open="(" close=")" separator=",">
                #{sensorCode}
            </foreach>
            and
        </if>

        <include refid="zdbxTypes"/>
        ORDER BY id
    </select>

    <select id="findWarehouseSensorDTOList" resultMap="BaseResultMap">
        select s.* from  data_permission p,warehouse_sensor s
        where p.id =  #{permissionId}
          and ((p.type = 'WAREHOUSE' AND s.warehouse_code = p.warehouse_code)
            OR 	(p.type = 'REGION' AND s.warehouse_code = p.warehouse_code AND s.region_code = p.region_code)
            OR 	(p.type = 'SENSOR' AND s.warehouse_code = p.warehouse_code AND   s.region_code = p.region_code AND s.sensor_code = p.sensor_code))
          and s.`status` = 1;
    </select>




</mapper>