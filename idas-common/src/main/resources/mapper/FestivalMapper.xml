<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.FestivalMapper">
    <resultMap id="BaseResultMap" type="com.gtg56.idas.common.entity.jdbc.Festival">
        <id column="id" property="id"/>
        <result column="festival_start_year" property="festivalStartYear"/>
        <result column="festival_end_year" property="festivalEndYear"/>
        <result column="festival_name" property="festivalName"/>
        <result column="lunar_festival_name" property="lunarFestivalName"/>
        <result column="festival_start_date" property="festivalStartDate"/>
        <result column="festival_end_date" property="festivalEndDate"/>
        <result column="workday_list" property="workdayList"/>
    </resultMap>

    <select id="findByYear" resultMap="BaseResultMap">
        SELECT *
        FROM festival
        WHERE festival_start_year = #{year} OR festival_end_year = #{year}
    </select>
</mapper>