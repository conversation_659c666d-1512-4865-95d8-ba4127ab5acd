<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.RdbIncrSyncOffsetMapper">
    <resultMap id="BaseResultMap" type="com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset">
        <id column="id" property="id"/>
        <result column="url" property="url"/>
        <result column="table_name" property="tableName"/>
        <result column="last_id" property="lastId"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <update id="set">
        UPDATE rdb_incr_sync_offset SET last_id = #{lastId}
            WHERE url = #{url} and table_name = #{tableName}
    </update>

</mapper>