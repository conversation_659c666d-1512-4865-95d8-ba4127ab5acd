<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataRole">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="code" column="code" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="typeName" column="type_name" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="CHAR"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.auth.DataRoleDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
        <result property="typeName" column="type_name" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.create_time,rs.deleted_flag,
        rs.code, rs.name, rs.status, rs.type,case when rs.type = 'WAREHOUSE' then  '仓库'  when rs.type = 'REGION' then '库房'   when rs.type = 'SENSOR' then '测点终端' end as type_name, rs.remark, rs.creator_id, rs.modifier, rs.modify_time
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role rs
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role rs
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.code=#{id}
    </select>

    <select id="selectByCode" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role rs
        WHERE rs.code=#{code} and rs.deleted_flag = 0
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role rs
        <include refid="commonConditions"/>
    </select>

    <sql id="commonConditions">
        <where>
                <if test="params.code != null and params.code != ''">
                    AND rs.code LIKE CONCAT('%', #{params.code}, '%')
                </if>
                <if test="params.name != null and params.name != ''">
                    AND rs.name LIKE CONCAT('%', #{params.name}, '%')
                </if>
                <if test="params.status != null and params.status != ''">
                    AND rs.status LIKE CONCAT('%', #{params.status}, '%')
                </if>
                <if test="params.type != null and params.type != ''">
                    AND rs.type LIKE CONCAT('%', #{params.type}, '%')
                </if>
                <if test="params.remark != null and params.remark != ''">
                    AND rs.remark LIKE CONCAT('%', #{params.remark}, '%')
                </if>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataRole">
        UPDATE data_role
        <set>
            code=#{code},
            name=#{name},
            status=#{status},
            type=#{type},
            remark=#{remark},
            creator_id=#{creatorId},
            modifier=#{modifier},
            modify_time=#{modifyTime},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
