<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataCheckResultDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="tradeDate" column="trade_date" jdbcType="TIMESTAMP"/>
        <result property="dataCheckConfigDetailId" column="data_check_config_detail_id" jdbcType="BIGINT"/>
        <result property="warehouseSensorId" column="warehouse_sensor_id" jdbcType="BIGINT"/>
        <result property="warehouseCode" column="warehouse_code" jdbcType="CHAR"/>
        <result property="regionCode" column="region_code" jdbcType="CHAR"/>
        <result property="sensorCode" column="sensor_code" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="CHAR"/>
        <result property="missQuantity" column="miss_quantity" jdbcType="INTEGER"/>
        <result property="checkTime" column="check_time" jdbcType="TIMESTAMP"/>
        <result property="recheckTime" column="recheck_time" jdbcType="TIMESTAMP"/>
        <result property="checkStatus" column="check_status" jdbcType="INTEGER"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="dataCheckConfigName" column="name" jdbcType="CHAR"/>
        <result property="dataCheckConfigCode" column="code" jdbcType="CHAR"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="CHAR"/>
        <result property="regionName" column="region_name" jdbcType="CHAR"/>
        <result property="sensorName" column="sensor_name" jdbcType="CHAR"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="CHAR"/>
        <result property="modifier" column="modifier" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        c.name, c.code,s.warehouse_name,s.region_name,s.sensor_name,rs.id,rs.creator_id,rs.create_time,rs.modifier,rs.deleted_flag,
        rs.trade_date, rs.data_check_config_detail_id, rs.warehouse_sensor_id,rs.warehouse_code,rs.region_code,rs.sensor_code, rs.remark, rs.miss_quantity, rs.check_time, rs.recheck_time, rs.check_status, rs.modify_time
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
        left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
        left join data_check_config c on c.id = d.data_check_config_id
        left join warehouse_sensor s on s.id = rs.warehouse_sensor_id
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
        left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
        left join data_check_config c on c.id = d.data_check_config_id
        left join warehouse_sensor s on s.id = rs.warehouse_sensor_id
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
        left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
        left join data_check_config c on c.id = d.data_check_config_id
        left join warehouse_sensor s on s.id = rs.warehouse_sensor_id
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
        left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
        left join data_check_config c on c.id = d.data_check_config_id
        left join warehouse_sensor s on s.id = rs.warehouse_sensor_id
        <include refid="commonConditions"/>
    </select>

    <select id="findDataCheckResultDetailList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result_detail rs
        left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
        left join data_check_config c on c.id = d.data_check_config_id
        left join warehouse_sensor s on s.id = rs.warehouse_sensor_id
        where rs.data_check_config_detail_id =#{dataCheckConfigDetailId}
          and rs.trade_date >= #{startDate}
          and rs.trade_date <![CDATA[ <= ]]> #{endDate}</select>
    <sql id="commonConditions">
        <where>
                <if test="params.dataCheckConfigDetailId != null">
                    AND rs.data_check_config_detail_id = #{params.dataCheckConfigDetailId}
                </if>
                <if test="params.dataPermissionId != null">
                    AND rs.warehosue_sensor_id = #{params.dataPermissionId}
                </if>
                <if test="params.remark != null and params.remark != ''">
                    AND rs.remark LIKE CONCAT('%', #{params.remark}, '%')
                </if>
                <if test="params.missQuantity != null and params.missQuantity != ''">
                    AND rs.miss_quantity LIKE CONCAT('%', #{params.missQuantity}, '%')
                </if>
                <if test="params.tradeDateStart != null">
                    AND rs.trade_date &gt;= #{params.tradeDateStart}
                </if>
                <if test="params.tradeDateEnd != null">
                    AND rs.trade_date &lt;= #{params.tradeDateEnd}
                </if>
            <if test="params.checkTimeStart != null">
                AND (rs.check_time &gt;= #{params.checkTimeStart} or rs.recheck_time &gt;= #{params.checkTimeStart})
            </if>
            <if test="params.checkTimeEnd != null">
                AND (rs.check_time &lt;= #{params.checkTimeEnd} or rs.recheck_time &lt;= #{params.checkTimeEnd})
            </if>
                <if test="params.recheckTimeStart != null">
                    AND rs.recheck_time &gt;= #{params.recheckTimeStart}
                </if>
                <if test="params.recheckTimeEnd != null">
                    AND rs.recheck_time &lt;= #{params.recheckTimeEnd}
                </if>
                <if test="params.checkStatus != null and params.checkStatus != ''">
                    AND rs.check_status LIKE CONCAT('%', #{params.checkStatus}, '%')
                </if>
                <if test="params.creatorId != null">
                    AND rs.creator_id = #{params.creatorId}
                </if>
                <if test="params.createTimeStart != null">
                    AND rs.create_time &gt;= #{params.createTimeStart}
                </if>
                <if test="params.createTimeEnd != null">
                    AND rs.create_time &lt;= #{params.createTimeEnd}
                </if>
                <if test="params.modifier != null">
                    AND rs.modifier = #{params.modifier}
                </if>
                <if test="params.modifyTimeStart != null">
                    AND rs.modify_time &gt;= #{params.modifyTimeStart}
                </if>
                <if test="params.modifyTimeEnd != null">
                    AND rs.modify_time &lt;= #{params.modifyTimeEnd}
                </if>
                <if test="params.deletedFlag != null">
                    AND rs.deleted_flag = #{params.deletedFlag}
                </if>
                <if test="params.dataCheckConfigName != null and params.dataCheckConfigName != ''">
                    AND c.name LIKE CONCAT('%', #{params.dataCheckConfigName}, '%')
                </if>
                <if test="params.dataCheckConfigCode != null and params.dataCheckConfigCode != ''">
                    AND c.code LIKE CONCAT('%', #{params.dataCheckConfigCode}, '%')
                </if>
                <if test="params.warehouseName != null and params.warehouseName != ''">
                    AND s.warehouse_name LIKE CONCAT('%', #{params.warehouseName}, '%')
                </if>
                <if test="params.regionName != null and params.regionName != ''">
                    AND s.region_name LIKE CONCAT('%', #{params.regionName}, '%')
                </if>
                <if test="params.sensorName != null and params.sensorName != ''">
                    AND s.sensor_name LIKE CONCAT('%', #{params.sensorName}, '%')
                </if>
                <if test="params.sensorCode != null and params.sensorCode != ''">
                    AND s.sensor_code LIKE CONCAT('%', #{params.sensorCode}, '%')
                </if>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail">
        UPDATE data_check_result_detail
        <set>
            trade_date=#{tradeDate},
            data_check_config_detail_id=#{dataCheckConfigDetailId},
            warehouse_sensor_id=#{warehouseSensorId},
            remark=#{remark},
            miss_quantity=#{missQuantity},
            check_time=#{checkTime},
            recheck_time=#{recheckTime},
            check_status=#{checkStatus},
            modify_time=#{modifyTime},
            updater_id=#{updaterId},
            update_time=#{updateTime},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
