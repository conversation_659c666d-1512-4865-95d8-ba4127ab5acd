<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.ExternalTableMapper">
    <resultMap id="BaseResultMap" type="com.gtg56.idas.common.entity.jdbc.ExternalTable">
        <id column="id" property="id"/>
        <result column="namespace" property="namespace"/>
        <result column="table_name" property="tableName"/>
        <result column="table_properties" property="tableProperties"/>
        <result column="meta_store_path" property="metaStorePath"/>
        <result column="store_path" property="storePath"/>
        <result column="mode" property="mode"/>
        <result column="active" property="active"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>
</mapper>