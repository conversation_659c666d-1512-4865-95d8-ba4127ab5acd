<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.GncAppKeyMapper">
    <resultMap id="baseMap" type="com.gtg56.idas.common.entity.jdbc.GncAppKey">
        <id property="id" column="id"/>
        <result property="appKey" column="app_key"/>
        <result property="projectName" column="project_name"/>
    </resultMap>

    <select id="numKey" resultType="Integer">
        SELECT count(1)
        FROM gnc_app_key
        WHERE app_key = #{appKey}
    </select>
</mapper>