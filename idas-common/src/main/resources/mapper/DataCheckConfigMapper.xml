<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataCheckConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataCheckConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="code" column="code" jdbcType="CHAR"/>
        <result property="name" column="name" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="checkType" column="check_type" jdbcType="CHAR"/>
        <result property="checkDataType" column="check_data_type" jdbcType="CHAR"/>
        <result property="days" column="days" jdbcType="INTEGER"/>
        <result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
        <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="CHAR"/>
        <result property="lastCheckTime" column="last_check_time" jdbcType="TIMESTAMP"/>
        <result property="lastCheckStatus" column="last_check_status" jdbcType="CHAR"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="CHAR"/>
        <result property="modifier" column="modifier" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.creator_id,rs.create_time,rs.deleted_flag,
        rs.code, rs.name, rs.status, rs.check_type, rs.check_data_type,rs.days,rs.start_date,rs.end_date, rs.remark, rs.last_check_time, rs.last_check_status, rs.modifier, rs.modify_time
    </sql>

    <sql id="voColumnList">

    </sql>

    <insert id="insertData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO data_check_config(code, name, status, check_type, check_data_type, days, start_date, end_date, last_check_time, last_check_status, remark, creator_id, create_time)
                VALUES (#{code}, #{name}, #{status}, #{checkType}, #{checkDataType}, #{days},  #{startDate}, #{endDate}, #{lastCheckTime}, #{lastCheckStatus}, #{remark}, #{creatorId},  #{createTime});
    </insert>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        dcr.last_check_time,
        dcr.last_check_status,
        <include refid="baseColumnList" />

        FROM data_check_config rs
        LEFT JOIN
        (
            SELECT
                dcr1.data_check_config_id,
                dcr1.excute_time AS last_check_time,
                dcr1.check_status AS last_check_status
            FROM  data_check_result dcr1
            INNER JOIN
            (
                SELECT
                data_check_config_id,
                MAX(excute_time) AS max_check_time
                FROM
                data_check_result
                GROUP BY
                data_check_config_id
            ) dcr2 ON dcr1.data_check_config_id = dcr2.data_check_config_id AND dcr1.excute_time = dcr2.max_check_time
        ) dcr ON rs.id = dcr.data_check_config_id
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config rs
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config rs
        <include refid="commonConditions"/>
    </select>

    <sql id="commonConditions">
        <where>

            rs.deleted_flag = 0

            <if test="params.name != null and params.name != ''">
                AND rs.name  LIKE CONCAT('%', #{params.name}, '%')
            </if>
            <if test="params.code != null and params.code != ''">
                AND rs.code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="params.checkType != null">
                and rs.check_type = #{params.checkType}
            </if>
            <if test="params.checkDataType != null">
                and rs.check_data_type = #{params.checkDataType}
            </if>
            <if test="params.status != null  and params.status != ''">
                and rs.status = #{params.status}
            </if>

        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataCheckConfig">
        UPDATE data_check_config
        <set>
            code=#{code},
            name=#{name},
            status=#{status},
            check_type=#{checkType},
            check_data_type=#{checkDataType},
            days=#{days},
            start_date=#{startDate},
            end_date=#{endDate},
            remark=#{remark},
            last_check_time=#{lastCheckTime},
            last_check_status=#{lastCheckStatus},
            modifier=#{modifier},
            modify_time=#{modifyTime},
            deleted_flag=#{deletedFlag}
        </set>
        WHERE id = #{id}
    </update>

</mapper>
