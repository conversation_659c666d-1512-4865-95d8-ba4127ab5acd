<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.UserAuthMapper">
    <resultMap id="baseMap" type="com.gtg56.idas.common.entity.jdbc.UserAuth">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="authRealm" column="authRealm"/>
        <result property="authSubject" column="auth_subject"/>
        <result property="createTime" column="create_time"/>
        <result property="creator" column="creator"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifier" column="modifier"/>
    </resultMap>

    <select id="find" resultMap="baseMap">
        SELECT *
        FROM user_auth
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="username != null and username != ''">
                and username = #{username}
            </if>
            <if test="authRealm != null and authRealm != ''">
                and auth_realm = #{authRealm}
            </if>
        </where>
    </select>
</mapper>