<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataRolePermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataRolePermission">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="dataRoleId" column="data_role_id" jdbcType="BIGINT"/>
        <result property="dataPermissionId" column="data_permission_id" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.auth.DataRolePermissionDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.deleted_flag,
        rs.data_role_id, rs.data_permission_id
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_permission rs
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_permission rs
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_permission rs
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_role_permission rs
        <include refid="commonConditions"/>
    </select>

    <sql id="commonConditions">
        <where>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataRolePermission">
        UPDATE data_role_permission
        <set>
            data_role_id=#{dataRoleId},
            data_permission_id=#{dataPermissionId},
            modifier=#{modifier},
            modify_time=#{modifyTime},
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteDataByRoleId">
        delete from data_role_permission
        WHERE data_role_id = #{dataRoleId}
    </delete>

</mapper>
