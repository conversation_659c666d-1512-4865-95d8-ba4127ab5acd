<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataCheckResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataCheckResult">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="dataCheckConfigId" column="data_check_config_id" jdbcType="BIGINT"/>
        <result property="dataCheckConfigDetailId" column="data_check_config_detail_id" jdbcType="BIGINT"/>
        <result property="checkStartDate" column="check_start_date" jdbcType="TIMESTAMP"/>
        <result property="checkEndDate" column="check_end_date" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="CHAR"/>
        <result property="excuteTime" column="excute_time" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="useTime" column="use_time" jdbcType="BIGINT"/>
        <result property="checkStatus" column="check_status" jdbcType="INTEGER"/>
        <result property="normalQuantity" column="normal_quantity" jdbcType="INTEGER"/>
        <result property="unnormalQuantity" column="unnormal_quantity" jdbcType="INTEGER"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="dataCheckConfigName" column="name" jdbcType="CHAR"/>
        <result property="dataCheckConfigCode" column="code" jdbcType="CHAR"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="CHAR"/>
        <result property="regionName" column="region_name" jdbcType="CHAR"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="CHAR"/>
        <result property="modifier" column="modifier" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        p.warehouse_name,p.region_name,c.name,c.code,rs.id,rs.creator_id,rs.create_time,rs.modifier,rs.deleted_flag,
        rs.data_check_config_id,rs.data_check_config_id,rs.data_check_config_detail_id,rs.check_start_date,rs.check_end_date,rs.normal_quantity,rs.unnormal_quantity,rs.remark, rs.excute_time, rs.start_time, rs.end_time, rs.use_time, rs.check_status, rs.modify_time
    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result rs
            left join data_check_config c on c.id = rs.data_check_config_id
            left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
            left join data_permission p on p.id = d.data_permission_id
        <include refid="commonConditions"/>
        order by rs.excute_time desc
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result rs
            left join data_check_config c on c.id = rs.data_check_config_id
            left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
            left join data_permission p on p.id = d.data_permission_id
        <include refid="commonConditions"/>
        order by rs.excute_time desc
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result rs
            left join data_check_config c on c.id = rs.data_check_config_id
            left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
            left join data_permission p on p.id = d.data_permission_id
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
        order by rs.excute_time desc
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_result rs
            left join data_check_config c on c.id = rs.data_check_config_id
            left join data_check_config_detail d on d.id = rs.data_check_config_detail_id
            left join data_permission p on p.id = d.data_permission_id
        <include refid="commonConditions"/>
        order by rs.excute_time desc
    </select>

    <sql id="commonConditions">
        <where>
                <if test="params.dataCheckConfigName != null and params.dataCheckConfigName != ''">
                    AND c.name  LIKE CONCAT('%', #{params.dataCheckConfigName}, '%')
                </if>
                <if test="params.dataCheckConfigCode != null and params.dataCheckConfigCode != ''">
                    AND c.code LIKE CONCAT('%', #{params.dataCheckConfigCode}, '%')
                </if>
                <if test="params.dataCheckConfigId != null">
                    AND rs.data_check_config_id = #{params.dataCheckConfigId}
                </if>
                <if test="params.remark != null and params.remark != ''">
                    AND rs.remark LIKE CONCAT('%', #{params.remark}, '%')
                </if>
                <if test="params.excuteTimeStart != null">
                    AND rs.excute_time &gt;= #{params.excuteTimeStart}
                </if>
                <if test="params.excuteTimeEnd != null">
                    AND rs.excute_time &lt;= #{params.excuteTimeEnd}
                </if>
                <if test="params.startTimeStart != null">
                    AND rs.start_time &gt;= #{params.startTimeStart}
                </if>
                <if test="params.startTimeEnd != null">
                    AND rs.start_time &lt;= #{params.startTimeEnd}
                </if>
                <if test="params.endTimeStart != null">
                    AND rs.end_time &gt;= #{params.endTimeStart}
                </if>
                <if test="params.endTimeEnd != null">
                    AND rs.end_time &lt;= #{params.endTimeEnd}
                </if>
                <if test="params.useTime != null">
                    AND rs.use_time = #{params.useTime}
                </if>
                <if test="params.checkStatus != null and params.checkStatus != ''">
                    AND rs.check_status LIKE CONCAT('%', #{params.checkStatus}, '%')
                </if>
                <if test="params.creatorId != null">
                    AND rs.creator_id = #{params.creatorId}
                </if>
                <if test="params.createTimeStart != null">
                    AND rs.create_time &gt;= #{params.createTimeStart}
                </if>
                <if test="params.createTimeEnd != null">
                    AND rs.create_time &lt;= #{params.createTimeEnd}
                </if>
                <if test="params.modifier != null">
                    AND rs.modifier = #{params.modifier}
                </if>
                <if test="params.modifyTimeStart != null">
                    AND rs.modify_time &gt;= #{params.modifyTimeStart}
                </if>
                <if test="params.modifyTimeEnd != null">
                    AND rs.modify_time &lt;= #{params.modifyTimeEnd}
                </if>
                <if test="params.deletedFlag != null">
                    AND rs.deleted_flag = #{params.deletedFlag}
                </if>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataCheckResult">
        UPDATE data_check_result
        <set>
            data_check_config_id=#{dataCheckConfigId},
            remark=#{remark},
            excute_time=#{excuteTime},
            start_time=#{startTime},
            end_time=#{endTime},
            use_time=#{useTime},
            check_status=#{checkStatus},
            modify_time=#{modifyTime},
            updater_id=#{updaterId},
            update_time=#{updateTime},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
