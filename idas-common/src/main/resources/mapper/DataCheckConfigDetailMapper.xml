<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.common.mapper.DataCheckConfigDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="BIGINT"/>
<!--        <result property="deletedFlag" column="deleted_flag" jdbcType="INTEGER"/>-->
        <result property="dataCheckConfigId" column="data_check_config_id" jdbcType="BIGINT"/>
        <result property="dataPermissionId" column="data_permission_id" jdbcType="BIGINT"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="resultMapVO" type="com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO" extends="baseResultMap">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="CHAR"/>
        <result property="modifier" column="modifier" jdbcType="CHAR"/>
        <result property="warehouseCode" column="warehouse_code" jdbcType="CHAR"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="CHAR"/>
        <result property="regionCode" column="region_code" jdbcType="CHAR"/>
        <result property="regionName" column="region_name" jdbcType="CHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        rs.id,rs.creator_id,rs.create_time,rs.modifier,rs.deleted_flag,
        rs.data_check_config_id, rs.data_permission_id, rs.modify_time,
        dp.warehouse_code,dp.warehouse_name,dp.region_code,dp.region_name

    </sql>

    <sql id="voColumnList">

    </sql>

    <select id="listByQuery" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config_detail rs
        left join data_permission dp on dp.id = rs.data_permission_id
        <include refid="commonConditions"/>
    </select>

    <select id="listByMap" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config_detail rs
        left join data_permission dp on dp.id = rs.data_permission_id
        <include refid="commonConditions"/>
    </select>

    <sql id="baseVOSelect">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config_detail rs
        left join data_permission dp on dp.id = rs.data_permission_id
    </sql>

    <select id="selectVOById" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id=#{id}
    </select>

    <select id="listVOByIds" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        WHERE rs.id IN <foreach collection="coll" item="id" index="index" open="(" close=")" separator=",">#{id}</foreach>
    </select>

    <select id="listVOByQuery" resultMap="resultMapVO">
        <include refid="baseVOSelect"/>
        <include refid="commonConditions"/>
    </select>

    <select id="listVOByMap" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config_detail rs
        left join data_permission dp on dp.id = rs.data_permission_id
        <include refid="commonConditions"/>
    </select>

    <select id="findByConfigId" resultMap="resultMapVO">
        SELECT
        <include refid="baseColumnList" />
        FROM data_check_config_detail rs
        left join data_permission dp on dp.id = rs.data_permission_id
        where rs.data_check_config_id =  #{dataCheckConfigId} and rs.deleted_flag = 0
    </select>

    <sql id="commonConditions">
        <where>
                <if test="params.dataCheckConfigId != null">
                    and rs.data_check_config_id = #{params.dataCheckConfigId}
                </if>
                <if test="params.dataPermissionId != null">
                    and rs.data_permission_id = #{params.dataPermissionId}
                </if>
                <if test="params.creatorId != null">
                    and rs.creator_id = #{params.creatorId}
                </if>
                <if test="params.createTimeStart != null">
                    and rs.create_time &gt;= #{params.createTimeStart}
                </if>
                <if test="params.createTimeEnd != null">
                    and rs.create_time &lt;= #{params.createTimeEnd}
                </if>
                <if test="params.modifier != null">
                    and rs.modifier = #{params.modifier}
                </if>
                <if test="params.modifyTimeStart != null">
                    and rs.modify_time &gt;= #{params.modifyTimeStart}
                </if>
                <if test="params.modifyTimeEnd != null">
                    and rs.modify_time &lt;= #{params.modifyTimeEnd}
                </if>
                <if test="params.deletedFlag != null">
                    and rs.deleted_flag = #{params.deletedFlag}
                </if>
        </where>
    </sql>

    <update id="updateById" parameterType="com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail">
        UPDATE data_check_config_detail
        <set>
            data_check_config_id=#{dataCheckConfigId},
            data_permission_id=#{dataPermissionId},
            modify_time=#{modifyTime},
            modifier=#{modifier},
            deleted_flag=#{deletedFlag},
        </set>
        WHERE id = #{id}
    </update>

</mapper>
