/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: PageVO
 * @Author: zhangchuyi
 */
package com.gtg56.lark.web;

import com.github.pagehelper.PageInfo;
import com.gtg56.idas.common.entity.base.ImABean;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;

/**
 * @Description:
 *      分页信息
 *
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION 
 *
 * @Author: zhangchuyi
 * @CeateDate: 2018/9/21 08:54
 * @Since: Bamboo V00.00.001
 */
public class PageObject<T> implements ImABean {

    private static PageObject unPageInstance = new PageObject(1, Integer.MAX_VALUE);

    private int pageNum = 1;

    private int pageSize = 20;

    private Long total;

    private int pages;

    private List<T> resultList;

    private Order[] orders;

    private String orderStr;

    public PageObject(){

    }

    public PageObject(int pageNum, int pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = 0l;
        this.resultList = Collections.emptyList();
    }

    public PageObject(com.github.pagehelper.Page<T> page) {
        BeanUtils.copyProperties(page,this);
        this.resultList = page.getResult();
    }

    public PageObject(List<T> list) {
        this(new PageInfo<>(list));
    }

    public PageObject(PageInfo<T> page) {
        this.setTotal(Long.valueOf(page.getTotal()));
        this.setPages((int)page.getPages());
        this.setPageNum((int)page.getPageNum());
        this.setPageSize((int)page.getPageSize());
        this.setResultList(page.getList());
    }

    public static PageObject getUnPageObject() {
        return unPageInstance;
    }

    public static <T> PageObject<T> memoryPaging(List<T> dataList, int pageNum, int pageSize) {
        PageObject<T> pageObject = new PageObject<T>();
        pageObject.setPageNum(pageNum);
        pageObject.setPageSize(pageSize);
        if(dataList == null) {
            return null;
        }
        pageObject.setTotal((long)dataList.size());
        pageObject.setPages((int)(pageObject.getTotal() / pageSize) + 1);
        pageObject.setResultList(dataList.subList((pageNum - 1) * pageSize, pageNum * pageSize));
        return pageObject;
    }

    public void addOrders(Order... orders) {
        if(orders == null) {
            return;
        }
        if(this.orders == null) {
            this.orders = new Order[]{};
        }
        this.orders = ArrayUtils.addAll(this.orders, orders);
    }

    public Order[] getOrders() {
        if(this.orders == null) {
            this.orders = new Order[]{};
        }
        return orders;
    }

    public String getOrderStr() {
        if(orderStr == null && orders != null) {
            StringBuffer sb = new StringBuffer();
            for (Order order : orders) {
                if(sb.length() > 0) {
                    sb.append(",");
                }
                sb.append(order.getProperty()).append(" ").append(order.getDirection().name());
            }
            orderStr = sb.toString();
        }
        return orderStr;
    }

    public PageObject setOrders(Order[] orders) {
        this.orders = orders;
        return this;
    }

    public int getPageNum() {
        return pageNum;
    }

    public PageObject<T> setPageNum(int pageNum) {
        this.pageNum = pageNum;
        return this;
    }

    public int getPageSize() {
        return pageSize;
    }

    public PageObject<T> setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public Long getTotal() {
        return total;
    }

    public PageObject<T> setTotal(Long total) {
        this.total = total;
        return this;
    }

    public int getPages() {
        return pages;
    }

    public PageObject<T> setPages(int pages) {
        this.pages = pages;
        return this;
    }

    public List<T> getResultList() {
        return resultList;
    }

    public PageObject<T> setResultList(List<T> resultList) {
        this.resultList = resultList;
        return this;
    }

    @Override
    public String toString() {

        return "PageVO{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", total=" + total +
                ", pages=" + pages +
                '}'+super.toString();
    }
}
