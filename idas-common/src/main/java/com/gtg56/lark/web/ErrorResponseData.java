package com.gtg56.lark.web;

import com.gtg56.idas.common.entity.base.ImABean;

/**
 * 成功返回的对象
 *
 * <AUTHOR> 2019-05-27
 */
public class ErrorResponseData<T> extends ResponseData<T> implements ImABean {

    public ErrorResponseData(String message){
        this(null, RESULT_STATUS_FAIL, message);
    }

    public ErrorResponseData(String status, String message){
        this(null, status, message);
    }

    public ErrorResponseData(T data, String status, String message){
        super(data, status, message);
        super.setSuccess(false);
    }

    public ErrorResponseData(Throwable e){
        super.setMsg("抛出异常了！"+ e.getCause().getMessage());
        super.setSuccess(false);
    }
}
