package com.gtg56.lark.web;

import com.gtg56.idas.common.entity.base.ImABean;

/**
 * 成功返回的对象
 *
 * <AUTHOR> 2019-05-27
 */
public class SuccessResponseData<T> extends ResponseData<T> implements ImABean {

    public SuccessResponseData(){
        this(null, RESULT_STATUS_SUCCESS, "");
    }

    public SuccessResponseData(T data){
        this(data, RESULT_STATUS_SUCCESS, "");
    }

    public SuccessResponseData(T data, String message){
        this(data, RESULT_STATUS_SUCCESS, message);
    }

    public SuccessResponseData(T data, String status, String message){
        super(data, status, message);
        super.setSuccess(true);
    }
}
