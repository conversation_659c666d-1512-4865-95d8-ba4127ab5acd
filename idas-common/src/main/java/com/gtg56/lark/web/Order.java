package com.gtg56.lark.web;

/**
 * 排序类
 * <AUTHOR> 2019-08-02
 */
public class Order {
    private final Direction direction;
    private final String property;

    public Order(Direction direction, String property) {
        this.direction = direction;
        this.property = property;
    }

    public Direction getDirection() {
        return direction;
    }

    public String getProperty() {
        return property;
    }

    public boolean isAscending(){
        return direction.equals(Direction.ASC);
    }

    public static Order asc(String property) {
        return new Order(Direction.ASC, property);
    }

    public static Order desc(String property) {
        return new Order(Direction.DESC, property);
    }

    public static Order create(String property, boolean isDesc) {
        return new Order(isDesc ? Direction.DESC : Direction.ASC, property);
    }

    public enum Direction {
        ASC, DESC;
    }
}
