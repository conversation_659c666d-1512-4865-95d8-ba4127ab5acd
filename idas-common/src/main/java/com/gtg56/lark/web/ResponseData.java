/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: ResultVO
 * @Author: zhangchuyi
 */
package com.gtg56.lark.web;

import com.gtg56.idas.common.entity.base.ImABean;

/**
 * @Description:
 *      统一返回对象
 *
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION 
 * 2019/5/27    sp           V00.01.001     修改类名，增加静态方法以明确方法执行的内容
 *
 * @Author: zhangchuyi
 * @CeateDate: 2018/9/21 08:38
 * @Since: Bamboo V00.00.001
 */
public class ResponseData<T> implements ImABean {

    //接口请求正常
    public static final String RESULT_STATUS_SUCCESS = "SUCCESS";
    //接口请求业务失败异常
    public static final String RESULT_STATUS_FAIL = "BIZ_ERROR";

    /**
     * 返回数据对象
     */
    private T data;

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * 返回标识 成功:200 失败：500
     */
    private String code = "200";

    /**
     * 返回信息
     */
    private String msg;

    ResponseData() {

    }

    /**
     * 构造方法
     * @param data 可传对象、列表、分页对象
     * @param status
     * @param message
     */
    ResponseData(T data, String status, String message){
        super();

        this.data = data;
        if(status != null){
            this.code = status;
        }
        this.msg = message;
    }

    public static SuccessResponseData success(){
        return new SuccessResponseData();
    }

    public static SuccessResponseData success(Object data, String message){
        return new SuccessResponseData(data, message);
    }

    public static SuccessResponseData success(Object data){
        return new SuccessResponseData(data);
    }

    public static SuccessResponseData success(Object data, String status, String message){
        return new SuccessResponseData(data, status, message);
    }

    public static ErrorResponseData error(String message){
        return new ErrorResponseData(message);
    }

    public static ErrorResponseData error(String status, String message){
        return new ErrorResponseData(status, message);
    }

    public static ErrorResponseData error(Object data, String status, String message){
        return new ErrorResponseData(data, status, message);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static ErrorResponseData error(Throwable e){
        return new ErrorResponseData(e);
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public ResponseData setSuccess(Boolean success) {
        this.success = success;
        return this;
    }

    @Override
    public String toString() {
        return "ResultVO{" +
                "data=" + data +
                '}'+super.toString();
    }
}
