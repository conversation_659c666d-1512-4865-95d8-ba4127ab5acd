/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: BaseVO
 * @Author: zhang<PERSON>yi
 */
package com.gtg56.lark.common.base.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * <AUTHOR> 2020-11-23
 */
@Data
@Accessors(chain = true)
public class BaseVO implements Serializable {

    @ApiModelProperty(value = "标识")
    private String id;
}
