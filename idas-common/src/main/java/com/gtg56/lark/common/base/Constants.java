package com.gtg56.lark.common.base;


import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class Constants {


    //超级管理员用户名
    public static final String ADMINISTRATOR = "administrator";

    /**
     * 用户登录会话有效期（秒）
     */
    public static final int SHIRO_SESSION_TIMEOUT=60*60*24;


    //AES秘钥
    public static final String AESKEY = "20190101";

    //分隔符
    public static final String SPLIT_CODE = ":";

    //新建，未认证、未激活
    public static final Integer USER_STATUS_NO_ACTIVE = 0;

    //正常
    public static final Integer USER_STATUS_NORMAL = 1;

    //禁用、锁定、不可用
    public static final Integer USER_STATUS_LOCK = 2;





    //订单状态
    //已提交1
    public static final String ORDERSTATUS_CREATED = "CREATED";
    //已受理2
    public static final String ORDERSTATUS_APPROVED = "APPROVED";
    //拣货中3
    public static final String ORDERSTATUS_SUSPENDED = "SUSPENDED";
    //打包中4
    public static final String ORDERSTATUS_ON_VALIDATION = "ON_VALIDATION";
    //出库中5
    public static final String ORDERSTATUS_FRAUD_CHECKED = "FRAUD_CHECKED";
    //配送中6
    public static final String ORDERSTATUS_CHECKED_VALID = "CHECKED_VALID";
    //提货中7
    public static final String ORDERSTATUS_DEPARTURE = "DEPARTURE";
    //在途异常8
    public static final String ORDERSTATUS_ERR0R = "ERR0R";
    //已完成9
    public static final String ORDERSTATUS_COMPLETED = "COMPLETED";
    //已取消10
    public static final String ORDERSTATUS_CANCELLED = "CANCELLED";





    //升序
    public static final int GIFT_SORTORDER_ASE = 0;
    //降序
    public static final int GIFT_SORTORDER_DESC = 1;

    /**
     * ResultVO返回对象的类型-对象
     */
    public static final int DATA_TYPE_OBJECT = 1;

    /**
     * ResultVO返回对象的类型-数组
     */
    public static final int DATA_TYPE_ARRAY = 2;
    /**
     * ResultVO返回对象的类型-MAP
     */
    public static final int DATA_TYPE_MAP = 3;


    //应用类型—PC
    public static final String APPLICATION_PC = "PC";
    //应用类型—WAP
    public static final String APPLICATION_WAP = "WAP";
    //应用类型—ANDROID
    public static final String APPLICATION_ANDROID = "ANDROID";
    //应用类型—IOS
    public static final String APPLICATION_IOS = "IOS";


    public static final Integer MAX_PAGE_SIZE = Integer.MAX_VALUE;

    //加密次数
    public static final int PASSWORD_HASH_ITERATIONS = 2018;

    //shiro session redis 前缀
    public static final String SHIRO_SESSION_KEY_PREFIX = "shiro:session:";


    private Constants() {
    }

    public static String[] values() {
        Field[] fields = Constants.class.getFields();
        String[] s = new String[fields.length];
        for (int i = 0, n = fields.length; i < n; i++) {
            try {
                Field f = fields[i];
                s[i] = (String) f.get(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return s;
    }

    public static Set<String> asSet() {
        return new HashSet<String>(Arrays.asList(values()));
    }

    public static void main(String[] args) {
        System.out.println("values=" + Arrays.asList(values()));
        System.out.println("set=" + asSet());
    }
}
