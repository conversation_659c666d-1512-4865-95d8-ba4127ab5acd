package com.gtg56.lark.common.base.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> 2020-11-23
 */
@Data
@Accessors(chain = true)
public class SaasQuery extends BaseQuery {

    @ApiModelProperty(value = "标识")
    private String id;

    @ApiModelProperty(value = "创建人",dataType = "String",required = false)
    private String creatorId;

    @ApiModelProperty(value = "开始创建时间",dataType = "Date",required = false)
    private Date createTimeStart;

    @ApiModelProperty(value = "结束创建时间",dataType = "Date",required = false)
    private Date createTimeEnd;

    @ApiModelProperty(value = "租户id",dataType = "Long",required = false)
    private String tenantId;
}
