/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: BaseVO
 * @Author: zhang<PERSON>yi
 */
package com.gtg56.lark.common.base.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR> 2020-11-23
 */
@Data
@Accessors(chain = true)
public class SaasVO extends BaseVO implements Serializable {

    @ApiModelProperty(value = "创建人",dataType = "String",required = false)
    private String creatorId;

    @ApiModelProperty(value = "创建时间",dataType = "Date",required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改人",dataType = "String",required = false)
    private String updaterId;

    @ApiModelProperty(value = "修改时间",dataType = "Date",required = false)
    private Date updateTime;

    @ApiModelProperty(value = "删除标识",dataType = "Integer",required = false)
    private Integer deletedFlag;

    @ApiModelProperty(value = "租户id",dataType = "Long",required = false)
    private Long tenantId;

}
