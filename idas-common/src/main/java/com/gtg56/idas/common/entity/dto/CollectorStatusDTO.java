package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@ApiModel("采集器状态")
@Data
@Accessors(chain = true)
public class CollectorStatusDTO implements ImABean {
    private static final long serialVersionUID = -6447320634811644499L;
    
    @ApiModelProperty("采集器java类")
    private String collectorClass;
    @ApiModelProperty("采集器名称")
    private String collectorName;
    @ApiModelProperty("启动时间")
    private Date upTime;
    @ApiModelProperty("已启动时间")
    private String upped;
    @ApiModelProperty("主机")
    private String host;
    @ApiModelProperty("进程id")
    private Integer pid;
    @ApiModelProperty("spring激活配置")
    private String springActiveProfiles;
    @ApiModelProperty("采集轮数")
    private Long cycle;
    @ApiModelProperty("总采集条数")
    private Long totalCollect;
    @ApiModelProperty("总产出条数")
    private Long totalProduce;
    @ApiModelProperty("是否Leader，只有Leader才执行采集")
    private Boolean leader = false;
    
    @ApiModelProperty("上一轮采集条数")
    private Long currCollect;
    @ApiModelProperty("上一轮产出条数")
    private Long currProduce;
    
    public String getUpped() {
        if (upTime == null) return Double.NaN + "";
        return DateUtil.durationBeauty(System.currentTimeMillis() - upTime.getTime());
    }
}
