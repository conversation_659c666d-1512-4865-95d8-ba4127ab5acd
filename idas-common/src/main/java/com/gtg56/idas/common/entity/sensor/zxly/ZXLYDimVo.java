package com.gtg56.idas.common.entity.sensor.zxly;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
@Data
public class ZXLYDimVo implements ImABean {

    private static final long serialVersionUID = 6972928631568117796L;


    /*维度信息*/
    private Long id;
    private String wzName;
    private String ipAddr;
    private String kfsx;
    private Float wdbjsx;
    private Float wdbjxx;
    private Float wdyjsx;
    private Float wdyjxx;
    private Float sdbjsx;
    private Float sdbjxx;
    private Float sdyjsx;
    private Float sdyjxx;
    private String areaCode;
    private String deviceCode;



}
