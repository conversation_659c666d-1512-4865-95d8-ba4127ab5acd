package com.gtg56.idas.common.entity.sensor.lg;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
@Data
public class LGDimVo implements ImABean{
    private static final long serialVersionUID = 4316382943353629640L;
    private String regionCode;
    private String regionName;
    private String sensorCodeOrigion;
    private String sensorName;
    //private Short sensorType;
    private double temperatureHighLimit;
    private double temperatureLowLimit;
    private double humidityHighLimit;
    private double humidityLowLimit;
}


