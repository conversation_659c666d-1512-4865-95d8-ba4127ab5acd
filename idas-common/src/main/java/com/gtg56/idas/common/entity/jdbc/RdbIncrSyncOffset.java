package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "rdb_incr_sync_offset")
public class RdbIncrSyncOffset extends JDBCWithCreateModifyTime {
    private static final long serialVersionUID = -6204274584589408754L;
    
    @Column(name = "url")
    private String url;
    
    @Column(name = "table_name")
    private String tableName;
    
    @Column(name = "last_id")
    private Long lastId;
}
