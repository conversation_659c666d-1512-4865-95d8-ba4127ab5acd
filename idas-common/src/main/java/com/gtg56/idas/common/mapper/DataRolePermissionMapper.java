package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.core.jdbc.IMapper;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据角色权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface DataRolePermissionMapper extends IMapper<DataRolePermission> {

    int updateById(DataRolePermission entity);

    /**
     * 删除数据角色ID相关的关系
     * @param dataRoleId
     * @return
     */
    void deleteDataByRoleId(@Param("dataRoleId") String dataRoleId);
}
