package com.gtg56.idas.common.tool.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.tsdb.TSDBBaseEntity;
import com.gtg56.idas.common.entity.tsdb.query.TSDBQueryDTO;
import com.gtg56.idas.common.entity.tsdb.response.TSDBQueryResponseDTO;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

@Slf4j
public class OpenTSDBClient extends BaseClient {
    
    public OpenTSDBClient(String baseAddress) {
        super(baseAddress);
    }
    
    public void put(TSDBBaseEntity<?,?> entity,boolean exceptionOnFail) {
        String body = entity.toPutBody();
        String requestUrl = getBaseAddress() + "/api/put";
    
        try {
            HttpPost request = new HttpPost(requestUrl);
            HttpEntity httpEntity = new StringEntity(body);
            request.setEntity(httpEntity);
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode < HttpStatus.SC_OK || statusCode >= HttpStatus.SC_MULTIPLE_CHOICES) {
                InputStream content = response.getEntity().getContent();
                byte[] bytes = StreamUtil.fromStream(content);
                String s = new String(bytes);
                log.warn("put datapoint to openTSDB fail, status code : {} message \n{}\n\nbody\n{}", statusCode, s, body);
                if (exceptionOnFail) {
                    throw new RuntimeException("put datapoint to openTSDB fail , status code : "
                            + statusCode + " message " + s);
                }
            }
        } catch (IOException e) {
            log.warn("request to openTSDB fail", e);
            if (exceptionOnFail) {
                throw new RuntimeException("put datapoint to openTSDB fail", e);
            }
        }
    }
    
    public void put(TSDBBaseEntity<?,?> entity) {
        put(entity,true);
    }
    
    public List<TSDBQueryResponseDTO> query(TSDBQueryDTO query) {
        String body = query.toString();
        String requestUrl = getBaseAddress() + "/api/query";
    
        try {
            HttpPost request = new HttpPost(requestUrl);
            HttpEntity httpEntity = new StringEntity(body);
            request.setEntity(httpEntity);
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
    
            InputStream content = response.getEntity().getContent();
            byte[] bytes = StreamUtil.fromStream(content);
            String res = new String(bytes);

            if (statusCode == HttpStatus.SC_OK) {
                return JSON.parseArray(res, TSDBQueryResponseDTO.class);
            } else {
                processQueryFail(statusCode, res);
            }
        } catch (IOException e) {
            log.warn("request to openTSDB fail",e);
        }
        return Collections.emptyList();
    }
    
    private void processQueryFail(int statusCode,String content) {
        JSONObject json = JSON.parseObject(content);
        JSONObject error = json.getJSONObject("error");
        String message = error.getString("message");

        if (statusCode == HttpStatus.SC_BAD_REQUEST && message.startsWith("No such name for")) {
            log.info("query on not exists tag . message : {}", message);
        } else {
            log.warn("response code {} response message {}", statusCode, message);
        }
    }
    
}
