package com.gtg56.idas.common.util;

import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.entity.hive.DateDimension;
import com.gtg56.idas.common.entity.jdbc.Festival;
import com.gtg56.idas.common.tool.SafelyDone;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.ConcurrentSkipListSet;

public class DateUtil {
    
    private static final String[] DAY_OF_WEEK_STR = {null,"周日","周一","周二","周三","周四","周五","周六"};
    private static final Map<String,String> SOLAR_TERMS_CACHE = new ConcurrentSkipListMap<>();
    private static final Map<Integer, List<Season>> SEASON_CACHE = new ConcurrentSkipListMap<>();
    private static final Set<Integer> CACHED_SOLAR_TERMS_YEARS = new ConcurrentSkipListSet<>();
    
    /*
     * 日期格式对象不用静态final引用，是
     * 因为SimpleDateFormat非线程安全类型。
     * 所以用一次，调用一次，获取新对象。这样
     * 能绕开很多SB坑
     */
    
    /**
     * 19700101数仓日期分区格式
     * @return dateFormat
     */
    public static DateFormat ds() {
        return new SimpleDateFormat("yyyyMMdd");
    }
    
    @SneakyThrows
    public static Date parseDS(String ds) {
        return ds().parse(ds);
    }
    
    public static Calendar dateToCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }
    
    public static DateFormat ms() { return new SimpleDateFormat("yyyyMM");}
    
    public static DateFormat ys() { return new SimpleDateFormat("yyyy");}
    
    /**
     * 1970-01-01格式
     * @return dateFormat
     */
    public static DateFormat ymd() {
        return new SimpleDateFormat("yyyy-MM-dd");
    }

    public static DateFormat ymdhm() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm");
    }
    /**
     * 1970-01-01 00:00:00格式
     * @return dateFormat
     */
    public static DateFormat ymdhms() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }
    /**
     * 1970-01-01 00:00:00.000格式
     * @return dateFormat
     */
    public static DateFormat ymdhmss() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    }
    
    public static DateFormat shortYMDHMS() {
        return new SimpleDateFormat("yy/MM/dd HH:mm:ss");
    }
    
    public static DateFormat tsdb() {
        return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    }
    
    public static String durationBeauty(long durationMS) {
        if(durationMS < 1000L) {
            return durationMS + " ms";
        } else if(durationMS < 60000L) {
            return String.format("%.3f s",(durationMS / 1000d));
        } else if(durationMS < 3600000L) {
            return String.format("%.2f min",(durationMS / 60000d));
        } else if(durationMS < 86400000L) {
            return String.format("%.2f h",(durationMS / 3600000d));
        } else {
            return String.format("%.2f day",(durationMS / 86400000d));
        }
    }
    
    /**
     * 构建日期维表数据，根据节假日信息配置休假/调休
     * @param ymd ymd格式日期
     * @param festivals 节日信息
     * @return 日期维表数据
     */
    public static DateDimension buildDate(String ymd, List<Festival> festivals) {
        DateDimension dateDimension = buildDate(ymd);
        String date = dateDimension.getDate();
        
        Boolean isWork = null;
        
        for (Festival f : festivals) {
            if(date.compareTo(f.getFestivalStartDate()) >= 0 && date.compareTo(f.getFestivalEndDate()) <= 0) {
                dateDimension.setFestivalName(f.getFestivalName());
                dateDimension.setLunarFestivalName(f.getLunarFestivalName());
                dateDimension.setHoliday(CommonConsts.TRUE);
                dateDimension.setWorkday(CommonConsts.FALSE);
                isWork = false;
                break;
            }
        }
        if(isWork == null) {
//            dateDimension.setFestivalName("");
//            dateDimension.setLunarFestivalName("");
            dateDimension.setHoliday(CommonConsts.FALSE);
    
            for (Festival f : festivals) {
                if(f.getWorkdayList().contains(date)) {
                    isWork = true;
                    break;
                }
            }
            if(isWork != null) {
                dateDimension.setWorkday(CommonConsts.TRUE);
            } else {
                if(CommonConsts.TRUE.equals(dateDimension.getWeekend())) {
                    dateDimension.setWorkday(CommonConsts.FALSE);
                } else {
                    dateDimension.setWorkday(CommonConsts.TRUE);
                }
            }
        }
        return dateDimension;
    }
    
    /**
     * 构建日期维表数据，不考虑节假日。
     * @param ymd ymd格式日期
     * @return 日期维表数据
     */
    @SneakyThrows
    public static DateDimension buildDate(String ymd) {
        DateFormat dateFormat = ymd();
        Date solarDate = dateFormat.parse(ymd);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(solarDate);
    
        DateDimension dateDimension = new DateDimension();
    
        int solarYear = calendar.get(Calendar.YEAR);
    
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        Calendar weekStart = dayAdd(solarDate,-1*dayOfWeek +1);
        Calendar weekEnd = dayAdd(solarDate,7-dayOfWeek);
        
        boolean isLeap = isLeapYear(solarYear);
    
        Quarter quarter = Quarter.getQuarter(solarDate);
    
        // 处理公历部分
        dateDimension.setDate(dateFormat.format(solarDate))
                .setYear(solarYear + "")
                .setMonthOfYear(calendar.get(Calendar.MONTH) + 1 + "")
                .setWeekOfYear(calendar.get(Calendar.WEEK_OF_YEAR) + "")
                .setDayOfWeekNumber(dayOfWeek + "")
                .setDayOfWeekStr(DAY_OF_WEEK_STR[dayOfWeek])
                .setWeekStartDate(dateFormat.format(weekStart.getTime()))
                .setWeekStartYear(weekStart.get(Calendar.YEAR) + "")
                .setWeekEndDate(dateFormat.format(weekEnd.getTime()))
                .setWeekEndYear(weekEnd.get(Calendar.YEAR) + "")
                .setDayOfMonth(calendar.get(Calendar.DAY_OF_MONTH) + "")
                .setDayOfYear(calendar.get(Calendar.DAY_OF_YEAR) + "")
                .setWeekend(dayOfWeek == 1 || dayOfWeek == 7 ? CommonConsts.TRUE : CommonConsts.FALSE)
                .setWorkday(dayOfWeek == 1 || dayOfWeek == 7 ? CommonConsts.FALSE : CommonConsts.TRUE)
                .setHoliday(CommonConsts.FALSE)
                .setLeapYear(isLeap ? CommonConsts.TRUE : CommonConsts.FALSE)
                .setDaysOfYearCount(isLeap ? "366" : "365")
                .setSolarTerms(getSolarTerms(solarDate))
                .setQuarterOfYear(quarter.getQuarter() + "")
                .setQuarterStartDate(dateFormat.format(quarter.getStart()))
                .setQuarterEndDate(dateFormat.format(quarter.getEnd()))
                .setDaysOfQuarterCount(quarter.days() + "")
                .setDayOfQuarter(quarter.dayOfQuarter(solarDate) + "");
        
        // 处理农历部分 只有在支持年份内的 才能处理
        if(solarYear >= LUNAR_SUPPORT_MIN_YEAR && solarYear <= LUNAR_SUPPORT_MAX_YEAR) {
    
            int[] lunar = solarToLunar(solarYear, calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
            int lunarYear = lunar[0];
            int lunarMonth = lunar[1];
            int lunarDay = lunar[2];
            boolean lunarLeapMonth = lunar[3]==1;
            
            Season season = Season.getSeason(solarDate);
    
            dateDimension
                    .setLunarYear(lunarYear + "")
                    .setLunarMonth(lunarMonth + "")
                    .setLunarLeapMonth(lunarLeapMonth ? CommonConsts.TRUE : CommonConsts.FALSE)
                    .setLunarDay(lunarDay + "")
                    .setLunarGan(lunarYearToTianGan(lunarYear))
                    .setLunarZhi(lunarYearToDiZhi(lunarYear))
                    .setZodiacSign(lunarYearToZodiacSign(lunarYear))
                    .setSeason(season.getName())
                    .setDayOfSeason(season.dayInSeason(solarDate) + "")
                    .setDaysOfSeasonCount(season.days() + "")
                    .setSeasonStartDate(dateFormat.format(season.getStart()))
                    .setSeasonEndDate(dateFormat.format(season.getEnd()))
                    .setSeasonStartYear(season.getStartSolarYear() + "")
                    .setSeasonEndYear(season.getEndSolarYear() + "");
        }
    
        
        return dateDimension;
    }
    
    /**
     * 判断是否闰年
     * @param solarYear 公历年
     * @return 是否闰年
     */
    public static boolean isLeapYear(int solarYear) {
        return  (solarYear % 4 == 0 && solarYear % 100 != 0 || solarYear % 400 == 0);
    }
    
    /**
     * 日期计算
     * @param source 基线日期
     * @param add 日期增量
     * @return 增加后的calendar对象
     */
    public static Calendar dayAdd(Date source, int add) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(source);
        calendar.add(Calendar.DAY_OF_YEAR,add);
        return calendar;
    }
    
    /**
     * 日期计算并返回格式化后string
     * @param source 基线日期
     * @param add 日期增量
     * @param format 日期格式
     * @return 增加后并格式化的日期string
     */
    public static String formattedDayAdd(Date source,int add,DateFormat format) {
        return format.format(dayAdd(source,add));
    }
    
    /**
     * 日期差
     * 不含基线日期。例如2020-01-01到2020-01-02，返回1。如果想知道两者间共多少天，需要手动+1
     * @param d1 基线日期
     * @param d2 对比日期
     * @return 日期差
     */
    public static int dayDiff(Date d1,Date d2) {
        LocalDateTime ldt1 = LocalDateTime.ofInstant(d1.toInstant(), TimeZone.getDefault().toZoneId());
        LocalDateTime ldt2 = LocalDateTime.ofInstant(d2.toInstant(), TimeZone.getDefault().toZoneId());
        return (int) ldt1.until(ldt2, ChronoUnit.DAYS);
    }
    
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        return Date.from(zdt.toInstant());
    }
    
    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDate();
    }
    
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }
    
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }

    /**
     * 返回两个时间间的列表
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> getDateListBetween(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();

        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_DATE);
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ISO_DATE);

        while (!start.isAfter(end)) {
            dateList.add(start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            start = start.plusDays(1);
        }

        return dateList;
    }

    public static void main(String[] args) {
        String startDate = "2022-01-01";
        String endDate = "2022-01-10";

        List<String> dateList = getDateListBetween(startDate, endDate);

        for (String date : dateList) {
            System.out.println(date);
        }
    }
    
    /**
     * 季度类
     */
    @Getter
    @Setter(AccessLevel.PRIVATE)
    @Accessors(chain = true)
    static class Quarter {
        private int year;
        private int m1,m2,m3;
        private Date start,end;
        private int quarter;
        
        private Quarter(int year,int quarter) {
            this.year = year;
            this.quarter = quarter;
            Calendar c = Calendar.getInstance();
            c.set(Calendar.YEAR,year);
            switch (quarter) {
                case 1: {
                    m1 = Calendar.JANUARY;m2 = Calendar.FEBRUARY;m3 = Calendar.MARCH;
                    c.set(Calendar.MONTH,m1);
                    c.set(Calendar.DAY_OF_MONTH,1);
                    start = c.getTime();
                    c.set(Calendar.MONTH,m3);
                    c.set(Calendar.DAY_OF_MONTH,31);
                    end = c.getTime();
                    break;
                }
                case 2: {
                    m1 = Calendar.APRIL;m2 = Calendar.MAY;m3 = Calendar.JUNE;
                    c.set(Calendar.MONTH,Calendar.APRIL);
                    c.set(Calendar.DAY_OF_MONTH,1);
                    start = c.getTime();
                    c.set(Calendar.MONTH,Calendar.JUNE);
                    c.set(Calendar.DAY_OF_MONTH,30);
                    end = c.getTime();
                    break;
                }
                case 3: {
                    m1 = Calendar.JULY;m2 = Calendar.AUGUST;m3 = Calendar.SEPTEMBER;
                    c.set(Calendar.MONTH,Calendar.JULY);
                    c.set(Calendar.DAY_OF_MONTH,1);
                    start = c.getTime();
                    c.set(Calendar.MONTH,Calendar.SEPTEMBER);
                    c.set(Calendar.DAY_OF_MONTH,30);
                    end = c.getTime();
                    break;
                }
                case 4: {
                    m1 = Calendar.OCTOBER;m2 = Calendar.NOVEMBER;m3 = Calendar.DECEMBER;
                    c.set(Calendar.MONTH,Calendar.OCTOBER);
                    c.set(Calendar.DAY_OF_MONTH,1);
                    start = c.getTime();
                    c.set(Calendar.MONTH,Calendar.DECEMBER);
                    c.set(Calendar.DAY_OF_MONTH,31);
                    end = c.getTime();
                    break;
                }
                default:
                    throw new IllegalStateException("Unexpected quarter " + quarter);
            }
        }
        
        public int days() {
            return dayDiff(start,end) + 1;
        }
        
        public int dayOfQuarter(Date date) {
            return dayDiff(start,date) + 1;
        }
    
        public static Quarter getQuarter(Date date) {
            int quarter;
        
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            int month = c.get(Calendar.MONTH);
            int year = c.get(Calendar.YEAR);
            switch (month) {
                case Calendar.JANUARY: case Calendar.FEBRUARY: case Calendar.MARCH:
                    quarter = 1;
                    break;
                case Calendar.APRIL: case Calendar.MAY: case Calendar.JUNE:
                    quarter = 2;
                    break;
                case Calendar.JULY: case Calendar.AUGUST: case Calendar.SEPTEMBER:
                    quarter = 3;
                    break;
                case Calendar.OCTOBER: case Calendar.NOVEMBER: case Calendar.DECEMBER:
                    quarter = 4;
                    break;
                default:
                    throw new IllegalArgumentException("no fit quarter for date " + date);
            }
            return new Quarter(year,quarter);
        }
    }
    
    /**
     * 支持转换的最小农历年份
     */
    private static final int LUNAR_SUPPORT_MIN_YEAR = 1900;
    /**
     * 支持转换的最大农历年份
     */
    private static final int LUNAR_SUPPORT_MAX_YEAR = 2099;
    
    /**
     * 公历每月前的天数
     */
    private static final int[] DAYS_BEFORE_MONTH = { 0, 31, 59, 90, 120, 151, 181,
            212, 243, 273, 304, 334, 365 };
    
    /**
     * 用来表示1900年到2099年间农历年份的相关信息，共24位bit的16进制表示，其中：
     * 1. 前4位表示该年闰哪个月；
     * 2. 5-17位表示农历年份13个月的大小月分布，0表示小，1表示大；
     * 3. 最后7位表示农历年首（正月初一）对应的公历日期。
     *
     * 以2014年的数据0x955ABF为例说明：
     *                 1001 0101 0101 1010 1011 1111
     *                 闰九月                                  农历正月初一对应公历1月31号
     */
    private static final int[] LUNAR_INFO = {
            0x84B6BF,/*1900*/
            0x04AE53,0x0A5748,0x5526BD,0x0D2650,0x0D9544,0x46AAB9,0x056A4D,0x09AD42,0x24AEB6,0x04AE4A,/*1901-1910*/
            0x6A4DBE,0x0A4D52,0x0D2546,0x5D52BA,0x0B544E,0x0D6A43,0x296D37,0x095B4B,0x749BC1,0x049754,/*1911-1920*/
            0x0A4B48,0x5B25BC,0x06A550,0x06D445,0x4ADAB8,0x02B64D,0x095742,0x2497B7,0x04974A,0x664B3E,/*1921-1930*/
            0x0D4A51,0x0EA546,0x56D4BA,0x05AD4E,0x02B644,0x393738,0x092E4B,0x7C96BF,0x0C9553,0x0D4A48,/*1931-1940*/
            0x6DA53B,0x0B554F,0x056A45,0x4AADB9,0x025D4D,0x092D42,0x2C95B6,0x0A954A,0x7B4ABD,0x06CA51,/*1941-1950*/
            0x0B5546,0x555ABB,0x04DA4E,0x0A5B43,0x352BB8,0x052B4C,0x8A953F,0x0E9552,0x06AA48,0x6AD53C,/*1951-1960*/
            0x0AB54F,0x04B645,0x4A5739,0x0A574D,0x052642,0x3E9335,0x0D9549,0x75AABE,0x056A51,0x096D46,/*1961-1970*/
            0x54AEBB,0x04AD4F,0x0A4D43,0x4D26B7,0x0D254B,0x8D52BF,0x0B5452,0x0B6A47,0x696D3C,0x095B50,/*1971-1980*/
            0x049B45,0x4A4BB9,0x0A4B4D,0xAB25C2,0x06A554,0x06D449,0x6ADA3D,0x0AB651,0x095746,0x5497BB,/*1981-1990*/
            0x04974F,0x064B44,0x36A537,0x0EA54A,0x86B2BF,0x05AC53,0x0AB647,0x5936BC,0x092E50,0x0C9645,/*1991-2000*/
            0x4D4AB8,0x0D4A4C,0x0DA541,0x25AAB6,0x056A49,0x7AADBD,0x025D52,0x092D47,0x5C95BA,0x0A954E,/*2001-2010*/
            0x0B4A43,0x4B5537,0x0AD54A,0x955ABF,0x04BA53,0x0A5B48,0x652BBC,0x052B50,0x0A9345,0x474AB9,/*2011-2020*/
            0x06AA4C,0x0AD541,0x24DAB6,0x04B64A,0x6a573D,0x0A4E51,0x0D2646,0x5E933A,0x0D534D,0x05AA43,/*2021-2030*/
            0x36B537,0x096D4B,0xB4AEBF,0x04AD53,0x0A4D48,0x6D25BC,0x0D254F,0x0D5244,0x5DAA38,0x0B5A4C,/*2031-2040*/
            0x056D41,0x24ADB6,0x049B4A,0x7A4BBE,0x0A4B51,0x0AA546,0x5B52BA,0x06D24E,0x0ADA42,0x355B37,/*2041-2050*/
            0x09374B,0x8497C1,0x049753,0x064B48,0x66A53C,0x0EA54F,0x06AA44,0x4AB638,0x0AAE4C,0x092E42,/*2051-2060*/
            0x3C9735,0x0C9649,0x7D4ABD,0x0D4A51,0x0DA545,0x55AABA,0x056A4E,0x0A6D43,0x452EB7,0x052D4B,/*2061-2070*/
            0x8A95BF,0x0A9553,0x0B4A47,0x6B553B,0x0AD54F,0x055A45,0x4A5D38,0x0A5B4C,0x052B42,0x3A93B6,/*2071-2080*/
            0x069349,0x7729BD,0x06AA51,0x0AD546,0x54DABA,0x04B64E,0x0A5743,0x452738,0x0D264A,0x8E933E,/*2081-2090*/
            0x0D5252,0x0DAA47,0x66B53B,0x056D4F,0x04AE45,0x4A4EB9,0x0A4D4C,0x0D1541,0x2D92B5          /*2091-2099*/
        
        
    };
    
    /**
     * 将农历日期转换为公历日期
     * @param year                        农历年份
     * @param month                        农历月
     * @param monthDay                农历日
     * @param isLeapMonth        该月是否是闰月
     * [url=home.php?mod=space&uid=7300]@return[/url] 返回农历日期对应的公历日期，year0, month1, day2.
     */
    private static int[] lunarToSolar(int year, int month, int monthDay,
                                           boolean isLeapMonth) {
        int dayOffset;
        int leapMonth;
        int i;
        
        if (year < LUNAR_SUPPORT_MIN_YEAR || year > LUNAR_SUPPORT_MAX_YEAR || month < 1 || month > 12
                || monthDay < 1 || monthDay > 30) {
            throw new IllegalArgumentException(
                    "Illegal lunar date, must be like that:\n\t" +
                            "year : 1900~2099\n\t" +
                            "month : 1~12\n\t" +
                            "day : 1~30");
        }
        
        dayOffset = (LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & 0x001F) - 1;
        
        if (((LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & 0x0060) >> 5) == 2)
            dayOffset += 31;
        
        for (i = 1; i < month; i++) {
            if ((LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & (0x80000 >> (i - 1))) == 0)
                dayOffset += 29;
            else
                dayOffset += 30;
        }
        
        dayOffset += monthDay;
        leapMonth = (LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & 0xf00000) >> 20;
        
        // 这一年有闰月
        if (leapMonth != 0) {
            if (month > leapMonth || (month == leapMonth && isLeapMonth)) {
                if ((LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & (0x80000 >> (month - 1))) == 0)
                    dayOffset += 29;
                else
                    dayOffset += 30;
            }
        }
        
        if (dayOffset > 366 || (year % 4 != 0 && dayOffset > 365)) {
            year += 1;
            if (year % 4 == 1) {
                dayOffset -= 366;
            } else {
                dayOffset -= 365;
            }
        }
        
        int[] solarInfo = new int[3];
        for (i = 1; i < 13; i++) {
            int iPos = DAYS_BEFORE_MONTH[i];
            if (year % 4 == 0 && i > 2) {
                iPos += 1;
            }
            
            if (year % 4 == 0 && i == 2 && iPos + 1 == dayOffset) {
                solarInfo[1] = i;
                solarInfo[2] = dayOffset - 31;
                break;
            }
            
            if (iPos >= dayOffset) {
                solarInfo[1] = i;
                iPos = DAYS_BEFORE_MONTH[i - 1];
                if (year % 4 == 0 && i > 2) {
                    iPos += 1;
                }
                if (dayOffset > iPos)
                    solarInfo[2] = dayOffset - iPos;
                else if (dayOffset == iPos) {
//                    if (year % 4 == 0 && i == 2) {
//                        solarInfo[2] = DAYS_BEFORE_MONTH[i] - DAYS_BEFORE_MONTH[i - 1] + 1;
//                    } else {
//                        solarInfo[2] = DAYS_BEFORE_MONTH[i] - DAYS_BEFORE_MONTH[i - 1];
//                    }
                    solarInfo[2] = DAYS_BEFORE_MONTH[i] - DAYS_BEFORE_MONTH[i - 1];
                } else {
                    solarInfo[2] = dayOffset;
                }
                break;
            }
        }
        solarInfo[0] = year;
        
        return solarInfo;
    }
    
    /**
     * 将公历日期转换为农历日期，且标识是否是闰月
     * @param year 公历年份
     * @param month 公历月份
     * @param monthDay 公历日
     * @return 返回公历日期对应的农历日期，year0，month1，day2，leap3
     */
    private static int[] solarToLunar(int year, int month, int monthDay) {
        int[] lunarDate = new int[4];
        Date baseDate = new GregorianCalendar(1900, Calendar.JANUARY, 31).getTime();
        @SuppressWarnings("MagicConstant")
        Date objDate = new GregorianCalendar(year, month - 1, monthDay).getTime();
        int offset = (int) ((objDate.getTime() - baseDate.getTime()) / 86400000L);
        
        // 用offset减去每农历年的天数计算当天是农历第几天
        // lunarYear最终结果是农历的年份, offset是当年的第几天
        int lunarYear, daysOfYear = 0;
        for (lunarYear = LUNAR_SUPPORT_MIN_YEAR; lunarYear <= LUNAR_SUPPORT_MAX_YEAR && offset > 0; lunarYear++) {
            daysOfYear = daysInLunarYear(lunarYear);
            offset -= daysOfYear;
        }
        if (offset < 0) {
            offset += daysOfYear;
            lunarYear--;
        }
        
        // 农历年份
        lunarDate[0] = lunarYear;
        
        int leapMonth = leapMonth(lunarYear); // 闰哪个月,1-12
        boolean isLeap = false;
        // 用当年的天数offset,逐个减去每月（农历）的天数，求出当天是本月的第几天
        int lunarMonth, daysOfMonth = 0;
        for (lunarMonth = 1; lunarMonth <= 13 && offset > 0; lunarMonth++) {
            daysOfMonth = daysInLunarMonth(lunarYear, lunarMonth);
            offset -= daysOfMonth;
        }
        // 当前月超过闰月，要校正
        if (leapMonth != 0 && lunarMonth > leapMonth) {
            --lunarMonth;
            
            if (lunarMonth == leapMonth) {
                isLeap = true;
            }
        }
        // offset小于0时，也要校正
        if (offset < 0) {
            offset += daysOfMonth;
            --lunarMonth;
        }
        
        lunarDate[1] = lunarMonth;
        lunarDate[2] = offset + 1;
        lunarDate[3] = isLeap ? 1 : 0;
        
        return lunarDate;
    }
    
    /**
     * 传回农历year年month月的总天数
     * @param year 要计算的年份
     * @param month        要计算的月
     * @return 传回天数
     */
    private static int daysInMonth(int year, int month) {
        return daysInMonth(year, month,false);
    }
    
    /**
     * 传回农历year年month月的总天数
     * @param year 要计算的年份
     * @param month        要计算的月
     * @param leap 当月是否是闰月
     * @return 传回天数，如果闰月是错误的，返回0.
     */
    private static int daysInMonth(int year, int month, boolean leap) {
        int leapMonth = leapMonth(year);
        int offset = 0;
        
        // 如果本年有闰月且month大于闰月时，需要校正
        if (leapMonth != 0 && month > leapMonth) {
            offset = 1;
        }
        
        // 不考虑闰月
        if (!leap) {
            return daysInLunarMonth(year, month + offset);
        } else {
            // 传入的闰月是正确的月份
            if (leapMonth != 0 && leapMonth == month) {
                return daysInLunarMonth(year, month + 1);
            }
        }
        
        return 0;
    }
    
    /**
     * 传回农历 year年的总天数
     * @param year 将要计算的年份
     * @return 返回传入年份的总天数
     */
    private static int daysInLunarYear(int year) {
        int i, sum = 348;
        if (leapMonth(year) != 0) {
            sum = 377;
        }
        int monthInfo = LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & 0x0FFF80;
        for (i = 0x80000; i > 0x7; i >>= 1) {
            if ((monthInfo & i) != 0)
                sum += 1;
        }
        return sum;
    }
    
    
    /**
     * 传回农历 year年month月的总天数，总共有13个月包括闰月
     * @param year  将要计算的年份
     * @param month 将要计算的月份
     * @return 传回农历 year年month月的总天数
     */
    private static int daysInLunarMonth(int year, int month) {
        if ((LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & (0x100000 >> month)) == 0)
            return 29;
        else
            return 30;
    }
    
    
    /**
     * 传回农历 year年闰哪个月 1-12 , 没闰传回 0
     * @param year 将要计算的年份
     * @return 传回农历 year年闰哪个月1-12, 没闰传回 0
     */
    private static int leapMonth(int year) {
        return (LUNAR_INFO[year - LUNAR_SUPPORT_MIN_YEAR] & 0xF00000) >> 20;
    }
    
    private static final String[]
            LUNAR_TIAN_GAN = {"甲","乙","丙","丁","戊","己","庚","辛","壬","癸"},
            LUNAR_DI_ZHI = {"子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"},
            ZODIAC_SIGN = {"鼠","牛","虎","兔","龙","蛇","马","羊","申","鸡","狗","猪"};
    
    public static String lunarYearToTianGan(int lunarYear){
        return LUNAR_TIAN_GAN[(lunarYear-4) % 10];
    }
    
    public static String lunarYearToDiZhi(int lunarYear) {
        return LUNAR_DI_ZHI[(lunarYear-4) % 12];
    }
    
    public static String lunarYearToZodiacSign(int lunarYear) {
        return ZODIAC_SIGN[(lunarYear-4) % 12];
    }
    
    /**
     * 处理节气、季节部分
     */
    private static final  double D = 0.2422;
    private final static Map<String,Integer[]> INCREASE_OFFSET_MAP = new HashMap<>();//+1偏移
    private final static Map<String,Integer[]> DECREASE_OFFSET_MAP = new HashMap<>();//-1偏移
    
    /**24节气**/
    private enum SolarTermsEnum {
        LICHUN("立春"),//--立春
        YUSHUI("雨水"),//--雨水
        JINGZHE("惊蛰"),//--惊蛰
        CHUNFEN("春分"),//春分
        QINGMING("清明"),//清明
        GUYU("谷雨"),//谷雨
        LIXIA("立夏"),//立夏
        XIAOMAN("小满"),//小满
        MANGZHONG("芒种"),//芒种
        XIAZHI("夏至"),//夏至
        XIAOSHU("小暑"),//小暑
        DASHU("大暑"),//大暑
        LIQIU("立秋"),//立秋
        CHUSHU("处暑"),//处暑
        BAILU("白露"),//白露
        QIUFEN("秋分"),//秋分
        HANLU("寒露"),//寒露
        SHUANGJIANG("霜降"),//霜降
        LIDONG("立冬"),//立冬
        XIAOXUE("小雪"),//小雪
        DAXUE("大雪"),//大雪
        DONGZHI("冬至"),//冬至
        XIAOHAN("小寒"),//小寒
        DAHAN("大寒");//大寒
    
        private String chn;
        SolarTermsEnum(String chn) {this.chn = chn;}
    }
    
    static {
        DECREASE_OFFSET_MAP.put(SolarTermsEnum.YUSHUI.name(), new Integer[]{2026});//雨水
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.CHUNFEN.name(), new Integer[]{2084});//春分
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.XIAOMAN.name(), new Integer[]{2008});//小满
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.MANGZHONG.name(), new Integer[]{1902});//芒种
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.XIAZHI.name(), new Integer[]{1928});//夏至
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.XIAOSHU.name(), new Integer[]{1925,2016});//小暑
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.DASHU.name(), new Integer[]{1922});//大暑
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.LIQIU.name(), new Integer[]{2002});//立秋
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.BAILU.name(), new Integer[]{1927});//白露
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.QIUFEN.name(), new Integer[]{1942});//秋分
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.SHUANGJIANG.name(), new Integer[]{2089});//霜降
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.LIDONG.name(), new Integer[]{2089});//立冬
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.XIAOXUE.name(), new Integer[]{1978});//小雪
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.DAXUE.name(), new Integer[]{1954});//大雪
        DECREASE_OFFSET_MAP.put(SolarTermsEnum.DONGZHI.name(), new Integer[]{1918,2021});//冬至
        
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.XIAOHAN.name(), new Integer[]{1982});//小寒
        DECREASE_OFFSET_MAP.put(SolarTermsEnum.XIAOHAN.name(), new Integer[]{2019});//小寒
        
        INCREASE_OFFSET_MAP.put(SolarTermsEnum.DAHAN.name(), new Integer[]{2082});//大寒
    }
    
    //定义一个二维数组，第一维数组存储的是20世纪的节气C值，第二维数组存储的是21世纪的节气C值,0到23个，依次代表立春、雨水...大寒节气的C值
    private static final double[][] CENTURY_ARRAY =
            {{4.6295,19.4599,6.3826,21.4155,5.59,20.888,6.318,21.86,6.5,22.2,7.928,23.65,8.35,
                    23.95,8.44,23.822,9.098,24.218,8.218,23.08,7.9,22.6,6.11,20.84}
                    ,{3.87,18.73,5.63,20.646,4.81,20.1,5.52,21.04,5.678,21.37,7.108,22.83,
                    7.5,23.13,7.646,23.042,8.318,23.438,7.438,22.36,7.18,21.94,5.4055,20.12}};
    
    /**
     *
     * @param year 年份
     * @param solarTerms 节气
     * @return 返回节气是相应月份的第几天
     */
    private static int getSolarTermNum(int year,SolarTermsEnum solarTerms){
        
        double centuryValue;//节气的世纪值，每个节气的每个世纪值都不同
        
        int ordinal = solarTerms.ordinal();
        
        int centuryIndex;
        if(year>=1901 && year<=2000){//20世纪
            centuryIndex = 0;
        } else if(year>=2001 && year <= 2100){//21世纪
            centuryIndex = 1;
        } else {
            throw new RuntimeException("不支持此年份："+year+"，目前只支持1901年到2100年的时间范围");
        }
        centuryValue = CENTURY_ARRAY[centuryIndex][ordinal];
        int dateNum;
        /*
          计算 num =[Y*D+C]-L这是传说中的寿星通用公式
          公式解读：年数的后2位乘0.2422加C(即：centuryValue)取整数后，减闰年数
         */
        int y = year%100;//步骤1:取年分的后两位数
        if(isLeapYear(year)){ //闰年
            if(ordinal == SolarTermsEnum.XIAOHAN.ordinal() || ordinal == SolarTermsEnum.DAHAN.ordinal()
                    || ordinal == SolarTermsEnum.LICHUN.ordinal() || ordinal == SolarTermsEnum.YUSHUI.ordinal()){
                //注意：凡闰年3月1日前闰年数要减一，即：L=[(Y-1)/4],因为小寒、大寒、立春、雨水这两个节气都小于3月1日,所以 y = y-1
                y = y-1;//步骤2
            }
        }
        dateNum = (int)(y*D+centuryValue)- (y/4);//步骤3，使用公式[Y*D+C]-L计算
        dateNum += specialYearOffset(year,solarTerms);//步骤4，加上特殊的年分的节气偏移量
        return dateNum;
    }
    
    /**
     * 特例,特殊的年分的节气偏移量,由于公式并不完善，所以算出的个别节气的第几天数并不准确，在此返回其偏移量
     * @param year 年份
     * @param solarTerms 节气
     * @return 返回其偏移量
     */
    private static int specialYearOffset(int year,SolarTermsEnum solarTerms) {
        int offset = 0;
        offset += getOffset(DECREASE_OFFSET_MAP,year,solarTerms.name(),-1);
        offset += getOffset(INCREASE_OFFSET_MAP,year,solarTerms.name(),1);
        
        return offset;
    }
    
    private static int getOffset(Map<String,Integer[]> map,int year,String name,int offset){
        int off = 0;
        Integer[] years = map.get(name);
        if(null != years){
            for(int i:years){
                if(i == year){
                    off = offset;
                    break;
                }
            }
        }
        return off;
    }
    
    @SneakyThrows
    private static void cacheYearSolarTerms(int year) {
        if(CACHED_SOLAR_TERMS_YEARS.contains(year)) return;
        SOLAR_TERMS_CACHE.put(String.format("%d-02-%02d",year,getSolarTermNum(year,SolarTermsEnum.LICHUN)),SolarTermsEnum.LICHUN.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-02-%02d",year,getSolarTermNum(year,SolarTermsEnum.YUSHUI)),SolarTermsEnum.YUSHUI.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-03-%02d",year,getSolarTermNum(year,SolarTermsEnum.JINGZHE)),SolarTermsEnum.JINGZHE.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-03-%02d",year,getSolarTermNum(year,SolarTermsEnum.CHUNFEN)),SolarTermsEnum.CHUNFEN.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-04-%02d",year,getSolarTermNum(year,SolarTermsEnum.QINGMING)),SolarTermsEnum.QINGMING.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-04-%02d",year,getSolarTermNum(year,SolarTermsEnum.GUYU)),SolarTermsEnum.GUYU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-05-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIXIA)),SolarTermsEnum.LIXIA.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-05-%02d",year,getSolarTermNum(year,SolarTermsEnum.XIAOMAN)),SolarTermsEnum.XIAOMAN.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-06-%02d",year,getSolarTermNum(year,SolarTermsEnum.MANGZHONG)),SolarTermsEnum.MANGZHONG.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-06-%02d",year,getSolarTermNum(year,SolarTermsEnum.XIAZHI)),SolarTermsEnum.XIAZHI.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-07-%02d",year,getSolarTermNum(year,SolarTermsEnum.XIAOSHU)),SolarTermsEnum.XIAOSHU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-07-%02d",year,getSolarTermNum(year,SolarTermsEnum.DASHU)),SolarTermsEnum.DASHU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-08-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIQIU)),SolarTermsEnum.LIQIU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-08-%02d",year,getSolarTermNum(year,SolarTermsEnum.CHUSHU)),SolarTermsEnum.CHUSHU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-09-%02d",year,getSolarTermNum(year,SolarTermsEnum.BAILU)),SolarTermsEnum.BAILU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-09-%02d",year,getSolarTermNum(year,SolarTermsEnum.QIUFEN)),SolarTermsEnum.QIUFEN.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-10-%02d",year,getSolarTermNum(year,SolarTermsEnum.HANLU)),SolarTermsEnum.HANLU.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-10-%02d",year,getSolarTermNum(year,SolarTermsEnum.SHUANGJIANG)),SolarTermsEnum.SHUANGJIANG.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-11-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIDONG)),SolarTermsEnum.LIDONG.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-11-%02d",year,getSolarTermNum(year,SolarTermsEnum.XIAOXUE)),SolarTermsEnum.XIAOXUE.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-12-%02d",year,getSolarTermNum(year,SolarTermsEnum.DAXUE)),SolarTermsEnum.DAXUE.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-12-%02d",year,getSolarTermNum(year,SolarTermsEnum.DONGZHI)),SolarTermsEnum.DONGZHI.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-01-%02d",year+1,getSolarTermNum(year,SolarTermsEnum.XIAOHAN)),SolarTermsEnum.XIAOHAN.chn);
        SOLAR_TERMS_CACHE.put(String.format("%d-01-%02d",year+1,getSolarTermNum(year,SolarTermsEnum.DAHAN)),SolarTermsEnum.DAHAN.chn);
        
        
        DateFormat dateFormat = ymd();
        Date
                lichun = dateFormat.parse(String.format("%d-02-%02d",year,getSolarTermNum(year,SolarTermsEnum.LICHUN))),
                lixia = dateFormat.parse(String.format("%d-05-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIXIA))),
                liqiu = dateFormat.parse(String.format("%d-08-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIQIU))),
                lidong = dateFormat.parse(String.format("%d-11-%02d",year,getSolarTermNum(year,SolarTermsEnum.LIDONG))),
                lichunNextYear = dateFormat.parse(String.format("%d-02-%02d",year+1,getSolarTermNum(year+1,SolarTermsEnum.LICHUN)));
    
    
        Season spring = new Season().setName("春").setStart(lichun).setEnd(dayAdd(lixia,-1).getTime()),
                summer = new Season().setName("夏").setStart(lixia).setEnd(dayAdd(liqiu,-1).getTime()),
                autumn = new Season().setName("秋").setStart(liqiu).setEnd(dayAdd(lidong,-1).getTime()),
                winter = new Season().setName("冬").setStart(lidong).setEnd(dayAdd(lichunNextYear,-1).getTime());
        SEASON_CACHE.put(year,Arrays.asList(spring,summer,autumn,winter));
        
        CACHED_SOLAR_TERMS_YEARS.add(year);
    }
    
    /**
     * 获取节气
     * 会缓存前后共3年的节气和季节数据
     * @param date 日期
     * @return 如当日有节气则返回。无则null
     */
    public static String getSolarTerms(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int solarYear = calendar.get(Calendar.YEAR);
        // +-1有可能越过支持界，所以safelyDone
        SafelyDone.doIt(() -> cacheYearSolarTerms(solarYear-1));
        SafelyDone.doIt(() -> cacheYearSolarTerms(solarYear));
        SafelyDone.doIt(() -> cacheYearSolarTerms(solarYear+1));
        
        return SOLAR_TERMS_CACHE.get(ymd().format(date));
    }

    /**
     * 格式化日期：返回1970-01-01格式
     * @param aDate
     * @return
     */
    public static final String formatToDateStr(Date aDate) {
        return  ymd().format(aDate);
    }

    /**
     * 格式化日期：返回1970-01-01格式
     * @return
     */
    @SneakyThrows
    public static final Date yestoday() {

        Date date = DateUtil.ymd().parse(DateUtil.ymd().format(new Date()));
        ;
        return  DateUtil.dayAdd(date,-1).getTime();
    }


    /**
     * 格式化日期：返回1970-01-01格式
     * @return
     */
    @SneakyThrows
    public static final Date today() {

        Date date = DateUtil.ymd().parse(DateUtil.ymd().format(new Date()));
        ;
        return  date;
    }

    /**
     * 季节类
     */
    @Getter
    @Setter(AccessLevel.PRIVATE)
    @Accessors(chain = true)
    static class Season {
        private String name;
        private Date start,end;
        
        private Season() {}
        
        public int days() {
            return dayDiff(start,end) + 1;
        }
        
        public int dayInSeason(Date date) {
            return dayDiff(start,date) + 1;
        }
        
        public int getStartSolarYear() {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(start);
            return calendar.get(Calendar.YEAR);
        }
    
        public int getEndSolarYear() {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(end);
            return calendar.get(Calendar.YEAR);
        }
        
        public static Season getSeason(Date date) {
            getSolarTerms(date);// 生成节气，同时也会生成季节
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(Calendar.YEAR);
            List<Season> seasons = new ArrayList<>(SEASON_CACHE.getOrDefault(year,Collections.emptyList()));
            // 年头的可能是去年的冬季，所以要加上去年的季节信息
            seasons.addAll(SEASON_CACHE.getOrDefault(year-1,Collections.emptyList()));
            
            Optional<Season> first = seasons.stream()
                    .filter(s -> (s.start.before(date) || s.start.equals(date))
                            && (s.end.after(date) || s.end.equals(date)))
                    .findFirst();
            
            if(first.isPresent()) {
                return first.get();
            } else {
                throw new IllegalArgumentException("no fit season for date " + ymd().format(date));
            }
        }
    }
}
