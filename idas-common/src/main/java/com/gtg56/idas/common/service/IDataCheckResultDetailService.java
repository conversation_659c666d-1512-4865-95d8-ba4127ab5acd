package com.gtg56.idas.common.service;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.core.jdbc.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
/**
 * <p>
 * 数据完整性检测结果明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IDataCheckResultDetailService extends IService<DataCheckResultDetail> {

    /**
     *
     * @param dataCheckResultDetail
     * @return
     */
    public DataCheckResultDetail saveData(DataCheckResultDetail dataCheckResultDetail);

    /**
     *
     * @param dataCheckResultDetail
     */
    public DataCheckResultDetail updateData(DataCheckResultDetail dataCheckResultDetail);


    /**
     * 查找指定的DataCheckResultDetailDTO
     * @param dataCheckConfigDetailId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<DataCheckResultDetail> findDataCheckResultDetailList(Long dataCheckConfigDetailId,String startDate,String endDate);

    }
