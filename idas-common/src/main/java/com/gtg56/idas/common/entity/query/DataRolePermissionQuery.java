package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataRolePermissionQuery", description="数据角色权限表")

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataRolePermissionQuery extends SimpleQuery implements ImABean {

    @ApiModelProperty(value = "角色编码")
    private String code;

}