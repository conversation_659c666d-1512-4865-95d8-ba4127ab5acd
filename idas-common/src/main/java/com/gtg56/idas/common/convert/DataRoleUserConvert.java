package com.gtg56.idas.common.convert;

import com.gtg56.idas.common.entity.dto.auth.DataRolePermissionDTO;
import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public class DataRoleUserConvert {

    public static BiConsumer<List<DataRoleUser>, List<DataRoleUserDTO>> toVO() {
        return (dataRoleUserList, dataRoleUserVOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataRoleUserVOList.size(); i++) {
                DataRoleUser entity = dataRoleUserList.get(i);
                DataRoleUserDTO vo = dataRoleUserVOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<DataRoleUserDTO>, List<DataRoleUser>> toEntity() {
        return (dataRoleUserVOList, dataRoleUserList) -> {
            //添加自定义转换操作

        };
    }

}