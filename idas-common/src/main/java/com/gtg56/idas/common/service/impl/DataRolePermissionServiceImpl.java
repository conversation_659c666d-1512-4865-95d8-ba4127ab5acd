package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.mapper.DataRolePermissionMapper;
import com.gtg56.idas.common.service.IDataRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 数据角色权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service("dataRolePermissionService")
public class DataRolePermissionServiceImpl extends AbstractService<DataRolePermission,DataRolePermissionMapper> implements IDataRolePermissionService {
    @Resource(name = "dataRolePermissionMapper")
    private DataRolePermissionMapper dataRolePermissionMapper;



    /**
     * @param dataRolePermission
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRolePermission saveData(DataRolePermission dataRolePermission) {
        dataRolePermission.setId(null);
        dataRolePermission.baseSet();
        save(dataRolePermission);
        return dataRolePermission;
    }

    @Override
    public Boolean saveData(String dataRoleId, List<DataPermissionDTO> dataPermissionDTOList) {
        Boolean flag =false;

        if (dataPermissionDTOList == null || dataPermissionDTOList.size() == 0) {
            flag = true;
            return flag;
        }


        dataPermissionDTOList.stream().forEach(dataPermissionDTO -> {
            DataRolePermission dataRolePermission = new DataRolePermission();
            dataRolePermission.setDataRoleId(Long.valueOf(dataRoleId));
            dataRolePermission.setDataPermissionId(Long.valueOf(dataPermissionDTO.getId()));
            dataRolePermission.baseSet();
            save(dataRolePermission);
        });
        flag = true;

        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDataByRoleId(String dataRoleId) {
        Boolean flag = false;
        try {
            dataRolePermissionMapper.deleteDataByRoleId(dataRoleId);
            flag = true;
        }catch (Exception e){
            e.printStackTrace();
        }

        return flag;
    }


    /**
     * @param dataRolePermission
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRolePermission updateData(DataRolePermission dataRolePermission) {
        update(dataRolePermission);
        return dataRolePermission;
    }
}
