package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.SensorCompare;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.mapper.WarehouseSensorMapper;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("warehouseSensorService")
public class WarehouseSensorServiceImpl
        extends AbstractService<WarehouseSensor, WarehouseSensorMapper>
        implements IWarehouseSensorService {
    @Override
    public List<WarehouseSensor> listWarehouse() {
        return mapper.listWarehouse();
    }
    
    @Override
    public boolean createOrUpdate(WarehouseSensor warehouseSensor) {
        String warehouseCode = warehouseSensor.getWarehouseCode();
        String regionCode = warehouseSensor.getRegionCode();
        String sensorCode = warehouseSensor.getSensorCode();

        WarehouseSensor inDB = mapper.getBy(warehouseCode, regionCode, sensorCode);
        if(inDB == null) {
            warehouseSensor.setCreateTime(new Date())
                    .setModifyTime(new Date());
            save(warehouseSensor);
            return true;
        } else {
            BeanUtils.copyProperties(warehouseSensor, inDB, "id", "sensorFunction", "createTime", "modifyTime");
            inDB.setModifyTime(new Date());
            update(inDB);
            BeanUtils.copyProperties(inDB,warehouseSensor);
            return false;
        }
    }

    @Override
    public WarehouseSensor getBy(String warehouseCode, String regionCode, String sensorCode) {
        return mapper.getBy(warehouseCode, regionCode, sensorCode);
    }

    @Override
    public List<WarehouseSensor> findBy(WarehouseSensorQuery query) {
        return mapper.findBy(query);
    }

    @Override
    public List<WarehouseSensor> listByCompares(List<SensorCompare> compares) {
        List<WarehouseSensor> sensors = mapper.findBySensorCodes(compares.parallelStream().map(SensorCompare::getSensorCode).collect(Collectors.toList()));

        Set<String> filterCon = compares.parallelStream().map(c -> c.getRegionCode() + "-" + c.getSensorCode()).collect(Collectors.toSet());

        return sensors.parallelStream().filter(sensor ->
                filterCon.contains(sensor.getRegionCode() + "-" + sensor.getSensorCode())
        ).collect(Collectors.toList());
    }
}
