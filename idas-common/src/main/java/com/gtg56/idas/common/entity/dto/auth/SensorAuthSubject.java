package com.gtg56.idas.common.entity.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SensorAuthSubject extends AuthSubject {
    private static final long serialVersionUID = 9183228773960206653L;
    
    private List<String> warehouseCodes;
}
