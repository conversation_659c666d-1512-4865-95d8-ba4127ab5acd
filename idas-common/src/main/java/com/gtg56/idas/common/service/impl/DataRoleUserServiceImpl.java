package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;
import com.gtg56.idas.common.mapper.DataRoleUserMapper;
import com.gtg56.idas.common.service.IDataRoleUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 用户数据角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service("dataRoleUserService")
public class DataRoleUserServiceImpl extends AbstractService<DataRoleUser,DataRoleUserMapper> implements IDataRoleUserService {

    @Resource(name = "dataRoleUserMapper")
    private DataRoleUserMapper dataRoleUserMapper;

    /**
     * @param dataRoleUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRoleUser saveData(DataRoleUser dataRoleUser) {
        dataRoleUser.setId(null);
        dataRoleUser.baseSet();
        save(dataRoleUser);
        return dataRoleUser;
    }

    /**
     * @param dataRoleUser
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRoleUser updateData(DataRoleUser dataRoleUser) {
        dataRoleUser.baseSet();
        update(dataRoleUser);
        return dataRoleUser;
    }

    @Override
    public List<DataRoleUserDTO> listVOByUserId(String userId) {
        return dataRoleUserMapper.listVOByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByUserId(String userId) {
        Boolean flag = false;
        dataRoleUserMapper.deleteByUserId(userId);
        flag = true;

        return flag;
    }
}
