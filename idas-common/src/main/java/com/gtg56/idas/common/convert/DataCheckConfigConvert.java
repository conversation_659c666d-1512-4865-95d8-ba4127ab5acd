package com.gtg56.idas.common.convert;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDTO;
import com.gtg56.idas.common.util.StringUtil;

import java.util.function.BiConsumer;
import com.gtg56.idas.common.util.DateUtil;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public class DataCheckConfigConvert {

    public static BiConsumer<List<DataCheckConfig>, List<DataCheckConfigDTO>> toVO() {
        return (dataCheckConfigList, dataCheckConfigDTOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataCheckConfigDTOList.size(); i++) {
                DataCheckConfig entity = dataCheckConfigList.get(i);
                DataCheckConfigDTO vo = dataCheckConfigDTOList.get(i);
                if(vo ==  null){
                    continue;
                }
                vo.setId(String.valueOf(entity.getId()));
                if (entity.getStartDate() != null) {
                    vo.setStartDate(DateUtil.ymd().format(entity.getStartDate()));
                }
                if (entity.getEndDate() != null) {
                    vo.setEndDate(DateUtil.ymd().format(entity.getEndDate()));
                }

                if (entity.getModifyTime() != null) {
                    vo.setModifyTime(DateUtil.ymdhms().format(entity.getModifyTime()));
                }

                if (entity.getLastCheckTime() != null) {
                    vo.setLastCheckTime(DateUtil.ymdhms().format(entity.getLastCheckTime()));
                }
            }
        };
    }

    public static BiConsumer<List<DataCheckConfigDTO>, List<DataCheckConfig>> toEntity() {
        return (dataCheckConfigDTOList, dataCheckConfigList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataCheckConfigDTOList.size(); i++) {
                DataCheckConfig entity = dataCheckConfigList.get(i);
                DataCheckConfigDTO vo = dataCheckConfigDTOList.get(i);
                if(vo != null && vo.getId() != null) {
                    entity.setId(Long.valueOf(vo.getId()));
                }

                try{
                    if(vo != null && StringUtil.isNotEmpty(vo.getStartDate())) {
                        Date startDate = DateUtil.ymd().parse(vo.getStartDate());
                        entity.setStartDate(startDate);
                    }
                    if(vo != null && StringUtil.isNotEmpty(vo.getEndDate())) {
                        Date endDate = DateUtil.ymd().parse(vo.getEndDate());
                        entity.setEndDate(endDate);
                    }
                }catch (ParseException e){
                    e.printStackTrace();
                }
            }
        };
    }

}