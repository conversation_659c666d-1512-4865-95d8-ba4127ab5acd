package com.gtg56.idas.common.entity.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 手动采集请求对象
 */
public class ManualCollectRequestDTO {
    @ApiModelProperty(value = "仓库编码", required = true)
    private String warehouseCode;
    
    @ApiModelProperty(value = "日期(格式: yyyy-MM-dd)", required = true)
    private String date;
    
    @ApiModelProperty(value = "是否包含异常报警数据", required = false)
    private Boolean includeAlarm;

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Boolean getIncludeAlarm() {
        return includeAlarm;
    }

    public void setIncludeAlarm(Boolean includeAlarm) {
        this.includeAlarm = includeAlarm;
    }
}

/**
 * 手动采集结果对象
 */
public class ManualCollectResultDTO {
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    
    @ApiModelProperty(value = "采集日期")
    private String date;
    
    @ApiModelProperty(value = "是否包含异常数据")
    private Boolean includeAlarm;
    
    @ApiModelProperty(value = "采集的记录总数")
    private Long totalRecords;
    
    @ApiModelProperty(value = "处理成功的记录数")
    private Long successRecords;
    
    @ApiModelProperty(value = "采集状态")
    private String status;
    
    @ApiModelProperty(value = "消息")
    private String message;

    public ManualCollectResultDTO() {}

    public ManualCollectResultDTO(String warehouseCode, String date, Boolean includeAlarm, 
                             Long totalRecords, Long successRecords, String status, String message) {
        this.warehouseCode = warehouseCode;
        this.date = date;
        this.includeAlarm = includeAlarm;
        this.totalRecords = totalRecords;
        this.successRecords = successRecords;
        this.status = status;
        this.message = message;
    }

    // Getters and Setters
    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Boolean getIncludeAlarm() {
        return includeAlarm;
    }

    public void setIncludeAlarm(Boolean includeAlarm) {
        this.includeAlarm = includeAlarm;
    }

    public Long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Long totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Long getSuccessRecords() {
        return successRecords;
    }

    public void setSuccessRecords(Long successRecords) {
        this.successRecords = successRecords;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
