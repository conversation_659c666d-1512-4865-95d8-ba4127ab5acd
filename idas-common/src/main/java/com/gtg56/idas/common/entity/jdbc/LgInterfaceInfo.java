package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;



@Data
@ApiModel("路格温湿度接口表")
@Table(name = "t_lg_interface_tem_hum")
public class LgInterfaceInfo implements ImABean {

    @Id
    private Long id;

    @Column(name = "warehousecode")
    private String warehousecode;//仓库编号

    @Column(name = "address")
    private String Address;//时间段内下传的序号

    @Column(name = "devTime")
    private Date devTime;//设备捕获数据时间

    @Column(name = "sn")
    private String SN;//设备编号

    @Column(name = "channelPort")
    private String channelPort;// 1.是温度，2：是湿度

    @Column(name = "value")
    private BigDecimal value;//温度、湿度

    @Column(name = "pcTime")
    private Date  pcTime;//

    @Column(name = "operTime")
    private Date  operTime;//接口接收时间

    @Column(name = "upper")
    private BigDecimal upper;//上限

    @Column(name = "lower")
    private  BigDecimal lower;//下限



}
