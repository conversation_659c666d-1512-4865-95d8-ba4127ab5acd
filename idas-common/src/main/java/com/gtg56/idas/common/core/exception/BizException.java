package com.gtg56.idas.common.core.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

public class BizException extends RuntimeException {
    private static final long serialVersionUID = 5032310615267506042L;
    
    @Getter
    private ExceptionInfo exceptionInfo;
    
    public BizException() {
    }
    
    public BizException(ExceptionInfo exceptionInfo) {
        this(exceptionInfo.message);
        this.exceptionInfo = exceptionInfo;
    }
    
    public BizException(String message) {
        super(message);
    }
    
    public BizException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public BizException(Throwable cause) {
        super(cause);
    }
    
    public BizException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
    
    @Data
    @AllArgsConstructor(staticName = "of")
    public static class ExceptionInfo {
        private String code;
        private String message;
    }
}
