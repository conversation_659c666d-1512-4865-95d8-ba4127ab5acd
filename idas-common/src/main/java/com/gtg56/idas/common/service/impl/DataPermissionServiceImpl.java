package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.mapper.DataPermissionMapper;
import com.gtg56.idas.common.mapper.UserAuthMapper;
import com.gtg56.idas.common.service.IDataPermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service("dataPermissionService")
public class DataPermissionServiceImpl extends AbstractService<DataPermission, DataPermissionMapper>  implements IDataPermissionService {

    private final static String TYPE_WAREHOUSE = "WAREHOUSE";
    private final static String TYPE_REGION = "REGION";
    private final static String TYPE_SENSOR = "SENSOR";

    @Resource(name = "dataPermissionMapper")
    private DataPermissionMapper dataPermissionMapper;

    /**
     * @param DataPermission
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataPermission saveData(DataPermission DataPermission) {
        DataPermission.setId(null);
        DataPermission.baseSet();
        save(DataPermission);
        return DataPermission;
    }

    /**
     * @param DataPermission
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataPermission updateData(DataPermission DataPermission) {
        DataPermission.baseSet();
        update(DataPermission);
        return DataPermission;
    }

    @Override
    public List<DataPermissionDTO> listVOByRoleId(String roleId) {
        return dataPermissionMapper.listVOByRoleId(roleId);
    }

    @Override
    public List<DataPermissionDTO> listVOByUserId(String userId) {
        List<DataPermissionDTO> list1 = dataPermissionMapper.listVOByUserIdAndType(userId, TYPE_WAREHOUSE);
        List<DataPermissionDTO> list2 = dataPermissionMapper.listVOByUserIdAndType(userId, TYPE_REGION);
        List<DataPermissionDTO> list3 = dataPermissionMapper.listVOByUserIdAndType(userId, TYPE_SENSOR);

        List<DataPermissionDTO> resultVOList = new ArrayList<>();
        Set<DataPermissionDTO> set = new HashSet<>();
        set.addAll(list1);
        set.addAll(list2);
        set.addAll(list3);

        resultVOList = set.stream().collect(Collectors.toList());

//        System.out.println("========================"+resultVOList.size());

        return resultVOList;
    }

    @Override
    public List<DataPermissionDTO> listVOByType(String type) {
        return dataPermissionMapper.listVOByType(type);
    }

    @Override
    public Boolean refreshAndCreate() {

        boolean flag = false;

        List<DataPermission> listForWarehouse = dataPermissionMapper.findNewPermissionForWarehouse();

        List<DataPermission> listForRegion = dataPermissionMapper.findNewPermissionForRegion();

        List<DataPermission> listForSensor = dataPermissionMapper.findNewPermissionForSensor();

        listForWarehouse.stream().forEach(x->{
            this.saveData(x);
        });

        listForRegion.stream().forEach(x->{
            this.saveData(x);
        });

        listForSensor.stream().forEach(x->{
            this.saveData(x);
        });

        flag = true;

        return flag;
    }

    @Override
    public List<DataPermissionDTO> listVOByCodeList(List<DataPermissionDTO> codeList) {

        if (codeList == null || codeList.isEmpty()){
            return codeList;
        }

        List<DataPermission> allDataPermissionList = mapper.selectAll();
        List<DataPermissionDTO> resultDTOList = new ArrayList<>();

        HashMap<String,DataPermission> dataPermissionMap =  new HashMap<>();
        allDataPermissionList.stream().forEach(dataPermission -> {
            String key = "";
            switch (dataPermission.getType()) {
                case "WAREHOUSE":
                    key = dataPermission.getWarehouseCode();
                    break;
                case "REGION":
                    key = dataPermission.getWarehouseCode()+"_"+dataPermission.getRegionCode();
                    break;
                case "SENSOR":
                    key = dataPermission.getWarehouseCode()+"_"+dataPermission.getRegionCode()+"_"+dataPermission.getSensorCode();
                    break;
            }

            dataPermissionMap.put(key,dataPermission);
        });

        codeList.stream().forEach(dataPermissionDTO -> {
            String key = "";
            switch (dataPermissionDTO.getType()) {
                case "WAREHOUSE":
                    key = dataPermissionDTO.getWarehouseCode();
                    break;
                case "REGION":
                    key = dataPermissionDTO.getWarehouseCode()+"_"+dataPermissionDTO.getRegionCode();
                    break;
                case "SENSOR":
                    key = dataPermissionDTO.getWarehouseCode()+"_"+dataPermissionDTO.getRegionCode()+"_"+dataPermissionDTO.getSensorCode();
                    break;
            }
            DataPermission dataPermission = dataPermissionMap.get(key);
            if(dataPermission != null){
                dataPermissionDTO.setId(String.valueOf(dataPermission.getId()));
                dataPermissionDTO.setWarehouseName(dataPermission.getWarehouseName());
                dataPermissionDTO.setRegionName(dataPermission.getRegionName());
                dataPermissionDTO.setSensorName(dataPermission.getSensorName());
                resultDTOList.add(dataPermissionDTO);
            }
        });

        return resultDTOList;
    }
}
