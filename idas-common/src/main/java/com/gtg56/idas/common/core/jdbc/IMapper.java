package com.gtg56.idas.common.core.jdbc;

import com.gtg56.lark.common.base.query.BaseQuery;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ConditionMapper;
import tk.mybatis.mapper.common.IdsMapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

import java.util.List;
import java.util.Map;

/**
 * 定制版MyBatis Mapper插件接口，如需其他接口参考官方文档自行添加。
 */
public interface IMapper<T>
        extends
        BaseMapper<T>,
        ConditionMapper<T>,
        IdsMapper<T>,
        InsertListMapper<T> {

    <F extends BaseQuery> List<T> listByQuery(@Param("params") F var1);

    List<T> listByMap(@Param("params") Map<String, Object> var1);
}
