package com.gtg56.idas.common.entity.base;

import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public abstract class JDBCWithCreatorModifier extends JDBCWithCreateModifyTime {
    private static final long serialVersionUID = -3118493633759907354L;
    
    @Column(name = "creator")
    private String creator;
    @Column(name = "modifier")
    private String modifier;
    
    public void baseSet(UserDTO user) {
        setModifier(user.getId());
        if (id == null) {
            setCreator(user.getId());
        }
        baseSet();
    }
}
