package com.gtg56.idas.common.entity.dto.sensor;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OverLimitHandlingDTO implements ImABean {
    private static final long serialVersionUID = 630600866994802019L;
    
    @ApiModelProperty("温湿度监控系统厂商编码")
    private String corpCode;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("传感器编码")
    private String sensorCode;
    @ApiModelProperty("记录时间")
    private Date recordTime;
    @ApiModelProperty("处理时间")
    private Date handleTime;
    @ApiModelProperty("处理人")
    private String principal;
    @ApiModelProperty("处理意见")
    private String suggestion;
    @ApiModelProperty("温度")
    private BigDecimal temperature;
    @ApiModelProperty("湿度")
    private BigDecimal humidity;
    @ApiModelProperty("温度装")
    private String temperatureStatus;
    @ApiModelProperty("湿度状态")
    private String humidityStatus;
    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHighLimit;
    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLowLimit;
    @ApiModelProperty("湿度上限")
    private BigDecimal humidityHighLimit;
    @ApiModelProperty("湿度下限")
    private BigDecimal humidityLowLimit;
}
