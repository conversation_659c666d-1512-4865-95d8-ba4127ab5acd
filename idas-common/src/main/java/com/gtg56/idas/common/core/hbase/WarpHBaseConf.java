package com.gtg56.idas.common.core.hbase;

import com.gtg56.idas.common.core.hadoop.WarpHDFSConf;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;

import java.io.InputStream;

/**
 * Created by Link on 2017/7/14.
 */
@EqualsAndHashCode(callSuper = true)
public class WarpHBaseConf extends WarpHDFSConf {
    @SneakyThrows
    private InputStream hbase() {
        return fileOrResources("/etc/hbase/conf/hbase-site.xml", "hadoop/hbase-site.xml");
    }
    
    @Override
    public void init() {
        super.init();
        super.addResource(hbase());
    }
}
