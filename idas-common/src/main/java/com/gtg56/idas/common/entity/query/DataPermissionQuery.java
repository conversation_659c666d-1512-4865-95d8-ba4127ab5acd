package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataPermissionQuery", description="数据权限")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataPermissionQuery extends SimpleQuery implements ImABean {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

// "warehouseCode,warehouseName,regionCode,regionName,sensorCode,sensorName,creator,modifier,mofidyTime,"
}