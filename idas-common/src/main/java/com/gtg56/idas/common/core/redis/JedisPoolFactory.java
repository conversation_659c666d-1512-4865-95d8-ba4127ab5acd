package com.gtg56.idas.common.core.redis;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.Resource;

//@Component
//@Configuration
public class JedisPoolFactory implements EnvironmentAware {
    
    @Resource
    @Getter @Setter
    private Environment environment;
    
//    @Bean(name = "jedisPool")
    public JedisPool jedisPool(JedisPoolConfig jedisPoolConfig) {
        String host = getHost();
        int port = getPort();
        int timeout = getTimeout();
        String password = getPassword();

        return new JedisPool(jedisPoolConfig,host,port,timeout,password);
    }
    
//    @Bean(name = "jedisPoolConfig")
    public JedisPoolConfig getJedisPoolConfig() {
        int maxActive = getMaxActive();
        int maxIdle = getMaxIdle();
        long maxWaitMillis = getMaxWaitMills();
        
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(maxActive);
        config.setMaxIdle(maxIdle);
        config.setMaxWaitMillis(maxWaitMillis);
        
        return config;
    }
    
    public String getHost() {
        return environment.getRequiredProperty("redis.host");
    }
    
    public int getPort() {
        return Integer.parseInt(environment.getRequiredProperty("redis.port"));
    }
    
    public int getTimeout() {
        return Integer.parseInt(environment.getRequiredProperty("redis.timeout"));
    }
    
    public int getMaxActive() {
        return Integer.parseInt(environment.getRequiredProperty("redis.pool.max-active"));
    }
    
    public int getMaxIdle() {
        return Integer.parseInt(environment.getRequiredProperty("redis.pool.max-idle"));
    }
    
    public long getMaxWaitMills() {
        return Long.parseLong(environment.getRequiredProperty("redis.pool.max-wait"));
    }
    
    public String getPassword() {
        return environment.getRequiredProperty("redis.password");
    }
}
