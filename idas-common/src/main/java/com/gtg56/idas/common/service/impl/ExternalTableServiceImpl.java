package com.gtg56.idas.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.dto.TableMetaDTO;
import com.gtg56.idas.common.entity.jdbc.ExternalTable;
import com.gtg56.idas.common.mapper.ExternalTableMapper;
import com.gtg56.idas.common.service.IExternalTableService;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by Link on 2019/12/31.
 */
@Service("extTableService")
public class ExternalTableServiceImpl extends AbstractService<ExternalTable, ExternalTableMapper> implements IExternalTableService {
    
    @Resource
    RedisTemplate<String, Object> redisTemplate;
    
    private final static Set<String> supportData = new HashSet<>(Arrays.asList("carbon","delta"));

    @Override
    public boolean supportData(String dataType) {
        return supportData.contains(dataType);
    }

    @Override
    public TableMetaDTO getTableMeta(String namespace, String tableName) {
        Condition condition = new Condition(ExternalTable.class);
        Example.Criteria criteria = condition.and();
        
        criteria.andEqualTo("namespace",namespace)
                .andEqualTo("tableName",tableName);
    
        List<ExternalTable> extTables = findByCondition(condition);
        
        if(extTables.size() == 1) {
            ExternalTable extTable = extTables.stream().findFirst().get();
            TableMetaDTO tableMeta = new TableMetaDTO();
            tableMeta.setNamespace(extTable.getNamespace());
            tableMeta.setTableName(extTable.getTableName());
            tableMeta.setDataType(extTable.getMode());
            tableMeta.setDataStorePath(extTable.getStorePath());
            tableMeta.setMetaStorePath(extTable.getMetaStorePath());
            tableMeta.setIsHiveTable(false);
    
            Map<String, String> tblProp = new HashMap<>();
            if(StringUtils.isNotBlank(extTable.getTableProperties())) {
                JSONObject json = JSON.parseObject(extTable.getTableProperties());
                for (String key : json.getInnerMap().keySet()) {
                    tblProp.put(key, json.getString(key));
                }
            }
            tableMeta.setTableProperties(tblProp);
   
            List<String> partitionCols = new ArrayList<>();
            if("delta".equals(extTable.getMode())) {
            
            }
            tableMeta.setPartitionColumns(partitionCols);
            
            return tableMeta;
        }
        return null;
    }
}
