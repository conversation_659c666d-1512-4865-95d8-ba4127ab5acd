package com.gtg56.idas.common.core.kafka;

import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;


public class KafkaProperties extends Properties {
    private static final long serialVersionUID = 2021824917581534561L;
    
    @Resource
    @Getter
    @Setter
    private Environment env;
    
    @SneakyThrows
    public KafkaProperties (String propertiesFile) {
        this.load(this.getClass().getResourceAsStream(propertiesFile));
    }
    
    public void init() {
        this.setProperty("bootstrap.servers",env.getProperty("kafka.broker.address"));
        this.setProperty("metadata.broker.list",env.getProperty("kafka.broker.address"));
    }
    
    public Map<String,String> toMap() {
        Map<String,String> map = new HashMap<>();
        keySet().forEach(key -> {
            String k = (String) key;
            map.put(k,getProperty(k));
        });
        return map;
    }
}
