package com.gtg56.idas.common.entity.dto.sensor;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SensorRecordStatDTO implements ImABean {
    private static final long serialVersionUID = 1930326687888217930L;
    
    @ApiModelProperty("最高温度")
    private BigDecimal temperatureMax;
    @ApiModelProperty("最高传感器编码")
    private String temperatureMaxSensorCode;
    
    @ApiModelProperty("最低温度")
    private BigDecimal temperatureMin;
    @ApiModelProperty("最低温度传感器编码")
    private String temperatureMinSensorCode;
    
    @ApiModelProperty("平均温度")
    private BigDecimal temperatureAvg;
    
    @ApiModelProperty("最高湿度")
    private BigDecimal humidityMax;
    @ApiModelProperty("最高传感器编码")
    private String humidityMaxSensorCode;
    
    @ApiModelProperty("最低湿度")
    private BigDecimal humidityMin;
    @ApiModelProperty("最低湿度传感器编码")
    private String humidityMinSensorCode;
    
    @ApiModelProperty("平均湿度")
    private BigDecimal humidityAvg;
}
