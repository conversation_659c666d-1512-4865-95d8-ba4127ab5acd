package com.gtg56.idas.common.convert;

import java.util.List;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO;

import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public class DataCheckResultConvert {

    public static BiConsumer<List<DataCheckResult>, List<DataCheckResultDTO>> toVO() {
        return (dataCheckResultList, dataCheckResultDTOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataCheckResultDTOList.size(); i++) {
                DataCheckResult entity = dataCheckResultList.get(i);
                DataCheckResultDTO vo = dataCheckResultDTOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<DataCheckResultDTO>, List<DataCheckResult>> toEntity() {
        return (dataCheckResultDTOList, dataCheckResultList) -> {
            //添加自定义转换操作

        };
    }

}