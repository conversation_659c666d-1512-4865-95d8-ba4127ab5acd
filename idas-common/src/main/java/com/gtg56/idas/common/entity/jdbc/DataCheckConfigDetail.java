package com.gtg56.idas.common.entity.jdbc;

import java.util.Date;
import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 数据完整性检测配置明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckConfigDetail", description="数据完整性检测配置明细表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_check_config_detail")
public class DataCheckConfigDetail extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据完整性检测配置ID")
    private Long dataCheckConfigId;

    @ApiModelProperty(value = "数据权限ID")
    private Long dataPermissionId;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;

    @ApiModelProperty(value = "是否删除")
    private Integer deletedFlag;
}
