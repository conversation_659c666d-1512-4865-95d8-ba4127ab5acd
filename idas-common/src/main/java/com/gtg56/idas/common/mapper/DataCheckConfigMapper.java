package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.core.jdbc.IMapper;
import org.apache.ibatis.annotations.Options;

/**
 * <p>
 * 数据完整性检测配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface DataCheckConfigMapper extends IMapper<DataCheckConfig> {

    int updateById(DataCheckConfig entity);
    Long  insertData(DataCheckConfig entity);

}
