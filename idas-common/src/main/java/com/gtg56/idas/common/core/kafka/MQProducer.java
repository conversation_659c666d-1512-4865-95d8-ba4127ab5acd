package com.gtg56.idas.common.core.kafka;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Properties;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Created by Link on 2017/9/14.
 */
@Slf4j
public class MQProducer {
    @Getter
    final private String topic;
    @Getter
    final private Properties properties;
    final private Producer<String,String> producer;

    public MQProducer(Properties prop, String topic) {
        this.properties = (Properties) prop.clone();
        this.topic = topic;

        producer = new KafkaProducer<>(properties);
    }

    public boolean produce(String value) {
        return produce(null,value);
    }

    public boolean produce(String key,String value) {
        Future<RecordMetadata> res = producer.send(new ProducerRecord<>(topic, key, value));
        return waitUntilKafkaBrokerResponseAndGetStatue(res);
    }

    private boolean waitUntilKafkaBrokerResponseAndGetStatue(Future<RecordMetadata> future) {
        while (!future.isDone()) {}

        try {
            RecordMetadata rm = future.get(10, TimeUnit.SECONDS);
            log.debug("topic {} partition {} offset {} timestamp {}",rm.topic(),rm.partition(),rm.offset(),rm.timestamp());
            return rm.partition() > -1 && StringUtils.isNotBlank(rm.topic());
        } catch (Exception e) {
            log.warn("MQProducer.waitUntilKafkaBrokerResponseAndGetStatue catch exception",e);
        }
        return false;
    }
}
