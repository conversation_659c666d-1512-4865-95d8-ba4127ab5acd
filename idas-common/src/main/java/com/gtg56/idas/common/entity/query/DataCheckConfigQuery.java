package com.gtg56.idas.common.entity.query;

import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 数据完整性检测配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@ApiModel(value="DataCheckConfigQuery", description="数据完整性检测配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckConfigQuery extends SimpleQuery {

    @ApiModelProperty(value = "配置编码")
    private String code;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "配置状态 １ 启用０　禁用")
    private String status;

    @ApiModelProperty(value = "检测方式 dictCode: 1 固定天数  2 指定时间段")
    private String checkType;

    @ApiModelProperty(value = "检测类型 dictCode: 1 仓库2 库房")
    private String checkDataType;


// "code,name,status,checkType,checkDataType,remark,lastCheckTime,lastCheckStatus,"
}