package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.mapper.DataCheckResultDetailMapper;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.service.IDataCheckResultDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 数据完整性检测结果明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service("dataCheckResultDetailService")
public class DataCheckResultDetailServiceImpl extends AbstractService<DataCheckResultDetail, DataCheckResultDetailMapper> implements IDataCheckResultDetailService {

    @Resource(name = "dataCheckResultDetailMapper")
    private DataCheckResultDetailMapper dataCheckResultDetailMapper;

    /**
     * @param dataCheckResultDetail
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckResultDetail saveData(DataCheckResultDetail dataCheckResultDetail) {
        dataCheckResultDetail.setId(null);
        dataCheckResultDetail.baseSet();
        save(dataCheckResultDetail);
        dataCheckResultDetail = findById(dataCheckResultDetail.getId());
        return dataCheckResultDetail;
    }

    /**
     * @param dataCheckResultDetail
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckResultDetail updateData(DataCheckResultDetail dataCheckResultDetail) {
        dataCheckResultDetail.baseSet();
        update(dataCheckResultDetail);
        return dataCheckResultDetail;
    }

    @Override
    public List<DataCheckResultDetail> findDataCheckResultDetailList(Long dataCheckConfigDetailId, String startDate, String endDate) {
        return dataCheckResultDetailMapper.findDataCheckResultDetailList(dataCheckConfigDetailId, startDate, endDate);
    }
}
