package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;

import java.util.List;

/**
 * <p>
 * 数据权限 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IDataPermissionService extends IService<DataPermission> {

    /**
     *
     * @param DataPermission
     * @return
     */
    public DataPermission saveData(DataPermission DataPermission);

    /**
     *
     * @param DataPermission
     */
    public DataPermission updateData(DataPermission DataPermission);

    /**
     * 通过数据角色ID查找数据权限列表
     * @param roleId 数据角色ID
     * @return
     */
    public List<DataPermissionDTO> listVOByRoleId(String roleId);

    /**
     * 通过用户ID查找数据权限列表
     * @param userId 用户ID
     * @return
     */
    public List<DataPermissionDTO> listVOByUserId(String userId);

    /**
     * 通过权限类型查询数据权限
     * @param type 权限类型
     * @return
     */
    public List<DataPermissionDTO> listVOByType(String type);


    /**
     * 根据warehouse_sensor自动创建新增的数据权限，包括类型为：仓库、库房及测点终端
     * @return
     */
    public Boolean refreshAndCreate();

    /**
     * 通过传入的数据权限编码列表，找到对应的数据权限，并返回
     * @param codeList 权限类型
     * @return
     */
    public List<DataPermissionDTO> listVOByCodeList(List<DataPermissionDTO> codeList);
}
