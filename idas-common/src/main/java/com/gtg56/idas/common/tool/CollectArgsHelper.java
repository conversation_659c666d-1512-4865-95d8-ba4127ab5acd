package com.gtg56.idas.common.tool;

import lombok.SneakyThrows;
import org.apache.commons.cli.*;

import java.util.Arrays;
import java.util.List;

public class CollectArgsHelper {
    
    @SneakyThrows
    public static List<String> getConfigFilePaths(String[] args) {
        Options options = new Options();
        options.addOption(Option.builder().argName("confFiles").longOpt("confFiles").hasArg().required().build());
        CommandLineParser parser = new DefaultParser();
        CommandLine parse = parser.parse(options, args);
    
        String value = parse.getOptionValue("confFiles");
        
        return Arrays.asList(value.trim().split(","));
    }
}
