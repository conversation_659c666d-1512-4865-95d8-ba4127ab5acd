package com.gtg56.idas.common.entity.sensor.lb;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

@Data
public class LBSensorInfo implements ImABean {
    private static final long serialVersionUID = 5984147267906620515L;

    private String regionCode;
    private String regionName;
    private String sensorCodeOrigin;
    private String sensorName;
    private Float temperatureHighLimit;
    private Float temperatureLowLimit;
    private Float humidityHighLimit;
    private Float humidityLowLimit;
}
