package com.gtg56.idas.common.util;

import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class NetworkUtil {
    
    public static String getLocalIpv4() {
        try {
            InetAddress localHost = Inet4Address.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn(e.getMessage(),e);
        }
        return null;
    }
    
    public static String getLocalIpv6() {
        try {
            InetAddress localHost = Inet6Address.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn(e.getMessage(),e);
        }
        return null;
    }
}
