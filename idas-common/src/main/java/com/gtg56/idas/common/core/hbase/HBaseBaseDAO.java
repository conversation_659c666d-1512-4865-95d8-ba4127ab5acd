package com.gtg56.idas.common.core.hbase;

import com.gtg56.idas.common.entity.base.HBaseBaseDO;
import lombok.Setter;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.io.Closeable;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * Created by Link on 2017/5/27.
 * HBase基础DAO
 */
public abstract class HBaseBaseDAO<B extends HBaseBaseDO> implements Closeable {

    private static Logger log = LoggerFactory.getLogger(HBaseBaseDAO.class);
    
    @Setter
    @Resource(name = "hbaseConf")
    private Configuration conf;
    
    @Setter
    @Value("${hbase.multiGet.threads}")
    private Integer esSize;

    private volatile static Connection conn = null;
    final private static Lock connLock = new ReentrantLock(true) , esLock = new ReentrantLock(true);

    private volatile ExecutorService es = null;
    private Class<B> beanClass;
    protected TableName tableName;
    protected String columnFamily;

    @SuppressWarnings("unchecked")
    protected HBaseBaseDAO() {
        // 获取泛型类
        beanClass = (Class<B>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];

        // 获取Bean对应的表名/列族名
        B bean = getBeanInstance();
        if(bean == null) throw new RuntimeException("can not create " + beanClass.getName() + " instance ");
        tableName = TableName.valueOf(bean.getTableName());
        columnFamily = bean.getColumnFamily();
    }

    /**
     * 创建Bean实例
     * @return Bean实例
     */
    protected B getBeanInstance() {
        try {
            return beanClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            log.warn("Create Instance of Bean : " + beanClass.getName() + " fail ",e);
        }
        return null;
    }

    /**
     * 懒汉双重检查锁获取Connection单例
     * Connection是线程安全的，而且获取Connection是个重型操作，
     * 所以一个JVM只需要一个Connection对象
     * @return Connection单例
     * @throws IOException
     */
    private Connection getConn() throws IOException  {
        if(connectionNotUseful()) {
            connLock.lock();
            try {
                if(connectionNotUseful())
                    conn = ConnectionFactory.createConnection(conf);
            } finally {
                connLock.unlock();
            }
        }
        return conn;
    }

    private static boolean connectionUseful() {
        return conn != null && !conn.isAborted() && !conn.isClosed();
    }
    private static boolean connectionNotUseful() {
        return conn == null || conn.isAborted() || conn.isClosed();
    }

    /**
     * 获取HBase表对象
     * 虽然Table是线程安全的，但是Table对象保存从HMaster获取的表的region信息，
     * 包括Region的开始rowKey和结束rowKey。这些信息可能随时会变（Region自动或手动分裂）
     * 而get/put/scan/delete操作也依赖这些信息去跟RegionServer交互。
     * 所以建议每个操作单独获取Table。获取Table这个操作非常轻量。
     * @return Table
     * @throws IOException
     */
    protected Table getTable() throws IOException {
        return getConn().getTable(tableName);
    }

    public Configuration getConf() {
        return conf;
    }

    protected ExecutorService getEs() {
        if(es == null) {
            esLock.lock();
            try {
                if(es == null) {
                    es = Executors.newFixedThreadPool(esSize);
                }
            } finally {
                esLock.unlock();
            }
        }
        return es;
    }

    /**
     * 获取
     * @param rowKey 行键
     * @return bean
     */
    public B get(byte[] rowKey) {
        B bean = getBeanInstance();

        if(bean != null) {
            try (Table table = getTable()) {
                Get get = new Get(rowKey);
                get.addFamily(columnFamily.getBytes());

                Result result = table.get(get);

                bean.map(result);
            } catch (IOException e) {
                log.warn("HBase get fail.",e);
            }
        }
        return bean;
    }

    /**
     * 获取
     * @param rowKey 行键
     * @return bean
     */
    public B get(String rowKey) {
        return get(Bytes.toBytes(rowKey));
    }

    /**
     * 多重获取
     * 通过多线程异步请求HBase，优化Table.get()对多个行键查询慢得令人发指的性能
     * 同时保证返回顺序与传入rowKeys顺序一致，丢弃null
     * @param rowKeys 行键列表
     * @return beans
     */
    public List<B> get(final List<byte[]> rowKeys) {
        return get(rowKeys,true);
    }
    
    /**
     * 多重获取
     * 通过多线程异步请求HBase，优化Table.get()对多个行键查询慢得令人发指的性能
     * 同时保证返回顺序与传入rowKeys顺序一致
     * @param rowKeys 行键列表
     * @param dropNullResult 是否丢弃null result
     * @return beans
     */
    public List<B> get(final List<byte[]> rowKeys,final boolean dropNullResult) {
        LinkedHashMap<byte[], B> results = new LinkedHashMap<>();
        List<Future<B>> futures = new ArrayList<>(rowKeys.size());
    
        rowKeys.forEach(rowKey -> {
            results.put(rowKey, null);
            futures.add(getEs().submit(new AsyncGetTask(rowKey)));
        });
    
        while (!futures.isEmpty()) {
            // 注意不要在遍历中对集合进行修改。除非是concurrent包下的集合。
            List<Future<B>> rmList = new ArrayList<>(futures.size());
        
            futures.stream().filter(Future::isDone).forEach(future -> {
                try {
                    B result = future.get(500, TimeUnit.MILLISECONDS);
                    results.put(result.getRowKey(),result);
                    rmList.add(future);
                } catch (Exception e) {
                    log.warn("HBase multi get fail", e);
                }
            });
        
            futures.removeAll(rmList);
        }
        
        if(dropNullResult) {
            return results.values().stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return new ArrayList<>(results.values());
        }
    }

    /**
     * 异步获取任务类
     */
    private class AsyncGetTask implements Callable<B> {
        private byte[] rk;

        AsyncGetTask(byte[] rowKey) {
            this.rk = rowKey;
        }

        @Override
        public B call() throws Exception {
            return get(rk);
        }
    }

    /**
     * 插入/更新
     * @param bean bean
     */
    public void put(B bean) {
        try (Table table = getTable()){
            table.put(bean.toPut());
        } catch (IOException e) {
            log.warn("HBase put fail.",e);
        }
    }

    public B putAndGet(B bean) {
        put(bean);
        return get(bean.getRowKey());
    }

    /**
     * 插入/更新
     * @param beans beans
     */
    public void put(List<B> beans) {
        try (Table table = getTable()){
            List<Put> puts = beans.stream().map(HBaseBaseDO::toPut).collect(Collectors.toList());
            table.put(puts);
        } catch (IOException e) {
            log.warn("HBase put fail.",e);
        }
    }

    /**
     * 根据行键范围获取
     * 注意：scan扫描 rowKey >= startRowKey AND rowKey < endRowKey
     * 即不包括endRowKey。
     * includeEndRow = true除外
     * @param startRowKey 起始行键
     * @param endRowKey 结束行键
     * @param includeEndRow 是否包含结束行键的行
     * @return list of bean
     */
    public List<B> scan(byte[] startRowKey, byte[] endRowKey, boolean includeEndRow) {
        Scan scan;

        if(includeEndRow) {
            Filter filter = new InclusiveStopFilter(endRowKey);
            scan = new Scan().withStartRow(startRowKey).setFilter(filter);
        } else {
            scan = new Scan().withStartRow(startRowKey).withStopRow(endRowKey);
        }

        scan.addFamily(columnFamily.getBytes());
        return scan(scan);
    }

    /**
     * 扫描
     * @param scan 自定义扫描器
     * @return list of bean
     */
    protected List<B> scan(Scan scan) {
        List<B> list = new ArrayList<>();

        try (Table table = getTable() ) {
            ResultScanner resultScanner = table.getScanner(scan);

            Result result;

            while ((result = resultScanner.next()) != null ) {
                B bean = getBeanInstance();
                if (bean != null) {
                    bean.map(result);
                    list.add(bean);
                }
            }
        } catch (IOException e) {
            log.warn("HBase scan fail.",e);
        }
        return list;
    }

    /**
     * 过滤
     * @param filter 过滤器
     * @return list of bean
     */
    public List<B> filter(Filter filter) {
        Scan scan = new Scan();
        scan.addFamily(columnFamily.getBytes());
        scan.setFilter(filter);

        return scan(scan);
    }

    public void delete(byte[] rowKey) {
        try (Table table = getTable()) {
            Delete delete = new Delete(rowKey);

            table.delete(delete);
        } catch (IOException e) {
            log.warn("HBase delete fail.", e);
        }
    }

    public void delete(B bean) {
        if(bean.getRowKey()!=null && bean.getRowKey().length > 0) {
            delete(bean.getRowKey());
        }
    }

    @Override
    public void close() throws IOException {
        if (es != null) es.shutdown(); // 强制停止所有异步获取操作
        if (conn != null) conn.close();
    }

    @Override
    protected void finalize() throws Throwable {
        this.close();
        super.finalize();
    }

    /**
     * 行键前缀过滤器
     * @param rowKeyPrefix 行键前缀
     * @return 过滤器
     */
    public static Filter rowKeyPrefixFilter(byte[] rowKeyPrefix) {
        return new PrefixFilter(rowKeyPrefix);
    }

    /**
     * 获得相等过滤器。相当于SQL的 [字段] = [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter eqFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.EQUAL, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 获得大于过滤器。相当于SQL的 [字段] > [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter gtFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.GREATER, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 获得大于等于过滤器。相当于SQL的 [字段] >= [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter gteqFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.GREATER_OR_EQUAL, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 获得小于过滤器。相当于SQL的 [字段] < [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter ltFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.LESS, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 获得小于等于过滤器。相当于SQL的 [字段] <= [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter lteqFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.LESS_OR_EQUAL, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 获得不等于过滤器。相当于SQL的 [字段] != [值]
     * @param cf 列族名
     * @param col 列名
     * @param val 值
     * @return 过滤器
     */
    public static Filter neqFilter(String cf, String col, byte[] val) {
        SingleColumnValueFilter f = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.NOT_EQUAL, val);
        f.setLatestVersionOnly(true);
        f.setFilterIfMissing(true);
        return f;
    }

    /**
     * 和过滤器 相当于SQL的 的 and
     * @param filters 多个过滤器
     * @return 过滤器
     */
    public static Filter andFilter(Filter... filters) {
        FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);
        if(filters!=null && filters.length > 0) {
            if(filters.length > 1) {
                for (Filter f : filters) {
                    filterList.addFilter(f);
                }
            }
            if(filters.length == 1) {
                return filters[0];
            }
        }
        return filterList;
    }

    /**
     * 和过滤器 相当于SQL的 的 and
     * @param filters 多个过滤器
     * @return 过滤器
     */
    public static Filter andFilter(Collection<Filter> filters) {
        return andFilter(filters.toArray(new Filter[0]));
    }

    /**
     * 或过滤器 相当于SQL的 or
     * @param filters 多个过滤器
     * @return 过滤器
     */
    public static Filter orFilter(Filter... filters) {
        FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);
        if(filters!=null && filters.length > 0) {
            for(Filter f : filters) {
                filterList.addFilter(f);
            }
        }
        return filterList;
    }

    /**
     * 或过滤器 相当于SQL的 or
     * @param filters 多个过滤器
     * @return 过滤器
     */
    public static Filter orFilter(Collection<Filter> filters) {
        return orFilter(filters.toArray(new Filter[0]));
    }

    /**
     * 非空过滤器 相当于SQL的 is not null
     * @param cf 列族
     * @param col 列
     * @return 过滤器
     */
    public static Filter notNullFilter(String cf,String col) {
        SingleColumnValueFilter filter = new SingleColumnValueFilter(cf.getBytes(),col.getBytes(), CompareOperator.NOT_EQUAL,new NullComparator());
        filter.setFilterIfMissing(true);
        filter.setLatestVersionOnly(true);
        return filter;
    }

    /**
     * 空过滤器 相当于SQL的 is null
     * @param cf 列族
     * @param col 列
     * @return 过滤器
     */
    public static Filter nullFilter(String cf,String col) {
        SingleColumnValueFilter filter = new SingleColumnValueFilter(cf.getBytes(),col.getBytes(), CompareOperator.EQUAL,new NullComparator());
        filter.setFilterIfMissing(false);
        filter.setLatestVersionOnly(true);
        return filter;
    }

    /**
     * 子字符串过滤器 相当于SQL的 like '%[val]%'
     * @param cf 列族
     * @param col 列
     * @param sub 子字符串
     * @return 过滤器
     */
    public static Filter subStringFilter(String cf, String col, String sub) {
        SingleColumnValueFilter filter = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.EQUAL, new SubstringComparator(sub));
        filter.setFilterIfMissing(true);
        filter.setLatestVersionOnly(true);
        return filter;
    }

    /**
     * 正则过滤器 相当于SQL的 rlike '[regex]'
     * @param cf 列族
     * @param col 列
     * @param regex 正则表达式
     * @return 过滤器
     */
    public static Filter regexFilter(String cf, String col , String regex) {
        SingleColumnValueFilter filter = new SingleColumnValueFilter(cf.getBytes(), col.getBytes(), CompareOperator.EQUAL, new RegexStringComparator(regex));
        filter.setFilterIfMissing(true);
        filter.setLatestVersionOnly(true);
        return filter;
    }
}
