package com.gtg56.idas.common.entity.jdbc;

import java.util.Date;
import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <p>
 * 数据完整性检测结果明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckResultDetail", description="数据完整性检测结果明细表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_check_result_detail")
public class DataCheckResultDetail extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据日期")
    private Date tradeDate;

    @ApiModelProperty(value = "数据完整性检测配置明细表ID")
    private Long dataCheckConfigDetailId;

    @ApiModelProperty(value = "数据权限ID")
    private Long warehouseSensorId;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "库房编码")
    private String regionCode;

    @ApiModelProperty(value = "测点终端编码")
    private String sensorCode;

    @ApiModelProperty(value = "缺失数据数量")
    private Integer missQuantity;

    @ApiModelProperty(value = "检测时间")
    private Date checkTime;

    @ApiModelProperty(value = "复核时间")
    private Date recheckTime;

    @ApiModelProperty(value = "最后执行状态 ０　正常  １　异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;

    @ApiModelProperty(value = "是否删除")
    private Long deletedFlag;

    @Transient
    @ApiModelProperty(value = "数据完整性检测配置名称")
    private String dataCheckConfigName;

    @Transient
    @ApiModelProperty(value = "数据完整性检测配置编码")
    private String dataCheckConfigCode;

    @Transient
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @Transient
    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @Transient
    @ApiModelProperty(value = "测点终端名称")
    private String sensorName;



}
