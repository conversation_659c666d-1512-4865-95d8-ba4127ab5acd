package com.gtg56.idas.common.entity.hbase.key;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverLimitHandingKey implements ImABean {
    private static final long serialVersionUID = -7010117272973302125L;
    private String sensorCode;
    private Date recordTime;
}
