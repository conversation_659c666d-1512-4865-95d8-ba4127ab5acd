package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.GncAppKey;
import com.gtg56.idas.common.mapper.GncAppKeyMapper;
import com.gtg56.idas.common.service.IGncAppKeyService;
import org.springframework.stereotype.Service;

@Service("gncAppKeyService")
public class GncAppKeyServiceImpl
        extends AbstractService<GncAppKey, GncAppKeyMapper>
        implements IGncAppKeyService {


    @Override
    public boolean hasKey(String key) {
        return mapper.numKey(key) > 0;
    }
}
