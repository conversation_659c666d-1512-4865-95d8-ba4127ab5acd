package com.gtg56.idas.common.util;

import com.gtg56.idas.common.entity.meta.SchemaChange;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;

import java.util.*;

public class SparkUtil {
    
    /**
     * 小驼峰列名转下划线
     * 适用于通过Bean生成的DataFrame转存
     * @param df dataFrame
     * @return 下划线列名的DataFrame
     */
    public static Dataset<Row> camelColumnToUnderscore(Dataset<Row> df) {
        String[] colMapping = Arrays.stream(df.columns()).map(col ->
                String.format("%s as %s", col, CaseUtil.lowerCamelToUnderscore(col))
        ).toArray(String[]::new);
        
        return df.selectExpr(colMapping);
    }
    
    /**
     * 下划线列名转小驼峰
     * 适用于从表中读取的DataFrame转Bean
     * @param df DataFrame
     * @return 小驼峰列名的DataFrame
     */
    public static Dataset<Row> underscoreColToCamel(Dataset<Row> df) {
        String[] colMapping = Arrays.stream(df.columns()).map(col ->
                String.format("%s as %s", col, CaseUtil.underscoreToCamel(col))
        ).toArray(String[]::new);
    
        return df.selectExpr(colMapping);
    }
    
    /**
     * null度量填0
     *
     * 某些维度没有数据，聚合出来的度量是null，但又想保留这些维度的时候，就需要填0
     * @param df df
     * @param measureColumns 度量列
     * @return 填0后的df
     */
    public static Dataset<Row> nullMeasuresFillZero(Dataset<Row> df,String... measureColumns) {
        // linkedHashMap保序
        Map<String, String> select = new LinkedHashMap<>();
        Arrays.stream(df.columns()).forEach(col -> select.put(col,col));
        
        Arrays.stream(measureColumns)
                .forEach(col -> select.put(col,"( " + col + " + 0 ) as " + col));
        
        return df.selectExpr(select.values().toArray(new String[0]));
    }
    
    private static final Set<DataType> numberTypes = new HashSet<>(
            Arrays.asList(
                DataTypes.ShortType,DataTypes.IntegerType,DataTypes.LongType,
                DataTypes.FloatType,DataTypes.DoubleType
            )
    );
    
    /**
     * null度量填0
     * 自动将数值型的列视为度量
     *
     * 某些维度没有数据，聚合出来的度量是null，但又想保留这些维度的时候，就需要填0
     * @param df df
     * @return 填0后的df
     */
    public static Dataset<Row> nullMeasuresFillZero(Dataset<Row> df) {
        String[] measures = Arrays.stream(df.schema().fields())
                .filter(sf -> numberTypes.contains(sf.dataType()))
                .map(StructField::name)
                .toArray(String[]::new);
        
        return nullMeasuresFillZero(df,measures);
    }
    
    /**
     * 结构检查
     * @param source 数据源名称
     * @param sourceSchema 数据源结构
     * @param target 目标源名称
     * @param targetSchema 目标源结构
     * @return 结构差异
     */
    public static List<SchemaChange> schemaCheck(String source,StructType sourceSchema,String target,StructType targetSchema) {
        List<SchemaChange> changes = new ArrayList<>();
        
        Map<String,StructField> sourceFields = new HashMap<>();
        Arrays.stream(sourceSchema.fields()).forEach(column -> sourceFields.put(column.name(),column));
        Map<String,StructField> targetFields = new HashMap<>();
        Arrays.stream(targetSchema.fields()).forEach(column -> targetFields.put(column.name(),column));
        
        // addColumn
        sourceFields.entrySet().stream()
                .filter(column  -> !targetFields.containsKey(column .getKey()))
                .forEach(column -> {
                    SchemaChange schemaChange = new SchemaChange()
                            .setSource(source)
                            .setTarget(target)
                            .setChangeType(SchemaChange.ChangeType.AddColumn)
                            .setColumnName(column.getKey())
                            .setNewDataType(column.getValue().dataType().typeName());
                    changes.add(schemaChange);
                });
        
        // changeType
        sourceFields.entrySet().stream()
                .filter(column -> targetFields.containsKey(column.getKey()))
                .filter(column -> {
                    String columnName = column.getKey();
                    StructField oldColumn = targetFields.get(columnName);
                    
                    return !column .getValue().dataType().equals(oldColumn.dataType());
                })
                .forEach(column -> {
                    String columnName = column.getKey();
                    StructField oldColumn = targetFields.get(columnName);
                    SchemaChange schemaChange = new SchemaChange()
                            .setSource(source)
                            .setTarget(target)
                            .setChangeType(SchemaChange.ChangeType.TypeChange)
                            .setColumnName(column .getKey())
                            .setNewDataType(column .getValue().dataType().typeName())
                            .setOldDataType(oldColumn.dataType().typeName());
    
                    changes.add(schemaChange);
                });
    
        // deleteColumn
        targetFields.entrySet().stream()
                .filter(column  -> !sourceFields.containsKey(column .getKey()))
                .forEach(column  -> {
                    SchemaChange schemaChange = new SchemaChange()
                            .setSource(source)
                            .setTarget(target)
                            .setChangeType(SchemaChange.ChangeType.DeleteColumn)
                            .setColumnName(column.getKey())
                            .setOldDataType(column.getValue().dataType().typeName());
                    changes.add(schemaChange);
                });

        changes.sort(SchemaChange::compareTo);
        return changes;
    }

    public static Dataset<Row> unionAll(Collection<Dataset<Row>> ds) {
        if (ds.isEmpty()) return null;

        Dataset<Row> f = null;

        for (Dataset<Row> d : ds) {
            if (f == null) {
                f = d;
            } else {
                f = f.unionAll(d);
            }
        }
        return f;
    }

}
