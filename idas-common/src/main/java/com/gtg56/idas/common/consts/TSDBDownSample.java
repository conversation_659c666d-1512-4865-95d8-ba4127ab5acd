package com.gtg56.idas.common.consts;

public class TSDBDownSample {
    public static String downSample(int amount, TSDBTimeUnit timeUnit, TSDBAggregator aggregator, TSDBFillPolicy fillPolicy) {
        return timeUnit.toDuration(amount) + "-" + aggregator.getName() + "-" + fillPolicy.getName();
    }
    
    public static final String
            _1SEC_LAST_NULL = downSample(1, TSDBTimeUnit.SECOND, TSDBAggregator.LAST, TSDBFillPolicy.NULL),
    
    _1MIN_LAST_NULL = downSample(1, TSDBTimeUnit.MINUTE, TSDBAggregator.LAST, TSDBFillPolicy.NULL),
    
    _30MIN_LAST_NONE = downSample(30, TSDBTimeUnit.MINUTE, TSDBAggregator.LAST, TSDBFillPolicy.NONE),
            _30MIN_LAST_NULL = downSample(30, TSDBTimeUnit.MINUTE, TSDBAggregator.LAST, TSDBFillPolicy.NULL);
}
