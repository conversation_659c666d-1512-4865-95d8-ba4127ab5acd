package com.gtg56.idas.common.entity.dto.sensor;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class SensorStatResultDTO implements ImABean {
    private static final long serialVersionUID = 6478570658605103187L;
    
    @ApiModelProperty("平均值")
    private BigDecimal avg;
    @ApiModelProperty("最低值")
    private BigDecimal min;
    @ApiModelProperty("最高值")
    private BigDecimal max;
    
    @ApiModelProperty("值")
    private List<Detail> details;
    
    @Data
    public static class Detail implements ImABean {
        private static final long serialVersionUID = -2851764882286209540L;
        @ApiModelProperty("仓库编码")
        private String warehouseCode;
        @ApiModelProperty("区域编码")
        private String regionCode;
        @ApiModelProperty("传感器编码")
        private String sensorCode;
        @ApiModelProperty("记录日期")
        private Date recordTime;
        @ApiModelProperty("值")
        private BigDecimal value;
    }
}
