package com.gtg56.idas.common.entity.sensor.lg;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class LGOverLimitHandling implements ImABean {
    private static final long serialVersionUID = 7804429828176242121L;

    /*与SQL字段的顺序大小写保持一致，不然可能获取不到值*/
    private  Integer recordId;//数据ID
    /*
    * wareHouseCode：仓库编码
    * orgCode：区域编码
    * orgName：区域名称
    * nodeCode：设备编码
    * nodeName：设备名称
    * */
    private String wareHouseCode, orgCode,orgName,nodeCode,nodeName;

    /*温度
    * 湿度
    * 温度上限
    * 温度下限
    * 湿度上限
    * 湿度下限
    * */
    private Double temperature,humidity,
            temperatureHighLimit,temperatureLowLimit,
            humidityHighLimit,humidityLowLimit;

    //记录时间
    private Date recordDateTime;

   //温控问题的处理意见
   private String overReason;
}
