package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.dto.TableMetaDTO;
import com.gtg56.idas.common.entity.jdbc.ExternalTable;

/**
 * Created by Link on 2019/12/31.
 * 管理非hive托管的表，如delta carbon
 */
public interface IExternalTableService extends IService<ExternalTable> {
    /**
     * 判断数据存储类型是否支持
     * @param dataType 数据存储类型
     * @return 支持与否
     */
    boolean supportData(String dataType);

    /**
     * 获取表的meta
     * @param namespace 命名空间
     * @param tableName 表名
     * @return meta
     */
    TableMetaDTO getTableMeta(String namespace, String tableName);
}
