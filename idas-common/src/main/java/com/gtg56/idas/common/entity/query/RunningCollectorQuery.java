package com.gtg56.idas.common.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RunningCollectorQuery {
    @ApiModelProperty("采集器名称模糊匹配")
    private String collectorNameKeyword;
    @ApiModelProperty("主机")
    private String host;
    @ApiModelProperty("只显示Leader采集器")
    private Boolean onlyLeader;
}
