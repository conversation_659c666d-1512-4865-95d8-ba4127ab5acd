package com.gtg56.idas.common.entity.query;

import java.util.Date;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 数据完整性检测结果明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckResultDetailQuery", description="数据完整性检测结果明细表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckResultDetailQuery extends SimpleQuery {

    @ApiModelProperty(value = "数据日期")
    private String tradeDateStart;

    @ApiModelProperty(value = "数据日期")
    private String tradeDateEnd;

    @ApiModelProperty(value = "数据完整性检测配置表明细ID")
    private Long dataCheckConfigDetailId;

    @ApiModelProperty(value = "数据权限ID")
    private String dataPermissionId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "缺失数据数量")
    private String missQuantity;

    @ApiModelProperty(value = "检测时间")
    private String checkTimeStart;

    @ApiModelProperty(value = "检测时间")
    private String checkTimeEnd;

    @ApiModelProperty(value = "复核时间")
    private Date recheckTimeStart;

    @ApiModelProperty(value = "复核时间")
    private Date recheckTimeEnd;

    @ApiModelProperty(value = "最后执行状态 ０　正常  １　异常")
    private String checkStatus;

    @ApiModelProperty(value = "创建用户编号 表：ba_user")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "修改用户编号 表：ba_user")
    private Long modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeStart;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeEnd;

    @ApiModelProperty(value = "是否删除")
    private Integer deletedFlag;

    @ApiModelProperty(value = "数据完整性检测配置名称")
    private String dataCheckConfigName;

    @ApiModelProperty(value = "数据完整性检测配置编码")
    private String dataCheckConfigCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @ApiModelProperty(value = "监测点名称")
    private String sensorName;

    @ApiModelProperty(value = "监测点编码")
    private String sensorCode;


}