package com.gtg56.idas.common.tool.http;

import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.util.DateUtil;
import com.jamesmurty.utils.XMLBuilder2;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.util.EntityUtils;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;

@Slf4j
public class ZJFDAReportClient extends BaseClient {
    
    public ZJFDAReportClient(String baseAddress) {
        super(baseAddress);
    }
    
    @Getter @Setter
    private String cropCode;
    @Getter @Setter
    private String password;
    @Getter @Setter
    private String code;
    
    public static final String SUCCESS_CODE = "999";
    
    public static final String
            DOC_TYPE_CHANGE_PASSWORD = "ChangePWD",
            DOC_TYPE_EDIT_ENTERPRISE_INFO = "EditEnterpriseInfo",
            DOC_TYPE_ADD_DEVICE = "AddDevice",
            DOC_TYPE_MODIFY_DEVICE = "ModifyDevice",
            DOC_TYPE_DELETE_DEVICE = "DeleteDevice",
            DOC_TYPE_UPLOAD_WAREHOUSE = "UploadWarehouse",
            DOC_TYPE_UPLOAD_HUMITURE_RECORD = "UploadHumitureRecord",
            DOC_TYPE_REPORT_STOP = "ReportStop";
    
    
    public static final Properties XML_PROP = new Properties();
    static {
        XML_PROP.put(javax.xml.transform.OutputKeys.INDENT, "yes");
        XML_PROP.put("{http://xml.apache.org/xslt}indent-amount", "2");
        XML_PROP.put(javax.xml.transform.OutputKeys.OMIT_XML_DECLARATION, "yes");
    }
    
    public Build buildUpload(String type) {
        long time = System.currentTimeMillis();
        String fileTypeNumber = String.format("%s-%s-%d",type, cropCode, time);
        XMLBuilder2 builder = XMLBuilder2.create("UPLOAD");
        String timeStr = DateUtil.ymdhms().format(new Date(time));
    
        builder.e("ENTERPRISE")
                .e("CODE").t(cropCode).up()
                .e("FILETYPENUMBER").t(fileTypeNumber).up()
                .e("FILECREATTIME").t(timeStr);
        return new Build(builder,fileTypeNumber);
    }
    
    public static String toXMLContent(XMLBuilder2 builder) {
        StringWriter writer = new StringWriter();
        builder.toWriter(writer,XML_PROP);
        return writer.toString();
    }
    
    private final ContentType stringContentType = ContentType.create("text/plain", StandardCharsets.UTF_8);
    
    public void upload(String xmlContent, String fileName) {
        String uri = getBaseAddress() + "/upload";
        HttpPost request = new HttpPost(uri);
        
        InputStreamBody xmlBody =
                new InputStreamBody(
                        new ByteArrayInputStream(xmlContent.getBytes()),
                        ContentType.APPLICATION_XML,
                        fileName
                );
        HttpEntity requestEntity = MultipartEntityBuilder.create()
                .addPart("xmlfile", xmlBody)
                .addPart("corp_id", new StringBody(cropCode, stringContentType))
                .addPart("corp_pwd", new StringBody(password, stringContentType))
                .addPart("uploadcode", new StringBody(code, stringContentType))
                .build();
    
        request.setEntity(requestEntity);
        
        try {
            HttpResponse response = httpClient.execute(request);
            
            if(HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                BizException bizException = new BizException("浙江药监上报失败 HTTP 状态码 " + response.getStatusLine().getStatusCode());
                log.warn("", bizException);
                throw bizException;
            }
            
            XMLBuilder2 parse = parseResponse(response);
            
            NodeList returnCode = parse.getDocument().getElementsByTagName("RETURNCODE");
            NodeList returnInfo = parse.getDocument().getElementsByTagName("RETURNINFO");
            String returnCodeContent = returnCode.item(0).getTextContent();
            
            if (SUCCESS_CODE.equals(returnCodeContent)) {
                log.info("浙江药监上报成功");
            } else {
                String returnInfoContent = returnInfo.item(0).getTextContent();
    
                BizException bizException = new BizException("浙江药监上报失败 返回码 " + returnCodeContent + "返回信息 " + returnInfoContent);
                log.warn("", bizException);
                throw bizException;
            }
        } catch (IOException e) {
            log.warn("浙江药监上报失败",e);
            throw new BizException(e);
        }
    }
    
    public List<QueryRecord> query(String fileTypeNumber) {
        long time = Long.parseLong(fileTypeNumber.split("-")[2]);
        String start = DateUtil.ymdhms().format(time - 5000);
        String end = DateUtil.ymdhms().format(time + 5000);
        return query(start, end, fileTypeNumber);
    }
    
    public List<QueryRecord> query(Date startDate, Date endDate, String fileTypeNumber) {
        return query(
                DateUtil.ymdhms().format(startDate),
                DateUtil.ymdhms().format(endDate),
                fileTypeNumber
        );
    }
    
    public List<QueryRecord> query(String startDate, String endDate, String fileTypeNumber) {
        List<QueryRecord> res = new LinkedList<>();
        String url = getBaseAddress() + "/query";
        
        try {
            URIBuilder uriBuilder = new URIBuilder(url)
                    .addParameter("corp_id", cropCode)
                    .addParameter("corp_pwd", password)
                    .addParameter("uploadcode", code)
                    .addParameter("date_from", startDate)
                    .addParameter("date_to", endDate);
    
            if (StringUtils.isNotBlank(fileTypeNumber)) {
                uriBuilder.addParameter("filetypenumber", fileTypeNumber);
            }
            URI uri = uriBuilder
                    .build();
    
            HttpGet get = new HttpGet(uri);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(5000)
                    .setSocketTimeout(5000)
                    .build();
            get.setConfig(requestConfig);
    
            HttpResponse response = httpClient.execute(get);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                log.warn("浙江药监查询失败 HTTP 状态码 {} ", response.getStatusLine().getStatusCode());
                return res;
            }
    
            XMLBuilder2 parse = parseResponse(response);
            NodeList recodes = parse.getDocument().getElementsByTagName("RECODE");
            int length = recodes.getLength();
            for (int i = 0; i < length; i++) {
                Node recode = recodes.item(i);
                ZJFDAReportClient.QueryRecord queryRecord = new ZJFDAReportClient.QueryRecord();
            
                NodeList childNodes = recode.getChildNodes();
                for (int j = 0 ; j < childNodes.getLength() ; j++ ) {
                    Node item = childNodes.item(j);
                    String nodeName = item.getNodeName();
                    String textContent = item.getTextContent();
                
                    switch (nodeName) {
                        case "FILETYPENUMER" : {
                            queryRecord.setFileTypeNumber(textContent);
                            break;
                        }
                        case "RESULT" : {
                            queryRecord.setResult(textContent);
                            break;
                        }
                        case "FILENAME" : {
                            queryRecord.setFileName(textContent);
                            break;
                        }
                        case "PARSEDATE" : {
                            queryRecord.setParseDate(textContent);
                            break;
                        }
                    }
                }
                res.add(queryRecord);
            }
        } catch (IOException e) {
            log.warn("浙江药监查询失败",e);
        } catch (URISyntaxException e) {
            log.warn("请求组装失败",e);
        }
        return res;
    }
    
    private static XMLBuilder2 parseResponse(HttpResponse response) throws IOException {
        HttpEntity responseEntity = response.getEntity();
        String content = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
        return XMLBuilder2.parse(content);
    }
    
    @Data
    public static class QueryRecord {
        private String fileTypeNumber;
        private String result;
        private String fileName;
        private String parseDate;
    }
    
    @Data
    @AllArgsConstructor
    public static class Build {
        private XMLBuilder2 builder;
        private String fileTypeNumber;
    }
    
}
