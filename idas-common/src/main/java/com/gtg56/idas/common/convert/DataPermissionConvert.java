package com.gtg56.idas.common.convert;


import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.dto.auth.DataRoleDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRole;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public class DataPermissionConvert {

    public static BiConsumer<List<DataPermission>, List<DataPermissionDTO>> toVO() {
        return (dataPermissionList, dataPermissionVOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataPermissionVOList.size(); i++) {
                DataPermission entity = dataPermissionList.get(i);
                DataPermissionDTO vo = dataPermissionVOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }

        };
    }

    public static BiConsumer<List<DataPermissionDTO>, List<DataPermission>> toEntity() {
        return (dataPermissionVOList, dataPermissionList) -> {
            //添加自定义转换操作

        };
    }

}