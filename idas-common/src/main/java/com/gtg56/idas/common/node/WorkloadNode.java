package com.gtg56.idas.common.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@NoArgsConstructor
public abstract class WorkloadNode implements Serializable {
    
    private static final long serialVersionUID = 6306764367626711588L;
    
    /**
     * 工作节点名称
     * @return 名称
     */
    public String nodeName() {
        return this.getClass().getSimpleName().replaceAll("_prov.+","");
    }
    
    /**
     * 接受参数
     * @return 参数表
     */
    public abstract List<NodeArg> acceptArgs();
    
    /**
     * 接受参数名
     * @return 参数名表
     */
    public List<String> acceptArgNames() {
        return acceptArgs().stream().map(NodeArg::getName).collect(Collectors.toList());
    }
    
    /**
     * 接受参数 [ name -> arg ]
     * @return 参数映射
     */
    protected Map<String,NodeArg> acceptArgsMap() {
        return acceptArgs().stream().collect(Collectors.toMap(NodeArg::getName,node -> node));
    }
    
    /**
     * 工作节点描述
     * @return 描述
     */
    public abstract String describe();
    
    /**
     * 输出 idas-inner-client cli 调用参数表 用例
     * @return 用例
     */
    public String usage() {
        StringBuilder sb = new StringBuilder(nodeName());
        for(NodeArg arg : acceptArgs()) {
            if(arg.getRequire()) {
                sb.append(" -").append(arg.getName()).append(" <");
                if(arg.getDefaultValue() != null) {
                    sb.append(" default ").append(arg.getDefaultValue());
                }
                sb.append(">");
            } else {
                sb.append(" [-").append(arg.getName()).append(" <");
                if(arg.getDefaultValue() != null) {
                    sb.append(" default ").append(arg.getDefaultValue());
                }
                sb.append(">]");
            }
        }
        return sb.toString();
    }
    
    
    /**
     * 校验参数是否符合要求
     * @param args 执行参数
     * @return 缺失的参数
     */
    public List<String> checkArgs(NodeArgsDTO args) {
        long numRequire = acceptArgs().stream()
                .filter(NodeArg::getRequire).count();
        
        if(numRequire == 0) return Collections.emptyList();
        
        return
            acceptArgs().stream()
                .filter(NodeArg::getRequire)
                .filter(arg -> !args.containsKey(arg.getName()) && StringUtils.isBlank(arg.getDefaultValue()))
                .map(NodeArg::getName)
                .collect(Collectors.toList());
    }
    
    /**
     * 校验参数并对空值设置默认值
     * @param args 执行参数
     */
    public void processArgs(NodeArgsDTO args) {
        List<String> missing = checkArgs(args);
        if(!missing.isEmpty()) throw WorkloadNodeException.missingRequireArguments(this,missing);
        
        acceptArgs().forEach(arg -> {
            if(args.get(arg.getName()) == null && arg.getDefaultValue() != null) {
                args.put(arg.getName(),arg.getDefaultValue());
            }
        });
    }
    
    public boolean active() {return true;}
    
    /** execution part **/
    
    private transient SparkSession spark;
    
    /**
     * 工作节点是否需要使用Spark。如果否，则不会授予Spark对象
     * @return 是否需要
     */
    public abstract boolean requireSpark();
    
    /**
     * 实例开始时，可以通过getSpark获得SparkSession
     * @return spark
     */
    protected final SparkSession getSpark() {
        return spark;
    }
    
    /**
     * 实例开始时，调度将授予实例Spark对象
     * @param spark spark对象
     */
    final void setSpark(SparkSession spark) {
        this.spark = spark;
    }
    
    /**
     * 实例结束后，将收回Spark
     */
    final void dropSpark() {
        this.spark = null;
    }
    
    private transient Logger log;
    
    protected final Logger getLog() { return log; }
    
    final void setLog(Logger log) { this.log = log; }
    
    /**
     * 执行逻辑
     *
     * 示例：
     * long cnt = getSpark().sql("xxx").count;
     * return dataReturn(cnt);
     * @param args 执行参数
     * @return 返回json string
     */
    public abstract String execution(NodeArgsDTO args);
    
    /**
     * 无返回
     * @return json string
     */
    protected static String noDataReturn() { return "{\"success\":true}";}
    
    /**
     * 有数据返回
     * @param data 数据
     * @return json string
     */
    protected static String dataReturn(Object data) {
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("data",data);
        return JSONObject.toJSONString(json,true);
    }
    
    /**
     * 执行前操作
     */
    public void beforeExecution(){}
    
    /**
     * 执行后操作
     */
    public void afterExecution(){}
    
    /**
     * 异常处理
     * @param e 异常
     */
    public void onException(Exception e) {}

}
