package com.gtg56.idas.common.util;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.List;

@Slf4j
public class JDBCUtil {
    
    public static boolean checkTableExists(String jdbcUrl,String user,String passwd,String table) {
        boolean exists = false;
        
        try (Connection conn = DriverManager.getConnection(jdbcUrl,user,passwd)) {
            String database = conn.getSchema();
            String sql = "SELECT count(1) as cnt FROM information_schema.TABLES where TABLE_SCHEMA = ? AND TABLE_NAME = ? ";
            PreparedStatement ps = conn.prepareStatement(sql);
            
            ps.setString(1,database);
            ps.setString(2,table);

            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                exists = rs.getLong("cnt") == 1;
            }
        } catch (SQLException e) {
            log.warn("checkTableExists fail", e);
        }
        return exists;
    }

    public static String getTableMinMaxId(String jdbcUrl, String user, String passwd, String table, String idCol, String where) {
        String ret = null;

        try (Connection conn = DriverManager.getConnection(jdbcUrl, user, passwd)) {
            String sql = "SELECT min(" + idCol + ") as min , max(" + idCol + ") as max from " + table + (StringUtils.isNotBlank(where) ? " WHERE " + where : "");
            PreparedStatement ps = conn.prepareStatement(sql);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                ret = String.format("%d,%d", rs.getLong("min"), rs.getLong("max"));
            }
        } catch (SQLException e) {
            log.warn("checkTableExists fail", e);
        }
        return ret;
    }

    public static String getTableMinMaxId(String jdbcUrl, String user, String passwd, String table, String idCol) {
        return getTableMinMaxId(jdbcUrl, user, passwd, table, idCol, null);
    }

    @SneakyThrows
    public static <T extends ImABean> List<T> resultSetToBeanList(ResultSet rs, Class<T> beanClass) {
        BeanListHandler<T> handler = new BeanListHandler<>(beanClass);
        return handler.handle(rs);
    }

    public static Long count(String jdbcUrl, String user, String passwd, String tableName) {
        Long ret = null;

        try (Connection conn = DriverManager.getConnection(jdbcUrl, user, passwd)) {
            String sql = "SELECT count(1) as cnt FROM " + tableName;
            ResultSet res = conn.prepareStatement(sql).executeQuery();

            while (res.next()) {
                ret = res.getLong("cnt");
            }
        } catch (SQLException e) {
            log.warn("count fail", e);
        }
        return ret;
    }

    public static void clearTable(String jdbcUrl, String user, String passwd, String tableName, String condition) {
        try (Connection conn = DriverManager.getConnection(jdbcUrl, user, passwd)) {
            String sql;
            if (StringUtils.isNotBlank(condition)) {
                sql = "DELETE FROM " + tableName + " WHERE " + condition;
            } else {
                sql = "TRUNCATE TABLE " + tableName;
            }
            conn.prepareStatement(sql).executeUpdate();
        } catch (SQLException e) {
            log.warn("clear fail", e);
        }
    }
}
