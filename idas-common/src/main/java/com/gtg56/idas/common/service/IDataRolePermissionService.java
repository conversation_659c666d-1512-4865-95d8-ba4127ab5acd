package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;

import java.util.List;

/**
 * <p>
 * 数据角色权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IDataRolePermissionService  extends IService<DataRolePermission> {

    /**
     *
     * @param dataRolePermission
     * @return
     */
    public DataRolePermission saveData(DataRolePermission dataRolePermission);

    /**
     *
     * @param dataRoleId
     * @param dataPermissionmVOList
     * @return
     */
    public Boolean saveData(String dataRoleId, List<DataPermissionDTO> dataPermissionmVOList);

    /**
     * 删除数据角色ID相关的关系
     * @param dataRoleId
     * @return
     */
    public Boolean deleteDataByRoleId(String dataRoleId);

    /**
     *
     * @param dataRolePermission
     */
    public DataRolePermission updateData(DataRolePermission dataRolePermission);
}
