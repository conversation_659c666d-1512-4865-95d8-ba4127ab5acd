package com.gtg56.idas.common.util;

import com.gargoylesoftware.htmlunit.BrowserVersion;
import com.gargoylesoftware.htmlunit.ScriptException;
import com.gargoylesoftware.htmlunit.WebClient;
import com.gargoylesoftware.htmlunit.html.HtmlPage;
import com.gargoylesoftware.htmlunit.javascript.JavaScriptErrorListener;

import java.net.MalformedURLException;
import java.net.URL;

public class HtmlUnitUtil {

    /**
     * 浏览器信息，从Chrome克隆一个
     */
    final private static BrowserVersion bv = BrowserVersion.BEST_SUPPORTED;

    static {
        java.util.logging.Logger.getLogger("com.gargoylesoftware").setLevel(java.util.logging.Level.OFF);
        java.util.logging.Logger.getLogger("org.apache.http.client").setLevel(java.util.logging.Level.OFF);
//
        org.apache.logging.log4j.LogManager.getLogger("com.gargoylesoftware").atLevel(org.apache.logging.log4j.Level.OFF);
        org.apache.logging.log4j.LogManager.getLogger("org.apache.http.client").atLevel(org.apache.logging.log4j.Level.OFF);
    }

    public static WebClient newWebClient() {
        return newWebClient(5000, 1000);
    }

    public static WebClient newWebClient(int timeout, int jsTimeout) {
        return newWebClient(timeout, jsTimeout, true);
    }

    public static WebClient newWebClient(int timeout, int jsTimeout, boolean enableJSEngine) {
        WebClient wc = new WebClient(bv);
        wc.getOptions().setUseInsecureSSL(true); // 允许使用不安全的SSL连接。如果不打开，站点证书过期的https将无法访问
        wc.getOptions().setJavaScriptEnabled(enableJSEngine); //启用JS解释器
        wc.getOptions().setCssEnabled(false); //禁用css支持
        // 禁用一些异常抛出
        wc.getOptions().setThrowExceptionOnScriptError(false);
        wc.getOptions().setThrowExceptionOnFailingStatusCode(false);
        wc.setJavaScriptErrorListener(new JavaScriptErrorListener() {
            @Override
            public void scriptException(HtmlPage page, ScriptException scriptException) {

            }

            @Override
            public void timeoutError(HtmlPage page, long allowedTime, long executionTime) {

            }

            @Override
            public void malformedScriptURL(HtmlPage page, String url, MalformedURLException malformedURLException) {

            }

            @Override
            public void loadScriptError(HtmlPage page, URL scriptUrl, Exception exception) {

            }

            @Override
            public void warn(String message, String sourceName, int line, String lineSource, int lineOffset) {

            }
        });

        wc.getOptions().setDoNotTrackEnabled(false); // 随请求发送DoNotTrack
        wc.setJavaScriptTimeout(jsTimeout);      // 设置JS超时
        wc.getOptions().setTimeout(timeout); //设置连接超时时间

        return wc;
    }
}
