package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.core.jdbc.IMapper;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据权限 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface DataPermissionMapper extends IMapper<DataPermission> {

    int updateById(DataPermission entity);

    /*×
     *  通过数据角色ID查找数据权限列表
     */
    List<DataPermissionDTO> listVOByRoleId(@Param("roleId")String roleId);

    /*×
     *  通过权限类型查询数据权限
     */
    List<DataPermissionDTO> listVOByType(@Param("type")String type);


    /**
     * 通过用户ID查找数据权限列表
     * @param userId 用户ID
     * @param type 权限类型
     * @return
     */
    List<DataPermissionDTO> listVOByUserIdAndType(@Param("userId")String userId, @Param("type")String type);

    /*×
     *  查找新增的仓库类型数据权限
     */
    List<DataPermission> findNewPermissionForWarehouse();

    /*×
     *  查找新增的库房类型数据权限
     */
    List<DataPermission> findNewPermissionForRegion();

    /*×
     *  查找新增的测点终端类型数据权限
     */
    List<DataPermission> findNewPermissionForSensor();

}
