package com.gtg56.idas.common.entity.dto.sensor;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 数据完整性检测配置明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="WarehouseSensorDTO", description="仓库温湿度传感器配置维度表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WarehouseSensorDTO extends SimpleVO implements ImABean {


    private static final long serialVersionUID = 1317192388801889255L;

    @ApiModelProperty("温湿度监控系统厂商编码")
    private String corpCode;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("区域编码（如无区域的厂商，则全部为ANY）")
    private String regionCode;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("原始传感器编码（厂商定义）")
    private String sensorCodeOrigin;

    @ApiModelProperty("传感器编码 [warehouseCode]-[sensorCodeOrigin]")
    private String sensorCode;

    @ApiModelProperty("传感器名称")
    private String sensorName;

    @ApiModelProperty("传感器类型（厂商定义）")
    private String sensorType;

    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHighLimit;

    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLowLimit;

    @ApiModelProperty("湿度上限")
    private BigDecimal humidityHighLimit;

    @ApiModelProperty("湿度下限")
    private BigDecimal humidityLowLimit;

    @ApiModelProperty("传感器功能（SENSOR：仓库测点；CAR：冷藏车；BOX：保温箱；OTHER：其他）")
    private String sensorFunction;

    @ApiModelProperty("状态（0：停用；1：启用；）")
    private String status;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}