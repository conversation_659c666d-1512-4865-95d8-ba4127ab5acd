package com.gtg56.idas.common.tool.compress;

import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;

public class CompressorFactory {
    
    public static Compressor forName(String name) {
        return Compressors.valueOf(name.toUpperCase()).getCompressor();
    }
    
    @SneakyThrows
    public static void main(String[] args) {
        System.out.println("COMPRESSORS UNIT TEST START\n");
        
        String plain = RandomStringUtils.random(65536, false, true);
        System.out.println("plain data length : " + plain.getBytes().length);
        
        for (Compressors compressors : Compressors.values()) {
            Compressor compressor = compressors.getCompressor();
            System.out.println("now testing [" + compressor.name() + "] compressor");
            long start = System.currentTimeMillis();
            byte[] compressed = compressor.compress(plain.getBytes());
            System.out.println("compress done , cost " + (System.currentTimeMillis() - start) + " ms ");
            System.out.println("compressed length " + compressed.length + ", compress rate " + ((compressed.length / (double) plain.getBytes().length) * 100));
            start = System.currentTimeMillis();
            byte[] decompress = compressor.decompress(compressed);
            System.out.println("decompress done , cost " + (System.currentTimeMillis() - start) + " ms ");
            boolean equals = plain.equals(new String(decompress));
            System.out.println("test result is " + equals);
            
            System.out.println();
        }
    }
    
    public enum Compressors {
        PLAIN(new PlainCompressor()),
        GZIP(new GZipCompressor()),
        LZO(new LZOCompressor()),
        SNAPPY(new SnappyCompressor());
        
        @Getter
        private Compressor compressor;
        
        Compressors(Compressor compressor) {
            this.compressor = compressor;
        }
    }
}
