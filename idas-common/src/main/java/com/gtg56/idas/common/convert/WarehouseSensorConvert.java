package com.gtg56.idas.common.convert;

import com.gtg56.idas.common.entity.dto.sensor.WarehouseSensorDTO;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public class WarehouseSensorConvert {

    public static BiConsumer<List<WarehouseSensor>, List<WarehouseSensorDTO>> toVO() {
        return (WarehouseSensorList, WarehouseSensorDTOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < WarehouseSensorDTOList.size(); i++) {
                WarehouseSensor entity = WarehouseSensorList.get(i);
                WarehouseSensorDTO vo = WarehouseSensorDTOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<WarehouseSensorDTO>, List<WarehouseSensor>> toEntity() {
        return (WarehouseSensorDTOList, WarehouseSensorList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < WarehouseSensorDTOList.size(); i++) {
                WarehouseSensor entity = WarehouseSensorList.get(i);
                WarehouseSensorDTO vo = WarehouseSensorDTOList.get(i);
                if(vo != null) {
                    entity.setId(Long.valueOf(vo.getId()));
                }
            }
        };
    }

}