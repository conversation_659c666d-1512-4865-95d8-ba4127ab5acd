package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.entity.dto.sensor.SensorStatResultDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.entity.query.SensorCompare;
import com.gtg56.idas.common.entity.query.WarehouseStatQuery;
import com.gtg56.idas.common.mapper.DataCheckResultMapper;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.mapper.DataPermissionMapper;
import com.gtg56.idas.common.service.IDataCheckResultService;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SpringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 数据完整性检测结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service("dataCheckResultService")
public class DataCheckResultServiceImpl extends AbstractService<DataCheckResult, DataCheckResultMapper> implements IDataCheckResultService {


    /**
     * @param dataCheckResult
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckResult saveData(DataCheckResult dataCheckResult) {
        dataCheckResult.setId(null);
        dataCheckResult.baseSet();
        dataCheckResult.setDeletedFlag(0L);
        save(dataCheckResult);
        return dataCheckResult;
    }

    /**
     * @param dataCheckResult
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckResult updateData(DataCheckResult dataCheckResult) {
        dataCheckResult.baseSet();
        update(dataCheckResult);
        return dataCheckResult;
    }

}
