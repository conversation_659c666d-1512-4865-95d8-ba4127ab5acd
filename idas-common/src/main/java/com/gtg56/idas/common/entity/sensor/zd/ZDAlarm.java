package com.gtg56.idas.common.entity.sensor.zd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("泽大报警记录")
public class ZDAlarm {

    @ApiModelProperty("设备代码")
    private String sbdm;
    @ApiModelProperty("温度值")
    private String wdz;
    @ApiModelProperty("湿度值")
    private String sdz;
    @ApiModelProperty("经度值")
    private String lng;
    @ApiModelProperty("纬度值")
    private String lat;
    @ApiModelProperty("采集时间")
    private String cjsj;
    @ApiModelProperty("记录状态描述")
    private String jlzt;
    @ApiModelProperty("报警处理时间")
    private String clsj;
    @ApiModelProperty("报警处理人")
    private String clry;
    @ApiModelProperty("报警处理措施")
    private String clcs;
}
