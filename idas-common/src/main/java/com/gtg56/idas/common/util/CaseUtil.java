package com.gtg56.idas.common.util;

import com.google.common.base.CaseFormat;

public class CaseUtil {
    
    /**
     * 下划线转小驼峰
     * @param underscore 下划线
     * @return
     */
    public static String underscoreToCamel(String underscore) {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,underscore.toLowerCase());
    }
    
    /**
     * 小驼峰转下划线
     * @param lowerCamel 小驼峰
     * @return
     */
    public static String lowerCamelToUnderscore(String lowerCamel) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE,lowerCamel);
    }
    
    public static String upperCamelToLowerCamel(String upperCamel) {
        return CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL,upperCamel);
    }
    
    public static String lowerCamelToUpperCamel(String lowerCamel) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_CAMEL,lowerCamel);
    }
    
    
    
    public static void main(String[] args) {
        System.out.println(underscoreToCamel("TMP_ABC")); // tmpAbc
        System.out.println(underscoreToCamel("tmp_ABC")); // tmpAbc
        System.out.println(underscoreToCamel("TtT_AbC")); // tttAbc
    
        System.out.println(lowerCamelToUnderscore("camelCase")); // camel_case
        System.out.println(lowerCamelToUnderscore("camelCaSe")); // camel_ca_se
    }
}
