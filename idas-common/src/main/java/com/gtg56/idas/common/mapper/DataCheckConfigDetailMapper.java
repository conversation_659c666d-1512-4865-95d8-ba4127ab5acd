package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.core.jdbc.IMapper;

import java.util.List;

/**
 * <p>
 * 数据完整性检测配置明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface DataCheckConfigDetailMapper extends IMapper<DataCheckConfigDetail> {

    int updateById(DataCheckConfigDetail entity);

    /**
     * 通过 配置表ID 查询明细 dataCheckConfigID
     * @param dataCheckConfigId
     */
    List<DataCheckConfigDetailDTO> findByConfigId(Long dataCheckConfigId);

}
