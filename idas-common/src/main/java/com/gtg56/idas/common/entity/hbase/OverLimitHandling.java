package com.gtg56.idas.common.entity.hbase;

import com.gtg56.idas.common.core.hbase.HBaseResultBeanMapping;
import com.gtg56.idas.common.entity.base.HBaseBaseDO;
import com.gtg56.idas.common.entity.hbase.key.OverLimitHandingKey;
import com.gtg56.idas.common.util.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@HBaseResultBeanMapping(nameSpace = "idas", tableName = "warehouse_overlimit_handling", columnFamily = "data")
public class OverLimitHandling extends HBaseBaseDO<OverLimitHandingKey> {
    private static final long serialVersionUID = -4456848853583856449L;
    
    @ApiModelProperty("温湿度监控系统厂商编码")
    private String corpCode;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("传感器编码")
    private String sensorCode;
    @ApiModelProperty("记录时间")
    private Date recordTime;
    @ApiModelProperty("处理时间")
    private Date handleTime;
    @ApiModelProperty("处理人")
    private String principal;
    @ApiModelProperty("处理意见")
    private String suggestion;
    @ApiModelProperty("温度")
    private BigDecimal temperature;
    @ApiModelProperty("湿度")
    private BigDecimal humidity;
    @ApiModelProperty("温度装")
    private String temperatureStatus;
    @ApiModelProperty("湿度状态")
    private String humidityStatus;
    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHighLimit;
    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLowLimit;
    @ApiModelProperty("湿度上限")
    private BigDecimal humidityHighLimit;
    @ApiModelProperty("湿度下限")
    private BigDecimal humidityLowLimit;
    
    @Override
    public OverLimitHandingKey transformRowKey() {
        if (getRowKey() == null) return null;
        String str = new String(getRowKey());
        String[] split = str.split("-");
        AssertUtil.isTrue(split.length == 2, "非法行键" + str);
        return new OverLimitHandingKey(split[0], new Date(Long.parseLong(split[1])));
    }
    
    @Override
    public byte[] toRowKey(OverLimitHandingKey key) {
        return String.format("%s-%d", key.getSensorCode(), key.getRecordTime().getTime()).getBytes();
    }
}
