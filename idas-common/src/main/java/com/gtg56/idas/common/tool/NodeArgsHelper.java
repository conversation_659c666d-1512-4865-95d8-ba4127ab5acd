package com.gtg56.idas.common.tool;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import lombok.SneakyThrows;
import org.apache.commons.cli.*;

import java.util.List;

public class NodeArgsHelper {
    
    private static final long serialVersionUID = -414757364435690813L;
    private Options options;
    private List<NodeArg> acceptArgs;
    
    public NodeArgsHelper(WorkloadNode node) {
        this.acceptArgs = node.acceptArgs();
        this.options = toOptions(node);
    }
    
    private static Options toOptions(WorkloadNode node) {
        List<NodeArg> args = node.acceptArgs();
        Options options = new Options();
        for(NodeArg arg : args) {
            Option.Builder builder = Option.builder();
            builder.argName(arg.getName()).longOpt(arg.getName()).required(arg.getRequire()).hasArg();
            options.addOption(builder.build());
        }
        return options;
    }
    
    @SneakyThrows
    public NodeArgsDTO toDTO(String[] args) {
        CommandLineParser parser = new DefaultParser();
        CommandLine parse = parser.parse(options, args);
        
        NodeArgsDTO dto = new NodeArgsDTO();
        for(NodeArg arg : acceptArgs) {
            if(options.hasOption(arg.getName())) {
                String optionValue = parse.getOptionValue(arg.getName());
                dto.put(arg.getName(),optionValue);
            }
        }
        return dto;
    }
}
