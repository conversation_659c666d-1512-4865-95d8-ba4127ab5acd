package com.gtg56.idas.common.entity.dto.sensor;

import java.util.Date;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.transform.DictionaryField;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 数据完整性检测配置明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckConfigDetailDTO", description="数据完整性检测配置明细表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckConfigDetailDTO extends SimpleVO implements ImABean {


    @ApiModelProperty(value = "数据完整性检测配置ID")
    private Long dataCheckConfigId;


    @ApiModelProperty(value = "数据权限ID")
    private Long dataPermissionId;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库房编码")
    private String regionCode;

    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @ApiModelProperty(value = "测点终端编码")
    private String sensorCode;

    @ApiModelProperty(value = "测点终端名称")
    private String sensorName;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "监测点明细")
    private List<WarehouseSensorDTO> warehouseSensorDTOList;
}