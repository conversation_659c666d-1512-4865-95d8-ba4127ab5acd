package com.gtg56.idas.common.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.dto.FDAReportDTO;
import com.gtg56.idas.common.entity.jdbc.FDAReport;
import com.gtg56.idas.common.entity.query.FDAReportQuery;
import com.gtg56.idas.common.mapper.FDAReportMapper;
import com.gtg56.idas.common.service.IFDAReportService;
import com.gtg56.idas.common.tool.compress.CompressorFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("fdaReportService")
public class FDAReportServiceImpl extends AbstractService<FDAReport, FDAReportMapper>
        implements IFDAReportService {
    
    @Override
    public PageInfo<FDAReport> findPage(FDAReportQuery query) {
        Page<FDAReport> page;
        if (query.getPageNum() != null && query.getPageSize() != null) {
            page = PageHelper.startPage(query.getPageNum(), query.getPageSize());
        } else {
            page = new Page<>();
        }
        
        List<FDAReport> res = mapper.find(query);
        PageInfo<FDAReport> pageInfo = page.toPageInfo();
        pageInfo.setList(res);
        return pageInfo;
    }
    
    @Override
    public FDAReportDTO decompress(FDAReport report) {
        FDAReportDTO dto = new FDAReportDTO();
        BeanUtils.copyProperties(report, dto);
        byte[] decompress = CompressorFactory.forName(report.getReportContentCompress()).decompress(report.getReportContent());
        dto.setReportContent(new String(decompress));
        return dto;
    }
}
