package com.gtg56.idas.common.tool.http;

import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.consts.TSDBAggregator;
import com.gtg56.idas.common.consts.TSDBTimeUnit;
import com.gtg56.idas.common.entity.dto.sensor.SensorRecordDTO;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.SensorCompare;
import com.gtg56.idas.common.entity.tsdb.TSDBBaseEntity;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.entity.tsdb.query.TSDBQueryDTO;
import com.gtg56.idas.common.entity.tsdb.response.TSDBQueryResponseDTO;
import com.gtg56.idas.common.util.MsgDigestUtil;
import com.gtg56.idas.common.util.RedisUtil;
import com.gtg56.idas.common.util.SpringUtil;
import javafx.util.Duration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class WarehouseSensorClient extends OpenTSDBClient {
    public WarehouseSensorClient(String baseAddress) {
        super(baseAddress);
    }

    @Override
    public void put(TSDBBaseEntity<?, ?> entity) {
        cachePut((WarehouseSensorRecord) entity);
        super.put(entity);
    }

    private static final String KEY_PREFIX = "WSR_CACHE_";

    private void cachePut(WarehouseSensorRecord entity) {
        try {
            RedisUtil redis = SpringUtil.getBean("redisUtil", RedisUtil.class);
            String key = buildCacheKey(entity.getTags().getWarehouseCode(), entity.getTags().getRegionCode(), entity.getTags().getSensorCode(), entity.getTags().getType());
            String value = entity.getTimestamp() + "_" + entity.getValue();
            //已有cache
            String cache = redis.get(key);
            if (StringUtils.isNotBlank(cache)) {
                String[] s = cache.split("_");
                long cacheTime = Long.parseLong(s[0]);
                if (cacheTime < entity.getTimestamp()) {
                    log.info("WarehouseSensorClient cache put {}", entity.toPutBody());
                    redis.set(key, value, 120);
                }
                return;
            }
            //
            redis.set(key, value, 120);
        } catch (Exception e) {
            log.warn("WarehouseSensorClient cache put failed", e);
        }
    }

    private String buildCacheKey(String warehouseCode, String regionCode, String sensorCode, String type) {
        return KEY_PREFIX + MsgDigestUtil.getMD5(warehouseCode + regionCode + sensorCode + type).getDigest();
    }

    public SensorRecordDTO getRecordAt(WarehouseSensor sensor, Date time) {
        Date start = new Date(time.getTime() - (long) Duration.minutes(2).toMillis());
        return getOneRecord(sensor, new TSDBQueryDTO(start).setEnd(time));
    }

    public SensorRecordDTO getLastRecord(WarehouseSensor sensor) {
        return getOneRecord(sensor, new TSDBQueryDTO(2, TSDBTimeUnit.MINUTE));
    }

    private SensorRecordDTO getOneRecord(WarehouseSensor sensor, TSDBQueryDTO query) {
        query.addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_TEMPERATURE, TSDBAggregator.NONE))
                .addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_HUMIDITY, TSDBAggregator.NONE));

        List<TSDBQueryResponseDTO> res = query(query);

        SensorRecordDTO ret = new SensorRecordDTO();
        Map<Integer, BigDecimal> tempDps = findTypeDPS(res, SensorConsts.TYPE_TEMPERATURE);
        Map<Integer, BigDecimal> humiDps = findTypeDPS(res, SensorConsts.TYPE_HUMIDITY);

        long time = 0L;

        if (!tempDps.isEmpty() || !humiDps.isEmpty()) {
            if (!tempDps.isEmpty()) {
                int t = tempDps.keySet().stream().mapToInt(k -> k).max().orElse(0);
                if (t > 0) {
                    ret.setTemperature(tempDps.get(t));
                    time = Math.max(time, t);
                }
            }
            if (!humiDps.isEmpty()) {
                int t = humiDps.keySet().stream().mapToInt(k -> k).max().orElse(0);
                if (t > 0) {
                    ret.setHumidity(humiDps.get(t));
                    time = Math.max(time, t);
                }
            }
        }

        ret.setRecordTime(time > 0 ? new Date(time * 1000L) : null)
                .setTemperatureStatus(SensorConsts.toStatus(
                        ret.getTemperature(),
                        sensor.getTemperatureHighLimit(),
                        sensor.getTemperatureLowLimit()
                ))
                .setHumidityStatus(SensorConsts.toStatus(
                        ret.getHumidity(),
                        sensor.getHumidityHighLimit(),
                        sensor.getHumidityLowLimit()
                ));

        BeanUtils.copyProperties(sensor, ret);

        return ret;
    }

    public List<SensorRecordDTO> getRecords(WarehouseSensor sensor, Date start, Date end) {
        return getRecords(sensor, start, end, null);
    }

    public List<SensorRecordDTO> getRecords(WarehouseSensor sensor, Date start, Date end, String downSample) {
        return getRecords(sensor, start, end, TSDBAggregator.NONE, downSample);
    }

    public List<SensorRecordDTO> getRecords(WarehouseSensor sensor, Date start, Date end, TSDBAggregator aggregator, String downSample) {
        TSDBQueryDTO query = new TSDBQueryDTO(start).setEnd(end)
                .addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_TEMPERATURE, Collections.emptyMap(), aggregator, downSample))
                .addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_HUMIDITY, Collections.emptyMap(), aggregator, downSample));

        List<TSDBQueryResponseDTO> res = query(query);

        Map<Integer, BigDecimal> tempDps = findTypeDPS(res, SensorConsts.TYPE_TEMPERATURE);
        Map<Integer, BigDecimal> humiDps = findTypeDPS(res, SensorConsts.TYPE_HUMIDITY);

        Set<Integer> times = new TreeSet<>();

        if (tempDps.isEmpty() && humiDps.isEmpty()) return Collections.emptyList();

        if (!tempDps.isEmpty()) times.addAll(tempDps.keySet());
        if (!humiDps.isEmpty()) times.addAll(humiDps.keySet());

        return times.parallelStream().map(time -> {
            SensorRecordDTO ret = new SensorRecordDTO();
            ret.setTemperature(!tempDps.isEmpty() ? tempDps.get(time) : null);
            ret.setHumidity(!humiDps.isEmpty() ? humiDps.get(time) : null);
            ret.setRecordTime(new Date(time * 1000L))
                    .setTemperatureStatus(SensorConsts.toStatus(
                            ret.getTemperature(),
                            sensor.getTemperatureHighLimit(),
                            sensor.getTemperatureLowLimit()
                    ))
                    .setHumidityStatus(SensorConsts.toStatus(
                            ret.getHumidity(),
                            sensor.getHumidityHighLimit(),
                            sensor.getHumidityLowLimit()
                    ));
            BeanUtils.copyProperties(sensor, ret);

            return ret;
        }).collect(Collectors.toList());
    }

    public List<SensorRecordDTO> listOverLimit(WarehouseSensor sensor, Date start, Date end) {
        TSDBQueryDTO tsdbQueryDTO = new TSDBQueryDTO(start)
                .setEnd(end)
                .addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_TEMPERATURE, TSDBAggregator.NONE))
                .addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_HUMIDITY, TSDBAggregator.NONE));

        List<TSDBQueryResponseDTO> res = query(tsdbQueryDTO);
        Map<Integer, BigDecimal> tempDps = findTypeDPS(res, SensorConsts.TYPE_TEMPERATURE);
        Map<Integer, BigDecimal> humiDps = findTypeDPS(res, SensorConsts.TYPE_HUMIDITY);

        if (!tempDps.isEmpty() && !humiDps.isEmpty()) {
            Map<Integer, BigDecimal> tempOver = findOverValue(tempDps, sensor.getTemperatureHighLimit(), sensor.getTemperatureLowLimit());
            Map<Integer, BigDecimal> humiOver = findOverValue(humiDps, sensor.getHumidityHighLimit(), sensor.getHumidityLowLimit());

            Set<Integer> times = new TreeSet<>();
            times.addAll(tempOver.keySet());
            times.addAll(humiOver.keySet());

            return times.stream().map(time -> {
                SensorRecordDTO dto = new SensorRecordDTO();
                BeanUtils.copyProperties(sensor, dto);
                dto.setTemperature(tempDps.get(time));
                dto.setHumidity(humiDps.get(time));
                dto.setRecordTime(new Date(time * 1000L));
                dto.setTemperatureStatus(SensorConsts.toStatus(dto.getTemperature(), sensor.getTemperatureHighLimit(), sensor.getTemperatureLowLimit()));
                dto.setHumidityStatus(SensorConsts.toStatus(dto.getHumidity(), sensor.getHumidityHighLimit(), sensor.getHumidityLowLimit()));
                return dto;
            }).filter(dto -> !(SensorConsts.STATUS_NORMAL.equals(dto.getTemperatureStatus()) && SensorConsts.STATUS_NORMAL.equals(dto.getHumidityStatus())))
                    .collect(Collectors.toList());

        }
        return Collections.emptyList();
    }

    private Map<Integer, BigDecimal> findOverValue(Map<Integer, BigDecimal> dps, BigDecimal highLimit, BigDecimal lowLimit) {
        return dps.entrySet().parallelStream()
                .filter(kv -> kv.getValue().doubleValue() > highLimit.doubleValue() || kv.getValue().doubleValue() < lowLimit.doubleValue())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Map<Integer, BigDecimal> findTypeDPS(List<TSDBQueryResponseDTO> responses, String type) {
        Map<Integer, BigDecimal> ret = new TreeMap<>();
        List<Map<Integer, BigDecimal>> dpss = responses.stream()
                .filter(r -> SensorConsts.METRIC.equals(r.getMetric()))
                .filter(r -> type.equals(r.getTags().get(SensorConsts.TAG_TYPE)))
                .map(TSDBQueryResponseDTO::getDps)
                .collect(Collectors.toList());

        if (dpss.size() == 1) {
            return dpss.get(0);
        }

        Set<Integer> times = dpss.stream().flatMap(dps -> dps.keySet().stream()).collect(Collectors.toSet());

        times.forEach(time -> {
            BigDecimal value = dpss.stream().map(dps -> dps.get(time)).filter(Objects::nonNull).findFirst().orElse(null);
            ret.put(time, value);
        });

        return ret;
    }

    private TSDBQueryDTO.QueryMetric queryMetric(WarehouseSensor sensor,
                                                 String type,
                                                 TSDBAggregator aggregator) {

        return queryMetric(sensor, type, new HashMap<>(), aggregator);
    }

    private TSDBQueryDTO.QueryMetric queryMetric(WarehouseSensor sensor,
                                                 String type,
                                                 Map<String, String> extTags,
                                                 TSDBAggregator aggregator) {
        return queryMetric(sensor, type, extTags, aggregator, null);
    }

    private TSDBQueryDTO.QueryMetric queryMetric(WarehouseSensor sensor,
                                                 String type,
                                                 Map<String, String> extTags,
                                                 TSDBAggregator aggregator,
                                                 String downSample) {
        return new TSDBQueryDTO.QueryMetric()
                .setMetric(SensorConsts.METRIC)
                .setAggregator(aggregator)
                .setDownsample(downSample)
                .addTag(SensorConsts.TAG_TYPE, type)
                .addTag(SensorConsts.TAG_WAREHOUSE_CODE, sensor.getWarehouseCode())
                .addTag(SensorConsts.TAG_REGION_CODE, sensor.getRegionCode())
                .addTag(SensorConsts.TAG_SENSOR_CODE, sensor.getSensorCode())
                .addTags(extTags);
    }

    private String getLastRecordFromCache(WarehouseSensor sensor, String type) {
        try {
            RedisUtil redis = SpringUtil.getBean("redisUtil", RedisUtil.class);
            String key = buildCacheKey(sensor.getWarehouseCode(), sensor.getRegionCode(), sensor.getSensorCode(), type);
            if (redis.hasKey(key)) {
                return redis.get(key);
            }
        } catch (Exception e) {
            log.warn("WarehouseSensorClient get cache failed", e);
        }
        return null;
    }

    public List<SensorRecordDTO> getLastRecordBatch(List<WarehouseSensor> sensors, boolean cacheOnly) {
        Map<SensorCompare, Map<Integer, BigDecimal>>
                tempDps = new ConcurrentHashMap<>(),
                humiDps = new ConcurrentHashMap<>();

        List<WarehouseSensor> notHitCache = new LinkedList<>();

        sensors.parallelStream().forEach(sensor -> {
            SensorCompare sensorCompare = new SensorCompare(sensor.getWarehouseCode(), sensor.getRegionCode(), sensor.getSensorCode());
            boolean hit = false;
            String tempCache = getLastRecordFromCache(sensor, SensorConsts.TYPE_TEMPERATURE);
            if (StringUtils.isNotBlank(tempCache)) {
                hit = true;

                String[] s = tempCache.split("_");
                Integer t = (int) (Long.parseLong(s[0]));
                BigDecimal v = new BigDecimal(s[1]);
                Map<Integer, BigDecimal> m = new HashMap<>();
                m.put(t, v);
                tempDps.put(sensorCompare, m);
            }

            String humCache = getLastRecordFromCache(sensor, SensorConsts.TYPE_HUMIDITY);
            if (StringUtils.isNotBlank(humCache)) {
                hit = true;

                String[] s = humCache.split("_");
                Integer t = (int) (Long.parseLong(s[0]));
                BigDecimal v = new BigDecimal(s[1]);
                Map<Integer, BigDecimal> m = new HashMap<>();
                m.put(t, v);
                humiDps.put(sensorCompare, m);
            }

            if (!hit) {
                notHitCache.add(sensor);
            }

        });

        if (!cacheOnly) {
            TSDBQueryDTO query = new TSDBQueryDTO(2, TSDBTimeUnit.MINUTE);
            notHitCache.forEach(sensor -> {
                query.addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_TEMPERATURE, TSDBAggregator.LAST));
                query.addQueryMetric(queryMetric(sensor, SensorConsts.TYPE_HUMIDITY, TSDBAggregator.LAST));
            });

            List<TSDBQueryResponseDTO> result = query(query);

            result.parallelStream().forEach(res -> {
                Map<String, String> tags = res.getTags();
                String warehouseCode = tags.get(SensorConsts.TAG_WAREHOUSE_CODE);
                String regionCode = tags.get(SensorConsts.TAG_REGION_CODE);
                String sensorCode = tags.get(SensorConsts.TAG_SENSOR_CODE);
                String type = tags.get(SensorConsts.TAG_TYPE);

                if (SensorConsts.TYPE_TEMPERATURE.equals(type)) {
                    tempDps.put(new SensorCompare(warehouseCode, regionCode, sensorCode), res.getDps());
                } else {
                    humiDps.put(new SensorCompare(warehouseCode, regionCode, sensorCode), res.getDps());
                }
            });
        }

        Map<Integer, BigDecimal> empty = new HashMap<>();
        Map<SensorCompare, WarehouseSensor> map = sensors.parallelStream().collect(Collectors.toMap(SensorCompare::new, sensor -> sensor));

        return map.keySet().parallelStream().map(compare -> {
            WarehouseSensor sensor = map.get(compare);
            Map<Integer, BigDecimal> td = tempDps.getOrDefault(compare, empty);
            Map<Integer, BigDecimal> hd = humiDps.getOrDefault(compare, empty);

            SensorRecordDTO dto = new SensorRecordDTO();
            BeanUtils.copyProperties(sensor, dto);

            Integer time = Stream.concat(td.keySet().stream(), hd.keySet().stream())
                    .parallel()
                    .max(Integer::compareTo)
                    .orElse(null);

            if (time != null) {
                dto.setRecordTime(new Date(time * 1000L));
                BigDecimal temperature = td.get(time);
                dto.setTemperature(temperature);
                BigDecimal humidity = hd.get(time);
                dto.setHumidity(humidity);
            }
            dto.setTemperatureStatus(SensorConsts.toStatus(dto.getTemperature(), sensor.getTemperatureHighLimit(), sensor.getTemperatureLowLimit()));
            dto.setHumidityStatus(SensorConsts.toStatus(dto.getHumidity(), sensor.getHumidityHighLimit(), sensor.getHumidityLowLimit()));

            //泽大保温箱的状态设置为未知，这是泽大保温箱无湿度数据
            if(SensorConsts.BOX_ZD_REGION.equals(dto.getRegionCode())){
                dto.setHumidityStatus(SensorConsts.STATUS_UNKNOWN);
            }

            return dto;
        })
                .sorted(RECORD_COMPARATOR)
                .collect(Collectors.toList());
    }

    public List<SensorRecordDTO> getLastRecordBatch(List<WarehouseSensor> sensors) {
        return getLastRecordBatch(sensors, true);
    }

    private static final Comparator<SensorRecordDTO> RECORD_COMPARATOR =
            Comparator.comparing(SensorRecordDTO::getRecordTime, Comparator.nullsFirst(Date::compareTo))
                    .reversed()
                    .thenComparing(SensorRecordDTO::getSensorCode);

}
