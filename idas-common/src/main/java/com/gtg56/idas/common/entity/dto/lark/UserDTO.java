package com.gtg56.idas.common.entity.dto.lark;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> 2020-02-01
 */
@Data
@Accessors(chain = true)
public class UserDTO implements ImABean {
    private static final long serialVersionUID = 3623364395178066497L;
    
    private String id;
    
    private String username;
    
    private String name;
    
    private String employeeId;
    
    private String companyType;
    
    private String companyId;
    
    private String deptId;
    
    private List<String> authorities;
    
    public boolean isAdmin() {
        return "1".equals(id) || "admin".equalsIgnoreCase(username);
    }
}
