/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: StringUtils
 * @Author: zhangchuyi
 */
package com.gtg56.idas.common.util;


import org.springframework.lang.Nullable;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 字符串工具类
 * <p>
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION
 * @Author: zhangchuyi
 * @Date: 2018/9/5 15:36
 * @Since: Bamboo V00.00.001
 */
public class StringUtil extends org.apache.commons.lang3.StringUtils {

    public static boolean isEmpty(@Nullable Object str) {
        return str == null || "".equals(str);
    }

    public static boolean isBlank(@Nullable Object str) {
        return str == null || isBlank(str.toString());
    }

    public static boolean isNotBlank(@Nullable Object str) {
        return str != null && isNotBlank(str.toString());
    }

    /**
     * 首字母转小写
     * @param s
     * @return
     */
    public static String toLowerCaseFirstOne(String s){
        if(StringUtil.isEmpty(s)){
            return s;
        }

        if(Character.isLowerCase(s.charAt(0))) {
            return s;
        } else{
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }


    /**
     * 首字母转大写
     * @param s
     * @return
     */
    public static String toUpperCaseFirstOne(String s){
        if(StringUtil.isEmpty(s)){
            return s;
        }

        if(Character.isUpperCase(s.charAt(0))){
            return s;
        }
        else{
            return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    /**
     * @param htmlStr
     * @return 删除Html标签
     * <AUTHOR>
     */
    public static String delHTMLTag(String htmlStr) {

        if(StringUtil.isEmpty(htmlStr)){
            return htmlStr;
        }

        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
        String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; // 定义style的正则表达式
        String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
        String regEx_space = "\\s*|\t|\r|\n";// 定义空格回车换行符
        String regEx_w = "<w[^>]*?>[\\s\\S]*?<\\/w[^>]*?>";//定义所有w标签

        Pattern p_w = Pattern.compile(regEx_w, Pattern.CASE_INSENSITIVE);
        Matcher m_w = p_w.matcher(htmlStr);
        htmlStr = m_w.replaceAll(""); // 过滤script标签


        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        htmlStr = m_script.replaceAll(""); // 过滤script标签


        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        htmlStr = m_style.replaceAll(""); // 过滤style标签


        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        htmlStr = m_html.replaceAll(""); // 过滤html标签


        Pattern p_space = Pattern.compile(regEx_space, Pattern.CASE_INSENSITIVE);
        Matcher m_space = p_space.matcher(htmlStr);
        htmlStr = m_space.replaceAll(""); // 过滤空格回车标签


        htmlStr = htmlStr.replaceAll("&nbsp;", ""); //过滤
        htmlStr = htmlStr.replaceAll(" ", ""); //过滤
        return htmlStr.trim(); // 返回文本字符串
    }

    public static String toLikeString(String str) {
        return "%" + str + "%";
    }

    public static void main(String[] args) {
        System.out.println(StringUtil.isNotBlank(null));
        System.out.println(StringUtil.isNotBlank(""));
        System.out.println(StringUtil.isNotBlank(" "));
        System.out.println(StringUtil.isNotBlank("bob"));
        System.out.println(StringUtil.isNotBlank("  bob  "));
    }
}
