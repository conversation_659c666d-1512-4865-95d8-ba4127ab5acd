package com.gtg56.idas.common.entity.dto.sensor;

import java.util.Date;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.lark.common.transform.DictionaryField;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 数据完整性检测配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@ApiModel(value="DataCheckConfigDTO", description="数据完整性检测配置表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckConfigDTO extends SimpleVO implements ImABean {


    @ApiModelProperty(value = "配置编码")
    private String code;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "配置状态 １ 启用０　禁用")
    private String status;

    @ApiModelProperty(value = "检测方式 dictCode: 1 固定天数  2 指定时间段")
    private String checkType;

    @ApiModelProperty(value = "检测类型 dictCode: 1 仓库2 库房")
    private String checkDataType;

    @ApiModelProperty(value = "往前检测多少天 check_type = 1时有效")
    private int days;

    @ApiModelProperty(value = "检测开始日期 check_type = 2时有效")
    private String startDate;

    @ApiModelProperty(value = "检测结束日期 check_type = 2时有效")
    private String endDate;

    @ApiModelProperty(value = "最后执行时间")
    private String lastCheckTime;

    @ApiModelProperty(value = "最后执行状态 ０　正常 １　异常")
    private String lastCheckStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private String modifyTime;

    @ApiModelProperty(value = "配置明细列表")
    List<DataCheckConfigDetailDTO> dataCheckConfigDetailVOList;
}