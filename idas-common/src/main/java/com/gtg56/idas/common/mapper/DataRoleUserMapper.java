package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.core.jdbc.IMapper;
import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;

import java.util.List;

/**
 * <p>
 * 用户数据角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface DataRoleUserMapper extends IMapper<DataRoleUser> {

    int updateById(DataRoleUser entity);

    /**
     * 通过用户ID查询数据角色列表
     * @param userId
     * @return
     */
    List<DataRoleUserDTO> listVOByUserId(String userId);

    /**
     * 通过用户ID删除数据角色用户数据
     * @param userId
     * @return
     */
    Boolean deleteByUserId(String userId);
}
