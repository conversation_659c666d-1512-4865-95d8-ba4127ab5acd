package com.gtg56.idas.common.entity.base;

import com.gtg56.idas.common.util.ByteArrayUtil;

import java.nio.charset.StandardCharsets;

public class StringRowKeyHBaseBaseDO extends HBaseBaseDO<String> {
    private static final long serialVersionUID = 8718227993958043926L;
    
    @Override
    public String transformRowKey() {
        return new String(getRowKey(), StandardCharsets.UTF_8);
    }
    
    @Override
    public byte[] toRowKey(String key) {
        return ByteArrayUtil.toByte(key);
    }
}
