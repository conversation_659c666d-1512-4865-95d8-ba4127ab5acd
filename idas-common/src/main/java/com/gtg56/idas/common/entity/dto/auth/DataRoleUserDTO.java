package com.gtg56.idas.common.entity.dto.auth;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 用户数据角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@ApiModel(value="DataRoleUserDTO", description="用户数据角色表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
public class DataRoleUserDTO extends SimpleVO implements ImABean {

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "数据角色ID")
    private String dataRoleId;

    @ApiModelProperty(value = "数据角色编码")
    private String dataRoleCode;

    @ApiModelProperty(value = "数据角色名称")
    private String dataRoleName;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}