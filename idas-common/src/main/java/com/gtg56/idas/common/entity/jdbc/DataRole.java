package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <p>
 * 数据角色
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataRole", description="数据角色")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_role")
public class DataRole extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "角色编码")
    private String code;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色状态")
    private String status;

    @ApiModelProperty(value = "角色类型 dictCode:dataRoleType")
    private String type;

    @ApiModelProperty(value = "角色类型")

    @Transient
    private String typeName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;

}
