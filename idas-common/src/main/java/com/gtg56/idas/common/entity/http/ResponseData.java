package com.gtg56.idas.common.entity.http;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.core.exception.BizException;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResponseData {
    public final static String
            CODE_SUCCESS = "200",
            CODE_FAIL_CLIENT = "400",
            CODE_FAIL_CLIENT_NO_AUTH = "403",
            CODE_FAIL_SERVER = "500";
    
    public final static String
            MSG_SUCCESS = "success",
    
            MSG_FAIL_CLIENT_NO_AUTH = "登录校验失败，请重新登录";
    
    private Object data;
    private String code;
    private String message;
    private Long timestamp = System.currentTimeMillis();
    
    public static ResponseData success() {
        ResponseData rb = new ResponseData();
        rb.code = CODE_SUCCESS;
        rb.message = MSG_SUCCESS;
        return rb;
    }
    
    public static ResponseData success(Object data) {
        ResponseData rb = success();
        rb.data = data;
        return rb;
    }
    
    public static ResponseData fail(Throwable t) {
        ResponseData rb = new ResponseData();
        rb.code = CODE_FAIL_SERVER;
        rb.message = t.getMessage();
        return rb;
    }
    
    public static ResponseData fail(BizException e) {
        ResponseData rb = new ResponseData();
        rb.code = CODE_FAIL_CLIENT;
        rb.message = e.getMessage();
        return rb;
    }
    
    public static ResponseData fail(BizException.ExceptionInfo exceptionInfo) {
        ResponseData rb = new ResponseData();
        rb.code = exceptionInfo.getCode();
        rb.message = exceptionInfo.getMessage();
        return rb;
    }
    
    public static ResponseData noAuth() {
        ResponseData rb = new ResponseData();
        rb.code = CODE_FAIL_CLIENT_NO_AUTH;
        rb.message = MSG_FAIL_CLIENT_NO_AUTH;
        return rb;
    }
    
    public String toJSON() {
        return JSON.toJSONString(this);
    }
}
