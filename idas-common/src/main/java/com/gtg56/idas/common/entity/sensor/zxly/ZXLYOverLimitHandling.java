package com.gtg56.idas.common.entity.sensor.zxly;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class ZXLYOverLimitHandling implements ImABean {

    /*表cxRecord_temphum + cdpz */
    private static final long serialVersionUID = -4530934139373199134L;
    private Long id;
    private String wzName;
    private String ipAddr;
    private Float wdz;
    private Float sdz;
    private Date dt;//处理时间
    private String kfsx;
    private String cDoEvent;//处理措施
    private String cReason;//处理原因
    private String cOperator;//操作人
    private String areaCode;
    private String deviceCode;
    private String remark;//当时温湿度情况
    private Float wdbjsx;
    private Float wdbjxx;
    private Float sdbjsx;
    private Float sdbjxx;

}
