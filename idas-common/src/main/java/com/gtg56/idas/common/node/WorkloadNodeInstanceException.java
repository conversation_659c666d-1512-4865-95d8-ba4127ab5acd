package com.gtg56.idas.common.node;

import lombok.Getter;

@Getter
public class WorkloadNodeInstanceException extends WorkloadNodeException {
    private static final long serialVersionUID = -4798887501543454871L;
    private final WorkloadNodeInstance instance;
    
    public WorkloadNodeInstanceException(WorkloadNodeInstance instance, String message, Throwable cause) {
        super(instance.getNode(),message, cause);
        this.instance = instance;
    }
    
    public WorkloadNodeInstanceException(WorkloadNodeInstance instance, String message) {
        super(instance.getNode(),message);
        this.instance = instance;
    }
}
