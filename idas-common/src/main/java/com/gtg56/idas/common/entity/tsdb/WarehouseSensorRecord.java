package com.gtg56.idas.common.entity.tsdb;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.entity.base.JsonBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@ApiModel(value = "温湿度传感器记录")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WarehouseSensorRecord
        extends TSDBBaseEntity<BigDecimal, WarehouseSensorRecord.Tags> {
    private static final long serialVersionUID = 6527749810825980808L;
    
    public static BigDecimal NULL_VALUE = BigDecimal.valueOf(-1024)
            .setScale(2,BigDecimal.ROUND_CEILING);
    
    @Override
    public String getMetric() {
        return SensorConsts.METRIC;
    }
    
    @Override
    public TSDBBaseEntity<BigDecimal, Tags> setValue(BigDecimal value) {
        return super.setValue(value.setScale(2,BigDecimal.ROUND_CEILING));
    }

    @Data
    @Accessors(chain = true)
    public static class Tags implements ImABean, JsonBean {
        private static final long serialVersionUID = -1592087351529829039L;

        @JSONField(ordinal = 0)
        @ApiModelProperty(value = "类型", allowableValues = "{TEMPERATURE,HUMIDITY}")
        private String type;

        @JSONField(ordinal = 1)
        @ApiModelProperty(value = "仓库编码", notes = "必须以中台仓库编码为准，而不是各供应商自定义")
        private String warehouseCode;
    
        @JSONField(ordinal = 2)
        @ApiModelProperty(value = "区域编码", notes = "某些供应商没有区域概念，则统一为【ANY】")
        private String regionCode;
    
        @JSONField(ordinal = 3)
        @ApiModelProperty(value = "传感器编码")
        private String sensorCode;
    
        @JSONField(ordinal = 4)
        @ApiModelProperty(value = "是否正常", allowableValues = "{Y,N}", notes = "status=N -> normal=T")
        private String normal = CommonConsts.TRUE;
    
        @JSONField(ordinal = 5)
        @ApiModelProperty(value = "状态", allowableValues = "{N,H,L,E,F}")
        private String status = SensorConsts.STATUS_NORMAL;
    }
}
