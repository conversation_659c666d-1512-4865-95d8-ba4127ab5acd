package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class WarehouseSensorRawQuery implements ImABean {
    private static final long serialVersionUID = 4448205990014091111L;
    
    @ApiModelProperty("对比对象")
    private List<SensorCompare> compares;
    
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
}
