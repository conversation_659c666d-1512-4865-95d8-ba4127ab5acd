package com.gtg56.idas.common.entity.hive;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 日期维表
 *
 * @see com.gtg56.idas.common.util.DateUtil#buildDate(String)
 * @see com.gtg56.idas.common.util.DateUtil#buildDate(String, List)
 * @see com.gtg56.idas.common.entity.jdbc.Festival
 */

@Data
@Accessors(chain = true)
public class DateDimension implements ImABean {
    private static final long serialVersionUID = -9001422030856758778L;
    
    private String date;                // 日期 yyyy-MM-dd
    private String year;                // 公历年份
    private String monthOfYear;         // 公历月份（1~12。Java Calendar是0~11，要注意）
    private String weekOfYear;          // 一年第几周（1~52）
    private String weekStartDate;       // 该周开始日期 yyyy-MM-dd
    private String weekStartYear;       // 该周开始公历年份（一年第一周和最后一周可能跨年）
    private String weekEndDate;         // 该周结束日期 yyyy-MM-dd
    private String weekEndYear;         // 该周结束公历年份（一年第一周和最后一周可能跨年）
    private String dayOfMonth;          // 公历日（1~31）
    private String dayOfYear;           // 一年第几天（1~366）
    private String leapYear;            // 是否闰年
    private String daysOfYearCount;     // 该公历年有多少天（平年365、闰年366）
    private String dayOfWeekNumber;     // 周几（1~7）一周从周日开始（java规范）
    private String dayOfWeekStr;        // 周几（周日~周六）一周从周日开始（java规范）
    private String quarterOfYear;       // 季度（1~4）
    private String dayOfQuarter;        // 季度第几天
    private String daysOfQuarterCount;  // 季度总天数
    private String quarterStartDate;    // 季度开始日期 yyyy-MM-dd
    private String quarterEndDate;      // 季度结束日期 yyyy-MM-dd
    private String lunarYear;           // 农历年份
    private String lunarGan;            // 农历年天干
    private String lunarZhi;            // 农历年地支
    private String zodiacSign;          // 生肖
    private String lunarMonth;          // 农历月份
    private String lunarLeapMonth;      // 是否农历闰月
    private String lunarDay;            // 农历日
    private String solarTerms;          // 节气（24节气）
    private String season;              // 季节（春夏秋冬），根据节气划分
    private String dayOfSeason;         // 季节第几天
    private String daysOfSeasonCount;   // 季节总天数
    private String seasonStartDate;     // 季节开始公历日期 yyyy-MM-dd
    private String seasonStartYear;     // 季节开始公历年份
    private String seasonEndDate;       // 季节结束公历日期 yyyy-MM-dd
    private String seasonEndYear;       // 季节结束公历年份
    private String festivalName;        // 公历节日（区分开，是因为同一日可能有2个节日。比如国庆和中秋有概率是同一天）
    private String lunarFestivalName;   // 农历节日
    private String weekend;             // 是否周末 dayOfWeekNumber in(1,7)
    private String workday;             // 是否工作日 由节假日/调休/周末综合决定。计算优先级：调休>节假日>周末
    private String holiday;             // 是否节假日 由节假日配置驱动
}
