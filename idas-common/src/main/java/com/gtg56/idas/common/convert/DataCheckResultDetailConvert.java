package com.gtg56.idas.common.convert;

import java.util.List;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;

import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public class DataCheckResultDetailConvert {

    public static BiConsumer<List<DataCheckResultDetail>, List<DataCheckResultDetailDTO>> toVO() {
        return (dataCheckResultDetailList, dataCheckResultDetailDTOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataCheckResultDetailDTOList.size(); i++) {
                DataCheckResultDetail entity = dataCheckResultDetailList.get(i);
                DataCheckResultDetailDTO vo = dataCheckResultDetailDTOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<DataCheckResultDetailDTO>, List<DataCheckResultDetail>> toEntity() {
        return (dataCheckResultDetailDTOList, dataCheckResultDetailList) -> {
            //添加自定义转换操作

        };
    }

}