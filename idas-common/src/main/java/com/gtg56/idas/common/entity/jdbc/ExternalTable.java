package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "external_table")
public class ExternalTable extends JDBCWithCreateModifyTime {
    
    private static final long serialVersionUID = 6028102633916322283L;
    
    @Column(name = "namespace")
    private String namespace;
    
    @Column(name = "table_name")
    private String tableName;
    
    @Column(name = "table_properties")
    private String tableProperties;
    
    @Column(name = "meta_store_path")
    private String metaStorePath;
    
    @Column(name = "store_path")
    private String storePath;
    
    @Column(name = "mode")
    private String mode;
    
    @Column(name = "active")
    private Integer active;
}
