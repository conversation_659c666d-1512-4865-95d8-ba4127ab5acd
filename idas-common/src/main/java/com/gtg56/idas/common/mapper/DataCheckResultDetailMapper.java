package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.core.jdbc.IMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据完整性检测结果明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface DataCheckResultDetailMapper extends IMapper<DataCheckResultDetail> {

    int updateById(DataCheckResultDetail entity);

    /**
     * 查找指定的DataCheckResultDetailDTO
     * @param dataCheckConfigDetailId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<DataCheckResultDetail> findDataCheckResultDetailList(@Param("dataCheckConfigDetailId") Long dataCheckConfigDetailId,@Param("startDate") String startDate,@Param("endDate") String endDate);
}
