package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@ApiModel("仓库温湿度传感器配置维度表")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "warehouse_sensor")
public class WarehouseSensor extends JDBCWithCreateModifyTime {
    private static final long serialVersionUID = 1317192388801889255L;
    
    @ApiModelProperty("温湿度监控系统厂商编码")
    @Column(name = "corp_code")
    private String corpCode;
    
    @ApiModelProperty("仓库编码")
    @Column(name = "warehouse_code")
    private String warehouseCode;
    
    @ApiModelProperty("仓库名称")
    @Column(name = "warehouse_name")
    private String warehouseName;
    
    @ApiModelProperty("区域编码（如无区域的厂商，则全部为ANY）")
    @Column(name = "region_code")
    private String regionCode;
    
    @ApiModelProperty("区域名称")
    @Column(name = "region_name")
    private String regionName;
    
    @ApiModelProperty("原始传感器编码（厂商定义）")
    @Column(name = "sensor_code_origin")
    private String sensorCodeOrigin;
    
    @ApiModelProperty("传感器编码 [warehouseCode]-[sensorCodeOrigin]")
    @Column(name = "sensor_code")
    private String sensorCode;
    
    @ApiModelProperty("传感器名称")
    @Column(name = "sensor_name")
    private String sensorName;
    
    @ApiModelProperty("传感器类型（厂商定义）")
    @Column(name = "sensor_type")
    private String sensorType;
    
    @ApiModelProperty("温度上限")
    @Column(name = "temperature_high_limit")
    private BigDecimal temperatureHighLimit;
    
    @ApiModelProperty("温度下限")
    @Column(name = "temperature_low_limit")
    private BigDecimal temperatureLowLimit;

    @ApiModelProperty("湿度上限")
    @Column(name = "humidity_high_limit")
    private BigDecimal humidityHighLimit;

    @ApiModelProperty("湿度下限")
    @Column(name = "humidity_low_limit")
    private BigDecimal humidityLowLimit;

    @ApiModelProperty("传感器功能（SENSOR：仓库测点；CAR：冷藏车；BOX：保温箱；OTHER：其他）")
    @Column(name = "sensor_function")
    private String sensorFunction;

    @ApiModelProperty("状态（0：停用；1：启用；）")
    @Column(name = "status")
    private String status;
}
