package com.gtg56.idas.common.tool.http;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.base.JsonBean;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.io.IOException;
import java.util.List;

@Slf4j
public class ZJFDAReportClientNew extends BaseClient {
    public ZJFDAReportClientNew() {
        super("http://wsdjg.zjfda.gov.cn:8085");
    }

    @Getter
    @Setter
    private String cropCode;
    @Getter
    @Setter
    private String password;
    @Getter
    @Setter
    private String code;

    public static final Integer SUCCESS_CODE = 0;

    public static final String
            API_CHANGE_PASSWORD = "changePWD",
            API_EDIT_ENTERPRISE_INFO = "editEnterpriseInfo",
            API_ADD_DEVICE = "addDevice",
            API_MODIFY_DEVICE = "modifyDevice",
            API_DELETE_DEVICE = "deleteDevice",
            API_UPLOAD_WAREHOUSE = "uploadWarehouse",
            API_UPLOAD_HUMITURE_RECORD = "uploadHumitureRecord",
            API_REPORT_STOP = "reportStop";

    private Result request(String api, Request request) {
        HttpPost post = new HttpPost(getBaseAddress() + "/" + api);
        String body = request.toJson();
        StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
        stringEntity.setContentEncoding("utf-8");
        post.setEntity(stringEntity);
        post.setHeader("Accept", "application/json; charset=UTF-8");

        try {
            log.info("request {} body {}", api, body);
            HttpResponse response = httpClient.execute(post);

            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                BizException bizException = new BizException("浙江药监上报失败 HTTP 状态码 " + response.getStatusLine().getStatusCode());
                log.warn("", bizException);
                throw bizException;
            }
            byte[] bytes = StreamUtil.fromStream(response.getEntity().getContent());
            String jsonStr = new String(bytes);
            JSONObject json = JSONObject.parseObject(jsonStr);
            log.info("response {}", jsonStr);

            return new Result(json.getInteger("status"), json.getString("error_msg"), body);
        } catch (IOException e) {
            BizException bizException = new BizException("浙江药监上报失败", e);
            log.warn("", bizException);
            throw bizException;
        } finally {
            post.releaseConnection();
        }
    }

    public Result changePassword(String newPassword) {
        ChangePasswordRequest request = new ChangePasswordRequest(this);
        request.setNew_pwd(newPassword);
        return request(API_CHANGE_PASSWORD, request);
    }

    public Result uploadTemperatureHumidityRecord(List<TemperatureHumidityRecord> data) {
        UploadTemperatureHumidityRecordRequest request = new UploadTemperatureHumidityRecordRequest(this);
        request.setData(data);
        return request(API_UPLOAD_HUMITURE_RECORD, request);
    }

    @Getter
    @Setter
    private static class Request implements JsonBean {
        private static final long serialVersionUID = -5929080779425144875L;

        private String corp_id;
        private String corp_pwd;
        private String uploadcode;

        public Request(ZJFDAReportClientNew client) {
            this.corp_id = client.getCropCode();
            this.corp_pwd = client.getPassword();
            this.uploadcode = client.getCode();
        }
    }

    @Getter
    @Setter
    private static class ChangePasswordRequest extends Request {

        private static final long serialVersionUID = 905511105096128701L;

        private String new_pwd;

        public ChangePasswordRequest(ZJFDAReportClientNew client) {
            super(client);
        }
    }

    @Getter
    @Setter
    private static class ObjectRequest<T> extends Request {

        private static final long serialVersionUID = -6956964234510603268L;

        private T data;

        public ObjectRequest(ZJFDAReportClientNew client) {
            super(client);
        }
    }

    @Getter
    @Setter
    private static class ListRequest<T> extends Request {

        private static final long serialVersionUID = -7044528424608140582L;

        private List<T> data;

        public ListRequest(ZJFDAReportClientNew client) {
            super(client);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class TemperatureHumidityRecord {
        private String id;
        private String time;
        private String temperature;
        private String humidity;
    }

    private static class UploadTemperatureHumidityRecordRequest extends ListRequest<TemperatureHumidityRecord> {

        private static final long serialVersionUID = 1462677528038816063L;

        public UploadTemperatureHumidityRecordRequest(ZJFDAReportClientNew client) {
            super(client);
        }
    }

    @Getter
    @ToString()
    public static class Result {

        public Result(Integer code, String message, String requestBody) {
            this.code = code;
            this.success = SUCCESS_CODE.equals(code);
            this.message = message;
            this.requestBody = requestBody;
        }

        private final Integer code;
        private final Boolean success;
        private final String message;
        private final String requestBody;
    }
}
