package com.gtg56.idas.common.service;

import com.github.pagehelper.PageInfo;
import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.dto.FDAReportDTO;
import com.gtg56.idas.common.entity.jdbc.FDAReport;
import com.gtg56.idas.common.entity.query.FDAReportQuery;

public interface IFDAReportService extends IService<FDAReport> {
    PageInfo<FDAReport> findPage(FDAReportQuery query);
    
    FDAReportDTO decompress(FDAReport report);
}
