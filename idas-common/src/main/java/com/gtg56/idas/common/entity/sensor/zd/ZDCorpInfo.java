package com.gtg56.idas.common.entity.sensor.zd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "泽大企业信息记录")
public class ZDCorpInfo {
    @ApiModelProperty(value = "企业代码")
    private String qydm;
    @ApiModelProperty(value = "企业名称")
    private String qymc;
    @ApiModelProperty(value = "企业地址")
    private String qydz;
    @ApiModelProperty(value = "联系人")
    private String lxr;
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;
    @ApiModelProperty(value = "质量负责人")
    private String zlfzr;
    @ApiModelProperty(value = "企业类型")
    private Integer qylx;
}
