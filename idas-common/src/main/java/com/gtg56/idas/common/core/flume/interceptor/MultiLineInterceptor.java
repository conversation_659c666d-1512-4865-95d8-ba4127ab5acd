package com.gtg56.idas.common.core.flume.interceptor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flume.Context;
import org.apache.flume.Event;
import org.apache.flume.interceptor.Interceptor;
import org.apache.flume.interceptor.RegexExtractorInterceptorPassThroughSerializer;
import org.apache.flume.interceptor.RegexExtractorInterceptorSerializer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MultiLineInterceptor implements Interceptor {
    
    static final String REGEX = "regex";
    static final String SERIALIZERS = "serializers";
    
    
    private final Pattern regex;
    private final List<NameAndSerializer> serializers;
    
    
    private MultiLineInterceptor(Pattern regex,
                                 List<NameAndSerializer> serializers) {
        this.regex = regex;
        this.serializers = serializers;
    }
    
    @Override
    public void initialize() {}
    @Override
    public void close() {}
    
    @Override
    public Event intercept(Event event) {
        Matcher matcher = regex.matcher(
                new String(event.getBody(), StandardCharsets.UTF_8));
        Map<String, String> headers = event.getHeaders();
        if (matcher.find()) {
            for (int group = 0, count = matcher.groupCount(); group < count; group++) {
                int groupIndex = group + 1;
                if (groupIndex > serializers.size()) {
                    log.debug("Skipping group {} to {} due to missing serializer",
                                group, count);
                    break;
                }
                NameAndSerializer serializer = serializers.get(group);
                log.debug("Serializing {} using {}", serializer.headerName,
                            serializer.serializer);
                headers.put(serializer.headerName,
                        serializer.serializer.serialize(matcher.group(groupIndex)));
            }
        }
        return event;
    }
    
    @Override
    public List<Event> intercept(List<Event> events) {
        List<Event> intercepted = new ArrayList<>(events.size());
        int addnum=0;//记录上一个正确匹配的event在队列中的位置,以便下一event有和它连接的需要
        for(int i=0;i<events.size();i++){
            Event interceptedEvent=null;
            Matcher matcher=regex.matcher(
                    new String(events.get(i).getBody(), StandardCharsets.UTF_8));
            if(matcher.find()){
                interceptedEvent = intercept(events.get(i));
                intercepted.add(interceptedEvent);
                addnum=i;//更新正确匹配的event在队列中的位置
            } else {
                String body=new String(intercepted.get(addnum).getBody(), StandardCharsets.UTF_8)+"\n"+new String(events.get(i).getBody(), StandardCharsets.UTF_8);
                intercepted.get(addnum).setBody(body.getBytes());
            }
        }
        return intercepted;
    }
    
    public static class Builder implements Interceptor.Builder {
        
        private Pattern regex;
        private List<NameAndSerializer> serializerList;
        private final RegexExtractorInterceptorSerializer defaultSerializer = new RegexExtractorInterceptorPassThroughSerializer();
        
        public void configure(Context context) {
            String regexString = context.getString(REGEX);
            regex = Pattern.compile(regexString);
            configureSerializers(context);
        }
        
        private void configureSerializers(Context context) {
            String serializerListStr = context.getString(SERIALIZERS);
            
            String[] serializerNames = serializerListStr.split("\\s+");
            
            Context serializerContexts =
                    new Context(context.getSubProperties(SERIALIZERS + "."));
            
            serializerList = new ArrayList<>(serializerNames.length);
            for(String serializerName : serializerNames) {
                Context serializerContext = new Context(
                        serializerContexts.getSubProperties(serializerName + "."));
                String type = serializerContext.getString("type", "DEFAULT");
                String name = serializerContext.getString("name");
                
                if("DEFAULT".equals(type)) {
                    serializerList.add(new NameAndSerializer(name, defaultSerializer));
                } else {
                    serializerList.add(new NameAndSerializer(name, getCustomSerializer(
                            type, serializerContext)));
                }
            }
        }
        
        private RegexExtractorInterceptorSerializer getCustomSerializer(
                String clazzName, Context context) {
            try {
                RegexExtractorInterceptorSerializer serializer = (RegexExtractorInterceptorSerializer) Class
                        .forName(clazzName).newInstance();
                serializer.configure(context);
                return serializer;
            } catch (Exception e) {
                log.error("Could not instantiate event serializer.", e);
                
            }
            return defaultSerializer;
        }
        
        public Interceptor build() {
            return new MultiLineInterceptor(regex, serializerList);
        }
    }
    
    @Getter
    static class NameAndSerializer {
        private final String headerName;
        private final RegexExtractorInterceptorSerializer serializer;
        
        public NameAndSerializer(String headerName,
                                 RegexExtractorInterceptorSerializer serializer) {
            this.headerName = headerName;
            this.serializer = serializer;
        }
    }
}
