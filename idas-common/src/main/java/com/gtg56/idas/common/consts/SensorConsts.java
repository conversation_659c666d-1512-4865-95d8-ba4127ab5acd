package com.gtg56.idas.common.consts;

import java.math.BigDecimal;

public class SensorConsts {
    
    public final static String AUTH_REALM = "SENSOR";
    
    public final static String
            TYPE_TEMPERATURE = "TEMPERATURE",   // 温度类型
            TYPE_HUMIDITY = "HUMIDITY",         // 湿度类型

    STATUS_NORMAL = "N",                // 正常状态
            STATUS_HIGH = "H",                  // 过高
            STATUS_LOW = "L",                   // 过低
            STATUS_ERROR = "E",                 // 异常
            STATUS_FAIL = "F",                  // 失败（无值）
            STATUS_OVER_LIMIT = "O",            // 超标 （H or L） 展示用

            STATUS_UNKNOWN = "U",               // 未知

    BOX_ZD_REGION = "********************************-5a337071644770306247493d",      //泽大-保温箱
    CORP_ZDBX = "GZ_ZDBX",              // 中大百迅
            CORP_ZDBXIOT = "GZ_ZDBXIOT",              // 中大百迅iot
            CORP_ZD = "HZ_ZD",                  // 泽大
            CORP_LG = "HZ_LG",                  // 杭州路格
            CORP_SD = "CZ_SD",                  // 常州数点
            CORP_KBS = "GZ_KBS",                // 广州金博
            CORP_LB = "BJ_LB",                  // 北京龙邦
            CORP_ZXLY = "BJ_ZXLY";                  // 北京志翔领驭

    public final static String METRIC = "warehouse.sensor";

    public final static String
            TAG_TYPE = "type",
            TAG_WAREHOUSE_CODE = "warehouseCode",
            TAG_REGION_CODE = "regionCode",
            TAG_SENSOR_CODE = "sensorCode",
            TAG_NORMAL = "normal",
            TAG_STATUS = "status";

    public final static String
            FUNC_SENSOR = "SENSOR",
            FUNC_BOX = "BOX",
            FUNC_CAR = "CAR",
            FUNC_OTHER = "OTHER";

    public final static TSDBAggregator HISTORY_AGGREGATOR = TSDBAggregator.NONE;
    public final static String HISTORY_DOWNSAMPLE = TSDBDownSample._30MIN_LAST_NULL;

    public static String toStatus(BigDecimal value, BigDecimal highLimit, BigDecimal lowLimit) {

        if (value == null) {
            return STATUS_ERROR;
        } else if (highLimit.compareTo(value) < 0) {
            return STATUS_HIGH;
        } else if (lowLimit.compareTo(value) > 0) {
            return STATUS_LOW;
        } else if (highLimit.compareTo(value) >= 0 && lowLimit.compareTo(value) <= 0) {
            return STATUS_NORMAL;
        }
        return STATUS_FAIL;
    }
    
    public static final String ZJFDA_CODE = "ZJFDA";
    
}
