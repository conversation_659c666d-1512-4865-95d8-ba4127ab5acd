package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.Festival;
import com.gtg56.idas.common.mapper.FestivalMapper;
import com.gtg56.idas.common.service.IFestivalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("festivalService")
public class FestivalServiceImpl extends AbstractService<Festival, FestivalMapper> implements IFestivalService {
    
    @Override
    public List<Festival> findByYear(int year) {
        return mapper.findByYear(year);
    }
}
