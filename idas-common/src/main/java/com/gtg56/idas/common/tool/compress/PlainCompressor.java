package com.gtg56.idas.common.tool.compress;

import com.gtg56.idas.common.util.StreamUtil;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class PlainCompressor implements Compressor {
    @Override
    public String name() {
        return "plain";
    }
    
    @Override
    public byte[] compress(byte[] plain) {
        return plain;
    }
    
    @Override
    public void compress(InputStream plain, OutputStream out) throws IOException {
        StreamUtil.inToOut(plain, out);
    }
    
    @Override
    public byte[] decompress(byte[] compressed) {
        return compressed;
    }
    
    @Override
    public void decompress(InputStream compressed, OutputStream out) throws IOException {
        StreamUtil.inToOut(compressed, out);
    }
    
}
