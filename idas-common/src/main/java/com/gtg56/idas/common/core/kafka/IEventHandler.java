package com.gtg56.idas.common.core.kafka;

import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * Created by Link on 2017/9/14.
 * MQ消息事件处理接口。
 *
 * 使用场景示例：
 * 模型处理完成后，将通过MQ进行回调。此时，MQConsumer将接收事件，
 * 并交由某个微服务实例去处理。该微服务实现该接口和handle方法，处理
 * 事件。
 *
 * 请注意：
 * handle的实现方法必须线程安全。MQConsumer将开多个线程去调用
 * 该方法。
 *
 * MQConsumer不关心处理过程和结果，所以尽量不要抛出任何异常！
 */
public interface IEventHandler {
    void handle(ConsumerRecord<String, String> event);
}
