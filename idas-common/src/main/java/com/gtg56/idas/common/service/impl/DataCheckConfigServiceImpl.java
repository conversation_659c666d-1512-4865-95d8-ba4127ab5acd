package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.convert.DataCheckConfigDetailConvert;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.mapper.DataCheckConfigDetailMapper;
import com.gtg56.idas.common.mapper.DataRolePermissionMapper;
import com.gtg56.idas.common.service.IDataCheckConfigService;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.mapper.DataCheckConfigMapper;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.service.IDataPermissionService;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.lark.web.ResponseData;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据完整性检测配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Service("dataCheckConfigService")
public class DataCheckConfigServiceImpl extends AbstractService<DataCheckConfig, DataCheckConfigMapper> implements IDataCheckConfigService {

    @Resource(name = "dataCheckConfigMapper")
    private DataCheckConfigMapper dataCheckConfigMapper;

    @Resource(name = "dataCheckConfigDetailMapper")
    private DataCheckConfigDetailMapper dataCheckConfigDetailMapper;

    @Resource(name = "dataPermissionService")
    private IDataPermissionService dataPermissionService;

    /**
     * @param dataCheckConfig
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public DataCheckConfig saveData(DataCheckConfig dataCheckConfig) {
        dataCheckConfig.setId(null);
        dataCheckConfig.baseSet();

        //新的明细记录
        List<DataCheckConfigDetailDTO> newDataCheckConfigDetailDTOList = dataCheckConfig.getDataCheckConfigDetailVOList();
        Map<Long, DataCheckConfigDetailDTO> newMap = new HashMap<>();

        dataCheckConfigMapper.insertData(dataCheckConfig);
        dataCheckConfig = findById(dataCheckConfig.getId());



        //该列表中缺少id,需要查询数据库
        List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(newDataCheckConfigDetailDTOList)){
            for(DataCheckConfigDetailDTO dataCheckConfigDetailDTO : newDataCheckConfigDetailDTOList){
                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                if("1".equals(dataCheckConfig.getCheckDataType())){
                    dataPermissionDTO.setType("WAREHOUSE");
                }else{
                    dataPermissionDTO.setType("REGION");
                }
                dataPermissionDTO.setWarehouseCode(dataCheckConfigDetailDTO.getWarehouseCode());
                dataPermissionDTO.setRegionCode(dataCheckConfigDetailDTO.getRegionCode());
                dataPermissionDTOList.add(dataPermissionDTO);
            }
        }
        dataPermissionDTOList = dataPermissionService.listVOByCodeList(dataPermissionDTOList);
        Map<String, DataPermissionDTO> dataPermissionDTOMap = new HashMap<>();
        for(DataPermissionDTO dataPermissionDTO : dataPermissionDTOList){
            dataPermissionDTOMap.put(dataPermissionDTO.getWarehouseCode()+"_"+dataPermissionDTO.getRegionCode(),dataPermissionDTO);
        }

        for (DataCheckConfigDetailDTO dataCheckConfigDetailDTO:newDataCheckConfigDetailDTOList) {
            if(dataCheckConfigDetailDTO.getDataPermissionId() == null){
                DataPermissionDTO dataPermissionDTO = dataPermissionDTOMap.get(dataCheckConfigDetailDTO.getWarehouseCode()+"_"+dataCheckConfigDetailDTO.getRegionCode());
                if(dataPermissionDTO != null){
                    dataCheckConfigDetailDTO.setDataPermissionId(Long.valueOf(dataPermissionDTO.getId()));
                }
            }
        }

        for(DataCheckConfigDetailDTO newDataCheckConfigDetailDTO : newDataCheckConfigDetailDTOList) {
            DataCheckConfigDetail dataCheckConfigDetail = BeanUtil.transform(newDataCheckConfigDetailDTO, DataCheckConfigDetail.class, DataCheckConfigDetailConvert.toEntity());
            dataCheckConfigDetail.setDataCheckConfigId(Long.valueOf(dataCheckConfig.getId()));
            dataCheckConfigDetail.baseSet();
            dataCheckConfigDetail.setDeletedFlag(0);
            dataCheckConfigDetailMapper.insert(dataCheckConfigDetail);
        }


        return dataCheckConfig;
    }

    /**
     * @param dataCheckConfig
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckConfig updateData(DataCheckConfig dataCheckConfig) {
        dataCheckConfig.baseSet();

        //新的明细记录
        List<DataCheckConfigDetailDTO> newDataCheckConfigDetailDTOList = dataCheckConfig.getDataCheckConfigDetailVOList();
        Map<Long, DataCheckConfigDetailDTO> newMap = new HashMap<>();

        //该列表中缺少id,需要查询数据库
        List<DataPermissionDTO> dataPermissionDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataCheckConfig.getDataCheckConfigDetailVOList())){
            for(DataCheckConfigDetailDTO dataCheckConfigDetailDTO : dataCheckConfig.getDataCheckConfigDetailVOList()){
                DataPermissionDTO dataPermissionDTO = new DataPermissionDTO();
                if("1".equals(dataCheckConfig.getCheckDataType())){
                    dataPermissionDTO.setType("WAREHOUSE");
                }else{
                    dataPermissionDTO.setType("REGION");
                }
                dataPermissionDTO.setWarehouseCode(dataCheckConfigDetailDTO.getWarehouseCode());
                dataPermissionDTO.setRegionCode(dataCheckConfigDetailDTO.getRegionCode());
                dataPermissionDTOList.add(dataPermissionDTO);
            }
        }
        dataPermissionDTOList = dataPermissionService.listVOByCodeList(dataPermissionDTOList);
        Map<String, DataPermissionDTO> dataPermissionDTOMap = new HashMap<>();
        for(DataPermissionDTO dataPermissionDTO : dataPermissionDTOList){
            dataPermissionDTOMap.put(dataPermissionDTO.getWarehouseCode()+"_"+dataPermissionDTO.getRegionCode(),dataPermissionDTO);
        }

        for (DataCheckConfigDetailDTO dataCheckConfigDetailDTO:newDataCheckConfigDetailDTOList) {
            if(dataCheckConfigDetailDTO.getDataPermissionId() == null){
                DataPermissionDTO dataPermissionDTO = dataPermissionDTOMap.get(dataCheckConfigDetailDTO.getWarehouseCode()+"_"+dataCheckConfigDetailDTO.getRegionCode());
                if(dataPermissionDTO != null){
                    dataCheckConfigDetailDTO.setDataPermissionId(Long.valueOf(dataPermissionDTO.getId()));
                }
            }
        }

        if (newDataCheckConfigDetailDTOList != null) {
            newMap = newDataCheckConfigDetailDTOList.stream()
                    .collect(Collectors.toMap(DataCheckConfigDetailDTO::getDataPermissionId,
                            Function.identity(), // 这里使用Function.identity()来直接使用DataCheckConfigDetailDTO对象作为值
                            (existing, replacement) -> existing)); // 在ID冲突的情况下，选择保留已有的条目
        }


        //原有明细记录
        List<DataCheckConfigDetailDTO> oldDataCheckConfigDetailDTOList = dataCheckConfigDetailMapper.findByConfigId(Long.valueOf(dataCheckConfig.getId()));
        Map<Long, DataCheckConfigDetailDTO> oldMap = new HashMap<>();


        if (oldDataCheckConfigDetailDTOList != null) {
            oldMap = oldDataCheckConfigDetailDTOList.stream()
                    .collect(Collectors.toMap(DataCheckConfigDetailDTO::getDataPermissionId,
                            Function.identity(), // 这里使用Function.identity()来直接使用DataCheckConfigDetailDTO对象作为值
                            (existing, replacement) -> existing)); // 在ID冲突的情况下，选择保留已有的条目
        }


        for(DataCheckConfigDetailDTO oldDataCheckConfigDetailDTO : oldDataCheckConfigDetailDTOList) {
            if (!newMap.containsKey(oldDataCheckConfigDetailDTO.getDataPermissionId())) {
                DataCheckConfigDetailDTO deleteDataCheckConfigDetailDTO = oldMap.get(oldDataCheckConfigDetailDTO.getDataPermissionId());
                DataCheckConfigDetail dataCheckConfigDetail = BeanUtil.transform(deleteDataCheckConfigDetailDTO, DataCheckConfigDetail.class, DataCheckConfigDetailConvert.toEntity());
                dataCheckConfigDetail.setId(Long.valueOf(deleteDataCheckConfigDetailDTO.getId()));
                dataCheckConfigDetail.setDeletedFlag(1);
                dataCheckConfigDetailMapper.updateById(dataCheckConfigDetail);
            }
        }


        for(DataCheckConfigDetailDTO newDataCheckConfigDetailDTO : newDataCheckConfigDetailDTOList) {
            if (!oldMap.containsKey(newDataCheckConfigDetailDTO.getDataPermissionId())) {
                DataCheckConfigDetail dataCheckConfigDetail = BeanUtil.transform(newDataCheckConfigDetailDTO, DataCheckConfigDetail.class, DataCheckConfigDetailConvert.toEntity());
                dataCheckConfigDetail.setDataCheckConfigId(Long.valueOf(dataCheckConfig.getId()));
                dataCheckConfigDetail.baseSet();
                dataCheckConfigDetail.setDeletedFlag(0);
                dataCheckConfigDetailMapper.insert(dataCheckConfigDetail);
            }
        }

        dataCheckConfig.setDeletedFlag(0L);
        //更新
        dataCheckConfigMapper.updateById(dataCheckConfig);

        return dataCheckConfig;
    }

    @Override
    public DataCheckConfig updateStatus(String id,String status) {

        DataCheckConfig checkConfig = findById(Long.valueOf(id));
        checkConfig.setStatus(status);
        checkConfig.setModifyTime(new Date());
        dataCheckConfigMapper.updateById(checkConfig);
        return checkConfig;
    }

}
