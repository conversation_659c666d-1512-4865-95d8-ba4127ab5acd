package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.core.reflect.ClassPoolX;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.node.WorkloadNode;
import javassist.ByteArrayClassPath;
import javassist.CtClass;
import lombok.Builder;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;

@Getter
@Builder
@Accessors(chain = true)
public class NodeProvideDTO implements ImABean {
    private static final long serialVersionUID = -4084641935135294161L;
    
    private final String nodeClassName;
    private final byte[] nodeByteCode;
    
    /**
     * 消费端调用
     *
     * 从序列化的字节码数据 加载WorkloadNode类并提供实例
     * @return workLoadNode
     */
    @SneakyThrows
    public WorkloadNode build() {
        ClassPoolX cp = ClassPoolX.getDefault();
    
        ByteArrayClassPath classPath = new ByteArrayClassPath(nodeClassName, nodeByteCode);
        cp.insertClassPath(classPath);
        CtClass ctClass = cp.get(nodeClassName);
        Class<?> rawClass = ctClass.toClass();
        if(!(WorkloadNode.class.isAssignableFrom(rawClass))) {
            throw new ReflectiveOperationException(
                    rawClass.getName() + " is not WorkloadNode subclass"
            );
        } else {
            return (WorkloadNode) rawClass.newInstance();
        }
    }
}
