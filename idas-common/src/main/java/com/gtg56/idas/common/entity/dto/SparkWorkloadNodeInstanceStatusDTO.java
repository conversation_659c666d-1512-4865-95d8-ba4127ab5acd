package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by Link on 2019/12/30.
 */
@Data
@NoArgsConstructor
public class SparkWorkloadNodeInstanceStatusDTO implements ImABean {
    private static final long serialVersionUID = 2188540328545542250L;

    private String driverIp;
    private String nodeName;
    private String instanceId;
    private NodeArgsDTO nodeArgs;
    private Date submitTime,executionTime,endTime;
    private String lastExceptionLocalized;
    private String status;
    private String returnJson;
}
