package com.gtg56.idas.common.entity.sensor.zd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "泽大设备信息")
public class ZDCorpDevice {
    @ApiModelProperty(value = "企业代码")
    private String qydm;
    @ApiModelProperty(value = "仓库代码")
    private String ckdm;
    @ApiModelProperty(value = "设备代码")
    private String sbdm;
    @ApiModelProperty(value = "设备名称")
    private String sbmc;
    @ApiModelProperty(value = "设备状态")
    private Integer sbzt;
}
