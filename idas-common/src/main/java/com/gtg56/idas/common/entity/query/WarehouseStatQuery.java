package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WarehouseStatQuery implements ImABean {
    private static final long serialVersionUID = -8711855693378743368L;
    
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
    
    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("对比对象")
    private List<SensorCompare> compares;
    
}
