package com.gtg56.idas.common.node;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Getter
public class WorkloadNodeException extends RuntimeException {
    private static final long serialVersionUID = 3338687057757476360L;
    
    private final WorkloadNode node;
    
    public WorkloadNodeException(WorkloadNode node,String message) {
        super(message);
        this.node = node;
    }
    
    public WorkloadNodeException(WorkloadNode node,String message, Throwable cause) {
        super(message, cause);
        this.node = node;
    }
    
    public static WorkloadNodeException noSuchNode(String nodeName) {
        return new WorkloadNodeException(null,"WorkloadNode " + nodeName + " not exists");
    }
    
    public static WorkloadNodeException notActive(WorkloadNode node) {
        return new WorkloadNodeException(node,"WorkloadNode " + node.nodeName() + " not active");
    }
    
    public static WorkloadNodeException missingRequireArguments(WorkloadNode node, List<String> missingArgs) {
        IllegalArgumentException iae = new IllegalArgumentException("missing require arguments : " + StringUtils.join(missingArgs," , "));
        return new WorkloadNodeException(node,"missing require arguments for WorkloadNode " + node.nodeName() + " , usage : "+node.usage(),iae);
    }
    
    public static WorkloadNodeException providerServiceNotAvailable(String nodeName,Throwable cause) {
        return new WorkloadNodeException(null,"requesting node " + nodeName + " fail , provider service not available",cause);
    }
}
