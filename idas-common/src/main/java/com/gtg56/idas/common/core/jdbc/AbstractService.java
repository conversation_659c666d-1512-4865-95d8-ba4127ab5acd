package com.gtg56.idas.common.core.jdbc;


import com.github.pagehelper.PageHelper;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Condition;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;

/**
 * 基于通用MyBatis Mapper插件的Service接口的实现
 */
public abstract class AbstractService<T,M extends IMapper<T>> implements IService<T> {

    @Autowired
    protected M mapper;

    private Class<T> modelClass;    // 当前泛型真实类型的Class

    @SuppressWarnings("unchecked")
    public AbstractService() {
        ParameterizedType pt = (ParameterizedType) this.getClass().getGenericSuperclass();
        modelClass = (Class<T>) pt.getActualTypeArguments()[0];
    }

    public void save(T model) {
        mapper.insertSelective(model);
    }

    public void save(List<T> models) {
        mapper.insertList(models);
    }

    public void deleteById(Long id) {
        mapper.deleteByPrimaryKey(id);
    }

    public void deleteByIds(String ids) {
        mapper.deleteByIds(ids);
    }

    public void update(T model) {
        mapper.updateByPrimaryKeySelective(model);
    }

    public T findById(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public T findBy(String fieldName, Object value) throws TooManyResultsException {
        try {
            T model = modelClass.newInstance();
            Field field = modelClass.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(model, value);
            return mapper.selectOne(model);
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public List<T> findByIds(String ids) {
        return mapper.selectByIds(ids);
    }

    public List<T> findByCondition(Condition condition) {
        return mapper.selectByCondition(condition);
    }

    public List<T> findAll() {
        return mapper.selectAll();
    }
    
    public Condition newCondition() { return new Condition(modelClass); }

    public <F extends BaseQuery> PageObject<T> pageByQuery(F query, PageObject pageObject) {
        PageHelper.startPage(pageObject.getPageNum(), pageObject.getPageSize(), pageObject.getOrderStr());
        List<T> list = ((IMapper)this.mapper).listByQuery(query);
        return new PageObject(list);
    }

    public PageObject<T> pageByMap(Map<String, Object> params, PageObject pageObject) {
        PageHelper.startPage(pageObject.getPageNum(), pageObject.getPageSize(), pageObject.getOrderStr());
        List<T> list = ((IMapper)this.mapper).listByMap(params);
        return new PageObject(list);
    }
}
