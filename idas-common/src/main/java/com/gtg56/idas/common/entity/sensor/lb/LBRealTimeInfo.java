package com.gtg56.idas.common.entity.sensor.lb;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class LBRealTimeInfo implements ImABean {
    private static final long serialVersionUID = 5043246479048851745L;

    private Float temperature;
    private Float humidity;
    private Date devTime;
    private String regionCode;
    private String regionName;
    private String sensorCodeOrigin;
    private String sensorName;
    private Float temperatureHighLimit;
    private Float temperatureLowLimit;
    private Float humidityHighLimit;
    private Float humidityLowLimit;
}
