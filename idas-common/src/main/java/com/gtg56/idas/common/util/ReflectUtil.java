package com.gtg56.idas.common.util;

import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class ReflectUtil {
    @SneakyThrows
    public static Method getFieldSetterMethod(Class<?> clz, Field field) {
        return clz.getMethod(setterMethodName(field),field.getType());
    }
    
    private static String setterMethodName(Field field) {
        String name = field.getName();
        return "set" + CaseUtil.lowerCamelToUpperCamel(name);
    }
    
    @SneakyThrows
    public static void setValue(Class<?> clz, Field field,Object obj,Object value) {
        Method setterMethod = getFieldSetterMethod(clz, field);
        setterMethod.invoke(obj,value);
    }
}
