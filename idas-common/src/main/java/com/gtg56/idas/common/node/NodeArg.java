package com.gtg56.idas.common.node;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@AllArgsConstructor(staticName = "of")
public class NodeArg implements Serializable {
    private static final long serialVersionUID = 5310152167164142476L;
    private final String name;
    private final Boolean require;
    private final String defaultValue;
}