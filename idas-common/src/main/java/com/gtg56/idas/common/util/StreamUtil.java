package com.gtg56.idas.common.util;

import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;

import java.io.*;

/**
 * Created by Link on 2017/9/8.
 */
public class StreamUtil {

    public static boolean storeToLocal(File file,InputStream is) {
        if(is == null) return false;

        try {
            FileOutputStream fos = new FileOutputStream(file);

            inToOut(is,fos);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        return true;

    }


    public static boolean storeToLocal(File file,byte[] data) {
        return storeToLocal(file,new ByteArrayInputStream(data));
    }

    public static byte[] fileData(File file) throws IOException {
        return fromStream(new FileInputStream(file));
    }

    public static byte[] fromStream(InputStream is) throws IOException {
        byte[] buffer = null;
        int read;

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            buffer = new byte[1024];
            while ( (read = is.read(buffer,0,1024)) != -1 ) {
                bos.write(buffer,0,read);
                bos.flush();
            }
            buffer = bos.toByteArray();
        } finally {
            safeClose(is);
        }

        return buffer;
    }

    public static String fromStreamToString(InputStream is, String charset) throws IOException {
        byte[] buffer = new byte[1024];
        int read;
        StringBuilder sb = new StringBuilder();
        try {
            while ((read = is.read(buffer, 0, 1024)) != -1) {
                if (read > 0) {
                    byte[] b = new byte[read];
                    System.arraycopy(buffer, 0, b, 0, read);
                    String s = new String(b, charset);
                    sb.append(s);
                }
            }
        } finally {
            safeClose(is);
        }
        return sb.toString();
    }

    public static String tmpFilePath() {
        return System.getProperty("java.io.tmpdir") + File.separator + "tmp" + RandomStringUtils.random(32, true, true);
    }

    public static long inToOut(InputStream is, OutputStream os) throws IOException {
        return inToOut(is, os, true, true);
    }

    public static long inToOut(InputStream is, OutputStream os, boolean closeIn, boolean closeOut) throws IOException {
        long totalLen = 0;
        try {
            byte[] buffer = new byte[1024];
            int len;
            
            while ( (len = is.read(buffer,0,1024) ) != -1 ) {
                os.write(buffer,0,len);
                os.flush();
                
                totalLen += len;
            }
        } finally {
            if(closeIn) {
                safeClose(is);
            }
            if(closeOut) {
                safeClose(os);
            }
        }
        
        return totalLen;
    }

    public static void printInputStream(InputStream is) throws IOException {
        StringBuilder sb = new StringBuilder();

        byte buffer[] = new byte[1024];

        while ( is.read(buffer,0,1024) != -1 ) {
            sb.append(new String(buffer));
        }

        System.out.println(sb.toString());

        safeClose(is);
    }

    public static void logInputStream(InputStream is , Logger log , String level) throws IOException {
        StringBuilder sb = new StringBuilder();

        byte buffer[] = new byte[1024];

        while ( is.read(buffer,0,1024) != -1 ) {
            sb.append(new String(buffer));
        }
        safeClose(is);

        switch (level) {
            case "trace" : {
                log.trace(sb.toString());
                break;
            }
            case "debug" : {
                log.debug(sb.toString());
                break;
            }
            case "warn" : {
                log.warn(sb.toString());
                break;
            }
            case "error" : {
                log.error(sb.toString());
                break;
            }
            default : {
                log.info(sb.toString());
                break;
            }
        }
    }

    public static InputStream resourceInputStream(String resourcePath) {
        Exception e = new Exception();
        StackTraceElement[] stackTrace = e.getStackTrace();
        StackTraceElement invokerElm = stackTrace[1];

        Class invokerClass;

        try {
            invokerClass = Class.forName(invokerElm.getClassName());
        } catch (ClassNotFoundException ignored) {
            invokerClass = StreamUtil.class;
        }

        return invokerClass.getClassLoader().getResourceAsStream(resourcePath);
    }
    
    public static long writeStream(byte[] data,OutputStream os) throws IOException {
        return writeStream(data,os,true);
    }
    
    public static long writeStream(byte[] data,OutputStream os,boolean closeOut) throws IOException {
        ByteArrayInputStream bis = new ByteArrayInputStream(data);
        return inToOut(bis,os,true,closeOut);
    }

    public static void safeClose(Closeable closeable) {
        if(closeable != null) {
            try {
                closeable.close();
            } catch (IOException ignored) {}
        }
    }

    public static void safeClose(AutoCloseable closeable) {
        if(closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {}
        }
    }
}
