package com.gtg56.idas.common.util;

import com.gtg56.lark.web.PageObject;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019-06-19
 */
public class BeanUtil {

    private static Logger logger = LoggerFactory.getLogger(BeanUtil.class);

    /**
     * 获取属性，主要去掉抛出异常
     * @param bean
     * @param name
     * @return
     */
    public static Object getProperty(Object bean, String name){
        try {
            return PropertyUtils.getProperty(bean, name);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return null;
    }

    /**
     * 写入属性，主要去掉抛出异常
     * @param bean
     * @param name
     * @param value
     */
    public static void setProperty(Object bean, String name, Object value) {
        try {
            PropertyUtils.setProperty(bean, name, value);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
    }

    public static <T, S> T transform(S source, Class<T> cls){
        return transform(source, cls, null);
    }

    public static <T, S> T transform(S source, Class<T> cls, BiConsumer<List<S>, List<T>> stBiConsumer){
        if(source == null) {
            return null;
        }
        try {
            T target = cls.newInstance();
            org.springframework.beans.BeanUtils.copyProperties(source, target);
//            new DictionaryTransform().transformDictionaryFields(target); //TODO 临时注释
            if(stBiConsumer != null) {
                stBiConsumer.accept(Arrays.asList(source), Arrays.asList(target));
            }
            return target;
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> T transformCode(Object source, Object target){
//        List<Field> fields = ReflectUtil.getAllDeclaredFields(target.getClass());
//        for (Field field : fields) {
//            Annotation annotation = field.getAnnotation()
//        }
//        try {
//
//            org.springframework.beans.BeanUtils.copyProperties(source, target);
//        } catch (InstantiationException e) {
//            e.printStackTrace();
//        } catch (IllegalAccessException e) {
//            e.printStackTrace();
//        }
        return null;
    }

    public static <T, S> List<T> transformList(List<S> sourceList, Class<T> cls){
        return transformList(sourceList, cls, null);
    }

    public static <T, S> List<T> transformList(List<S> sourceList, Class<T> cls, BiConsumer<List<S>, List<T>> stBiConsumer){
        List<T> targetList =  sourceList.stream().map(source -> transform(source, cls)).collect(Collectors.toList());
        if(stBiConsumer != null) {
            stBiConsumer.accept(sourceList, targetList);
        }
        return targetList;
    }

    public static <T, S> PageObject<T> transformPage(PageObject<S> sourcePage, Class<T> cls){
        return transformPage(sourcePage, cls, null);
    }

    public static <T, S> PageObject<T> transformPage(PageObject<S> sourcePage, Class<T> cls, BiConsumer<List<S>, List<T>> ftBiConsumer){
        PageObject<T> curPageObject = transform(sourcePage, PageObject.class);
        curPageObject.setResultList(transformList(sourcePage.getResultList(), cls, ftBiConsumer));
        return curPageObject;
    }
}
