package com.gtg56.idas.common.entity.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 手动采集请求对象
 */
public class ManualCollectRequestDTO {
    @ApiModelProperty(value = "仓库编码", required = true)
    private String warehouseCode;
    
    @ApiModelProperty(value = "日期(格式: yyyy-MM-dd)", required = true)
    private String date;
    
    @ApiModelProperty(value = "是否包含异常报警数据", required = false)
    private Boolean includeAlarm;

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Boolean getIncludeAlarm() {
        return includeAlarm;
    }

    public void setIncludeAlarm(Boolean includeAlarm) {
        this.includeAlarm = includeAlarm;
    }
}
