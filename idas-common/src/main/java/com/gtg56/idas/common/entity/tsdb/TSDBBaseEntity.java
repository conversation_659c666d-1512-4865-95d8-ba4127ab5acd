package com.gtg56.idas.common.entity.tsdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@ApiModel(value = "时序数据库OpenTSDB标准实体")
@Data
@Accessors(chain = true)
public abstract class TSDBBaseEntity<V extends Number,T extends Serializable> implements ImABean {
    private static final long serialVersionUID = -5325592100345481625L;
    
    @JSONField(ordinal = 0)
    @ApiModelProperty(value = "度量编码", notes = "子类可重写getMetric()实现固定")
    private String metric;
    
    @JSONField(ordinal = 1)
    @ApiModelProperty(value = "时间戳，秒格式", notes = "默认当前时间")
    private Long timestamp = System.currentTimeMillis() / 1000L;
    
    @JSONField(ordinal = 2)
    @ApiModelProperty(value = "度量数值", notes = "可以为任何Number类型")
    private V value;
    
    @JSONField(ordinal = 3)
    @ApiModelProperty(value = "标签", notes = "【至少要有1个标签】，可以用Map也可以是类")
    protected T tags;
    
    public String toPutBody() {
        return JSON.toJSONString(this);
    }
}
