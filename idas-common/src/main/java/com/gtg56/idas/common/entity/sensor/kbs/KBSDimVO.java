package com.gtg56.idas.common.entity.sensor.kbs;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class KBSDimVO implements ImABean {
    private static final long serialVersionUID = 8001224975081668297L;
    private String regionCode;
    private String regionName;
    private String sensorCodeOrigin;
    private String sensorName;
    private Short sensorType;
    private BigDecimal temperatureHighLimit;
    private BigDecimal temperatureLowLimit;
    private BigDecimal humidityHighLimit;
    private BigDecimal humidityLowLimit;

}
