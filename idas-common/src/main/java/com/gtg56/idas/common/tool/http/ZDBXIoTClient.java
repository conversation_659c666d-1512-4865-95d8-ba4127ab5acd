package com.gtg56.idas.common.tool.http;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.util.DateUtil;
import lombok.Data;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Slf4j
public class ZDBXIoTClient extends BaseClient {
    public ZDBXIoTClient(String baseAddress) {
        super(baseAddress);
    }

    @Setter
    private String name, pwd;

    private final static Integer successCode = 0;

    @SneakyThrows
    public String getToken() {
        URIBuilder builder = new URIBuilder(getBaseAddress());
        builder.setPath("/api/Values/Login");
        builder.addParameter("name", name);
        builder.addParameter("pwd", pwd);
        HttpPost request = new HttpPost(builder.build());

        try {
            HttpResponse response = httpClient.execute(request);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                BizException bizException = new BizException("获取Token失败 HTTP 状态码 " + response.getStatusLine().getStatusCode());
                log.warn("", bizException);
                throw bizException;
            }
            HttpEntity entity = response.getEntity();
            String res = getResponseString(entity);
            JSONObject json = JSONObject.parseObject(res);
            if (successCode.equals(json.getInteger("code"))) {
                return json.getString("data");
            } else {
                BizException bizException = new BizException("获取Token失败 返回 " + res);
                log.warn("", bizException);
                throw bizException;
            }
        } catch (IOException e) {
            BizException bizException = new BizException("获取Token失败" + e.getLocalizedMessage());
            log.warn("", bizException);
            throw bizException;
        } finally {
            request.releaseConnection();
        }
    }

    @SneakyThrows
    private Page getRecords(Date start, Date end, int page, String token) {
        URIBuilder builder = new URIBuilder(getBaseAddress());
        builder.setPath("/api/Values/HistoricalData");
        builder.addParameter("nPage", page + "");
        builder.addParameter("nSize", "20");
        builder.addParameter("start", DateUtil.ymdhms().format(start));
        builder.addParameter("end", DateUtil.ymdhms().format(end));
        builder.addParameter("limit", "false");
        HttpPost request = new HttpPost(builder.build());
        request.setHeader("token", token);

        try {
            HttpResponse response = httpClient.execute(request);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                BizException bizException = new BizException("获取历史记录失败 HTTP 状态码 " + response.getStatusLine().getStatusCode());
                log.warn("", bizException);
                throw bizException;
            }
            HttpEntity entity = response.getEntity();
            String res = getResponseString(entity);
            JSONObject json = JSONObject.parseObject(res);
            if (successCode.equals(json.getInteger("code"))) {
                return JSONObject.parseObject(json.getString("data"), Page.class);
            } else {
                BizException bizException = new BizException("获取Token失败 返回 " + res);
                log.warn("", bizException);
                throw bizException;
            }
        } catch (IOException e) {
            BizException bizException = new BizException("获取历史记录失败" + e.getLocalizedMessage());
            log.warn("", bizException);
            throw bizException;
        } finally {
            request.releaseConnection();
        }
    }

    public List<Record> getRecords(Date start, Date end) {
        String token = getToken();
        Page first = getRecords(start, end, 1, token);
        List<Record> records = new ArrayList<>();

        if (first.getPages() > 0) {
            records.addAll(first.getResult());

            for (int i = 2; i < first.getPages(); i++) {
                records.addAll(getRecords(start, end, i, token).getResult());
            }
        }
        return records;
    }

    @Data
    public static class Page {
        private Integer pages;
        private List<Record> result;
    }

    @Data
    public static class Record {
        private String areacode;
        private String areaname;
        private String devicecode;
        private String devicename;
        private String nodecode;
        private String nodename;
        private String userlimit;
        private String useroffline;
        private String battery;
        private String rpm;
        private BigDecimal tvalue;
        private BigDecimal tmin;
        private BigDecimal tmax;
        private BigDecimal hvalue;
        private BigDecimal hmin;
        private BigDecimal hmax;
        private String time;
        private Integer status;
        private String statusdesc;
    }

}
