package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.convert.DataCheckResultConvert;
import com.gtg56.idas.common.convert.WarehouseSensorConvert;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO;
import com.gtg56.idas.common.entity.dto.sensor.WarehouseSensorDTO;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.mapper.WarehouseSensorMapper;
import com.gtg56.idas.common.service.IDataCheckConfigDetailService;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.mapper.DataCheckConfigDetailMapper;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.util.BeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 数据完整性检测配置明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service("dataCheckConfigDetailService")
public class DataCheckConfigDetailServiceImpl extends AbstractService<DataCheckConfigDetail, DataCheckConfigDetailMapper> implements IDataCheckConfigDetailService {

    @Resource(name = "dataCheckConfigDetailMapper")
    private DataCheckConfigDetailMapper dataCheckConfigDetailMapper;

    @Resource(name = "warehouseSensorMapper")
    private WarehouseSensorMapper warehouseSensorMapper;

    /**
     * @param dataCheckConfigDetail
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckConfigDetail saveData(DataCheckConfigDetail dataCheckConfigDetail) {
        dataCheckConfigDetail.setId(null);
        dataCheckConfigDetail.baseSet();
        save(dataCheckConfigDetail);
        dataCheckConfigDetail = findById(dataCheckConfigDetail.getId());
        return dataCheckConfigDetail;
    }

    /**
     * @param dataCheckConfigDetail
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCheckConfigDetail updateData(DataCheckConfigDetail dataCheckConfigDetail) {
        dataCheckConfigDetail.baseSet();
        update(dataCheckConfigDetail);
        return dataCheckConfigDetail;
    }

    @Override
    public List<DataCheckConfigDetailDTO> findByConfigId(Long dataCheckConfigId) {
        List<DataCheckConfigDetailDTO>  dataCheckConfigDetailDTOList = dataCheckConfigDetailMapper.findByConfigId(dataCheckConfigId);
        if(dataCheckConfigDetailDTOList == null || dataCheckConfigDetailDTOList.size() == 0){
            return dataCheckConfigDetailDTOList;

        }

        dataCheckConfigDetailDTOList.forEach(dataCheckConfigDetailDTO -> {
            List<WarehouseSensor> warehouseSensorList = warehouseSensorMapper.findWarehouseSensorDTOList(dataCheckConfigDetailDTO.getDataPermissionId());
            List<WarehouseSensorDTO> warehouseSensorDTOList = BeanUtil.transformList(warehouseSensorList, WarehouseSensorDTO.class, WarehouseSensorConvert.toVO());
            dataCheckConfigDetailDTO.setWarehouseSensorDTOList(warehouseSensorDTOList);
        });

        return dataCheckConfigDetailDTOList;
    }
}
