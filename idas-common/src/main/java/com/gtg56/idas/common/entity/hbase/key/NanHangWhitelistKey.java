package com.gtg56.idas.common.entity.hbase.key;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class NanHangWhitelistKey implements Serializable {
    private static final long serialVersionUID = 5077054324452270351L;
    
    private String whitelistGroup;
    private String idNum;
}
