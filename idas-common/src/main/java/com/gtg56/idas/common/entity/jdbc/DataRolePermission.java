package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 数据角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataRolePermission", description="数据角色权限表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_role_permission")
public class DataRolePermission extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据角色ID")
    private Long dataRoleId;

    @ApiModelProperty(value = "数据权限ID")
    private Long dataPermissionId;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;
}
