package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;

import java.util.List;

/**
 * <p>
 * 用户数据角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface IDataRoleUserService  extends IService<DataRoleUser> {

    /**
     *
     * @param dataRoleUser
     * @return
     */
    public DataRoleUser saveData(DataRoleUser dataRoleUser);

    /**
     *
     * @param dataRoleUser
     */
    public DataRoleUser updateData(DataRoleUser dataRoleUser);

    /**
     * 通过用户ID查询数据角色列表
     * @param userId
     * @return
     */
    public List<DataRoleUserDTO> listVOByUserId(String userId);

    /**
     * 通过用户ID删除数据角色用户数据
     * @param userId
     * @return
     */
    public Boolean deleteByUserId(String userId);
}
