package com.gtg56.idas.common.entity.dto.auth;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 数据角色权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataRolePermissionDTO", description="数据角色权限表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
public class DataRolePermissionDTO extends SimpleVO implements ImABean {

    @ApiModelProperty(value = "数据角色ID")
    private String dataRoleId;

    @ApiModelProperty(value = "数据权限ID")
    private String dataPermissionId;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}