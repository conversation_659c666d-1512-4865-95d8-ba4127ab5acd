package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WarehouseSensorLastQuery implements ImABean {
    private static final long serialVersionUID = -7114343084635328800L;
    
    @ApiModelProperty("对比对象")
    private List<SensorCompare> compares;
    
    @ApiModelProperty(value = "温度状态", allowableValues = ",N,O,E")
    private String temperatureStatus;
    @ApiModelProperty(value = "湿度状态", allowableValues = ",N,O,E")
    private String humidityStatus;
    
    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLow;
    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHigh;
    
    @ApiModelProperty("湿度下限，>=0")
    private BigDecimal humidityLow;
    @ApiModelProperty("湿度上限，<=100")
    private BigDecimal humidityHigh;
}
