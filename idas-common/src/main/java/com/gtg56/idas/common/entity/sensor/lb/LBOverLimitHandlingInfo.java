package com.gtg56.idas.common.entity.sensor.lb;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class LBOverLimitHandlingInfo implements ImABean {
    private static final long serialVersionUID = 6877475482517930077L;

    private Number rn;
    private Date startTime;
    private String handleTime;
    private String principal;
    private String suggestion;
    private String sensorCodeOrigin;
    private String regionCode;
    private Float temperatureHighLimit;
    private Float temperatureLowLimit;
    private Float humidityHighLimit;
    private Float humidityLowLimit;
}
