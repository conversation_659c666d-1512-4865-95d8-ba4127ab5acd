package com.gtg56.idas.common.entity.base;

import com.gtg56.idas.common.core.hbase.HBaseResultBeanMapping;
import com.gtg56.idas.common.util.ByteArrayUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Link on 2017/5/26.
 * HBase基础Bean。一个Bean对应一张表下的一个列族下所有列。
 *
 * 封装HBase基础的主键查询/插入/更新
 */
@Slf4j
public abstract class HBaseBaseDO<K extends Serializable> implements ImABean {
    private static final long serialVersionUID = -7565213972672230169L;

    @Getter @Setter
    private byte[] rowKey;
    
    public abstract K transformRowKey();
    public abstract byte[] toRowKey(K key);
    public byte[] toRowKeyAndSet(K key) {
        setRowKey(toRowKey(key));
        return rowKey;
    }

    /**
     * 获取Bean对应的表名。如果有命名空间，则返回 [命名空间]:[表名]
     * @return 表名
     */
    public String getTableName() {
        HBaseResultBeanMapping mapping = getMapping();

        if(StringUtils.isBlank(mapping.nameSpace()))
            return mapping.tableName();
        else
            return mapping.nameSpace() + ":" + mapping.tableName();
    }

    /**
     * 获取Bean对应的列族名
     * @return 列族名
     */
    public String getColumnFamily() {
        return getMapping().columnFamily();
    }
    
    private HBaseResultBeanMapping getMapping() {
        Class<? extends HBaseBaseDO> myClass = this.getClass();
        HBaseResultBeanMapping mapping = myClass.getAnnotation(HBaseResultBeanMapping.class);
    
        if(mapping == null) {
            throw new IllegalArgumentException(myClass.getName() + " must use annotation HBaseResultBeanMapping");
        }
        return mapping;
    }

    /**
     * HBase Result对象属性注入Bean
     * @param result HBase Result对象
     */
    public final void map(Result result) {
        String columnFamily = getColumnFamily();
        byte[] cfByte = columnFamily.getBytes();

        this.setRowKey(result.getRow());

        Field[] fields = getFields();
        for(Field field : fields) {
            try {
                byte[] byteFN = field.getName().getBytes();
                invokeSetter(getSetterMethod(field), result.getValue(cfByte, byteFN), field.getType());
            } catch (Exception e) {
                log.warn("map Result to bean " + this.getClass().getName() + " field " + field.getName() + " failed",e);
            }
        }
    }

    /**
     * 获取Bean下非rowKey的属性
     * @return 属性s
     */
    private Field[] getFields() {
        Class<? extends HBaseBaseDO> myClass = this.getClass();
        Field[] declaredFields = myClass.getDeclaredFields();

        List<Field> fields = new ArrayList<>(declaredFields.length - 1);

        for(Field field : declaredFields) {
            if (!field.getName().equals("rowKey") && !Modifier.isStatic(field.getModifiers())) {
                fields.add(field);
            }
        }
        return fields.toArray(new Field[0]);
    }

    /**
     * 获取属性的Setter方法
     * @param field 属性对象
     * @return Setter方法
     */
    private Method getSetterMethod(Field field) {
        Class<? extends HBaseBaseDO> myClass = this.getClass();
        if(!field.getDeclaringClass().equals(myClass))
            throw new IllegalArgumentException("field " + field.getName() + " does not belong class " + myClass.getName());

        String setterMethodName = "set" + fieldNameToBigCamel(field);

        try {
            return myClass.getDeclaredMethod(setterMethodName,field.getType());
        } catch (NoSuchMethodException e) {
            throw new IllegalArgumentException("field " + field.getName() + "does not have setter method " + setterMethodName,e);
        }
    }

    /**
     * 调用Setter方法
     * @param setterMethod Setter方法对象
     * @param value byte数组值
     * @param type Setter方法对应的属性的类型对象
     */
    private void invokeSetter(Method setterMethod, byte[] value, Class<?> type) {
        try {
            if(value == null || value.length == 0) {
                return;
            } else if (type.equals(byte[].class)) {
                setterMethod.invoke(this, value);
            } else if(type.isAssignableFrom(Integer.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toInt(value));
            } else if(type.isAssignableFrom(Long.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toLong(value));
            } else if(type.isAssignableFrom(Short.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toShort(value));
            } else if(type.isAssignableFrom(Float.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toFloat(value));
            } else if(type.isAssignableFrom(Double.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toDouble(value));
            } else if(type.isAssignableFrom(Character.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toChar(value));
            } else if(type.isAssignableFrom(Boolean.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toBoolean(value));
            } else if(type.isAssignableFrom(String.class)) {
                setterMethod.invoke(this, ByteArrayUtil.toStr(value));
            } else {
                setterMethod.invoke(this, (Object) ByteArrayUtil.toObject(value));
            }
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new IllegalArgumentException("invoke setter method : " + setterMethod.getName() + " fail ",e);
        }
    }

    /**
     * Bean转换为HBase Put对象
     * @return Put对象
     */
    public Put toPut() {
        String columnFamily = getColumnFamily();
        byte[] cfByte = columnFamily.getBytes();

        Field[] fields = getFields();

        Put put = new Put(getRowKey());

        for(Field field : fields) {
            try {
                byte[] byteValue = getterResultToBytes(invokeGetterMethod(getGetterMethod(field)));

                if(byteValue == null || byteValue.length == 0) continue;

                put.addColumn(cfByte,field.getName().getBytes(),byteValue);
            } catch (Exception e) {
                log.warn("bean " + this.getClass().getName() + " field " + field.getName() + " toPut failed",e);
            }
        }
        return put;
    }

    /**
     * 获取Bean属性的Getter方法
     * @param field Bean属性对象
     * @return Setter方法对象
     */
    private Method getGetterMethod(Field field) {
        Class<? extends HBaseBaseDO> myClass = this.getClass();
        if(!field.getDeclaringClass().equals(myClass))
            throw new IllegalArgumentException("field " + field.getName() + " does not belong class " + myClass.getName());

        String setterMethodName = "get" + fieldNameToBigCamel(field);

        try {
            return myClass.getDeclaredMethod(setterMethodName);
        } catch (NoSuchMethodException e) {
            throw new IllegalArgumentException("field " + field.getName() + " does not have getter method " + setterMethodName,e);
        }
    }

    private String fieldNameToBigCamel(Field field) {
        String name = field.getName();
        return StringUtils.upperCase(StringUtils.left(name,1)) + StringUtils.right(name,name.length()-1);
    }

    /**
     * 调用Getter方法
     * @param method Getter方法
     * @return Getter返回值
     */
    private Object invokeGetterMethod(Method method) {
        try {
            return method.invoke(this);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Getter方法返回值变byte数组
     * @param value Getter返回值
     * @return byte数组
     */
    private byte[] getterResultToBytes(Object value) {
        if(value == null) {
            return null;
        }
        Class<?> type = value.getClass();

        if(type.equals(byte[].class)) {
            return (byte[]) value;
        } else if(type.isAssignableFrom(Integer.class)) {
            return ByteArrayUtil.toByte((Integer) value);
        } else if(type.isAssignableFrom(Long.class)) {
            return ByteArrayUtil.toByte((Long) value);
        } else if(type.isAssignableFrom(Short.class)) {
            return ByteArrayUtil.toByte((Short) value);
        } else if(type.isAssignableFrom(Float.class)) {
            return ByteArrayUtil.toByte((Float) value);
        } else if(type.isAssignableFrom(Double.class)) {
            return ByteArrayUtil.toByte((Double) value);
        } else if(type.isAssignableFrom(Character.class)) {
            return ByteArrayUtil.toByte((Character) value);
        } else if(type.isAssignableFrom(Boolean.class)) {
            return ByteArrayUtil.toByte((Boolean) value);
        } else if(type.isAssignableFrom(String.class)) {
            return ByteArrayUtil.toByte((String) value);
        } else {
            return ByteArrayUtil.toByte((Serializable) value);
        }
    }
}
