package com.gtg56.idas.common.service;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.core.jdbc.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 数据完整性检测配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface IDataCheckConfigService extends IService<DataCheckConfig> {

    /**
     *
     * @param dataCheckConfig
     * @return
     */
    public DataCheckConfig saveData(DataCheckConfig dataCheckConfig);

    /**
     *
     * @param dataCheckConfig
     */
    public DataCheckConfig updateData(DataCheckConfig dataCheckConfig);

    /**
     * 更新状态
     * @param id
     * @param status
     * @return
     */
    public DataCheckConfig updateStatus(String id,String status);


    }
