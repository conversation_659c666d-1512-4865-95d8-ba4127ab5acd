package com.gtg56.idas.common.entity.sensor.lg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "路格实时记录")
public class LGRealtimeRecord {
    @ApiModelProperty(value = "仪器序号")
    private Long index;
    @ApiModelProperty(value = "仪器Hex字段")
    private String reHex;
    @ApiModelProperty(value = "仪器SN号")
    private String sn;
    @ApiModelProperty(value = "字节数量")
    private Long bytesNumber;
    @ApiModelProperty(value = "仪器最后时间")
    private String datetime;
    @ApiModelProperty(value = "通道数")
    private Integer channelCount;
    @ApiModelProperty(value = "电池电量")
    private Integer battery;
    @ApiModelProperty(value = "是否接电")
    private Integer external;
    @ApiModelProperty(value = "通道数据")
    private List<LGChannelRecord> listChannel;
    
    @Data
    @ApiModel(value = "路格实时通道记录")
    public static class LGChannelRecord {
        @ApiModelProperty(value = "仪器SN号")
        private String sn;
        private Long devId;
        @ApiModelProperty(value = "通道序号")
        private Integer channelPort;
        @ApiModelProperty(value = "通道值")
        private BigDecimal value;
        @ApiModelProperty(value = "单位名")
        private String typeName;
        @ApiModelProperty(value = "通道单位符号")
        private String typeUnit;
        @ApiModelProperty(value = "测量精度")
        private BigDecimal format;
        @ApiModelProperty(value = "上限")
        private BigDecimal upper;
        @ApiModelProperty(value = "下限")
        private BigDecimal lower;
        @ApiModelProperty(value = "是否超标")
        private Boolean isOver;
        @ApiModelProperty(value = "单位名序号")
        private String typeNameCode;
        @ApiModelProperty(value = "通道单位符号序号")
        private String typeUnitCode;
    }
}
