package com.gtg56.idas.common.entity.jdbc;

import java.util.Date;
import java.util.List;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <p>
 * 数据完整性检测配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@ApiModel(value="DataCheckConfig", description="数据完整性检测配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_check_config")
public class DataCheckConfig extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置编码")
    private String code;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "配置状态 １ 启用０　禁用")
    private String status;

    @ApiModelProperty(value = "检测方式 dictCode: 1 固定天数  2 指定时间段")
    private String checkType;

    @ApiModelProperty(value = "检测类型 dictCode: 1 仓库2 库房")
    private String checkDataType;

    @ApiModelProperty(value = "往前检测多少天 check_type = 1时有效")
    private Integer days;

    @ApiModelProperty(value = "检测开始日期 check_type = 2时有效")
    private Date startDate;

    @ApiModelProperty(value = "检测结束日期 check_type = 2时有效")
    private Date endDate;

    @ApiModelProperty(value = "最后执行时间")
    private Date lastCheckTime;

    @ApiModelProperty(value = "最后执行状态 ０　正常 １　异常")
    private String lastCheckStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;

    @ApiModelProperty(value = "是否删除")
    private Long deletedFlag;

    @Transient
    @ApiModelProperty(value = "配置明细列表")
    List<DataCheckConfigDetailDTO> dataCheckConfigDetailVOList;
}
