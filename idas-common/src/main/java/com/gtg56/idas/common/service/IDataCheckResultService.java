package com.gtg56.idas.common.service;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.core.jdbc.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
/**
 * <p>
 * 数据完整性检测结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IDataCheckResultService extends IService<DataCheckResult> {

    /**
     *
     * @param dataCheckResult
     * @return
     */
    public DataCheckResult saveData(DataCheckResult dataCheckResult);

    /**
     *
     * @param dataCheckResult
     */
    public DataCheckResult updateData(DataCheckResult dataCheckResult);



    }
