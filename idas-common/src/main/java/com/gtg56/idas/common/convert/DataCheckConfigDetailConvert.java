package com.gtg56.idas.common.convert;

import java.util.List;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;

import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public class DataCheckConfigDetailConvert {

    public static BiConsumer<List<DataCheckConfigDetail>, List<DataCheckConfigDetailDTO>> toVO() {
        return (dataCheckConfigDetailList, dataCheckConfigDetailDTOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataCheckConfigDetailDTOList.size(); i++) {
                DataCheckConfigDetail entity = dataCheckConfigDetailList.get(i);
                DataCheckConfigDetailDTO vo = dataCheckConfigDetailDTOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<DataCheckConfigDetailDTO>, List<DataCheckConfigDetail>> toEntity() {
        return (dataCheckConfigDetailDTOList, dataCheckConfigDetailList) -> {
            //添加自定义转换操作

        };
    }

}