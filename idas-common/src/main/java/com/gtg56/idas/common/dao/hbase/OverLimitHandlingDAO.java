package com.gtg56.idas.common.dao.hbase;

import com.gtg56.idas.common.core.hbase.HBaseBaseDAO;
import com.gtg56.idas.common.core.hbase.WarpHBaseConf;
import com.gtg56.idas.common.entity.hbase.OverLimitHandling;
import com.gtg56.idas.common.entity.hbase.key.OverLimitHandingKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Repository("overLimitHandlingDAO")
public class OverLimitHandlingDAO extends HBaseBaseDAO<OverLimitHandling> {
    
    @Override
    public void put(OverLimitHandling bean) {
        OverLimitHandingKey key = new OverLimitHandingKey(bean.getSensorCode(), bean.getRecordTime());
        bean.toRowKeyAndSet(key);
        super.put(bean);
    }
    
    public List<OverLimitHandling> scan(String sensorCode, Date start, Date end) {
        String startKey = sensorCode + "-" + start.getTime();
        String endKey = sensorCode + "-" + end.getTime();
        return scan(startKey.getBytes(), endKey.getBytes(), true);
    }
    
    private static final Lock lock = new ReentrantLock(true);
    private static transient OverLimitHandlingDAO instance = null;
    
    /**
     * 为Spark分布式并行写入准备，其他应用请用Spring托管Bean
     *
     * @return OverLimitHandlingDAO
     */
    public static OverLimitHandlingDAO getInstance() {
        if (instance == null) {
            try {
                lock.lock();
                if (instance == null) {
                    instance = new OverLimitHandlingDAO();
                    WarpHBaseConf conf = new WarpHBaseConf();
                    conf.init();
                    instance.setConf(conf);
                    instance.setEsSize(8);
                }
            } finally {
                lock.unlock();
            }
        }
        return instance;
    }
}
