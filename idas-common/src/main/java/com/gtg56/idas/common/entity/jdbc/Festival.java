package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "festival")
public class Festival extends JDBCBaseDO {
    private static final long serialVersionUID = -675021445263386978L;
    
    @Column(name = "festival_start_year")
    private Integer festivalStartYear;
    
    @Column(name = "festival_end_year")
    private Integer festivalEndYear;
    
    @Column(name = "festival_name")
    private String festivalName;
    
    @Column(name = "lunar_festival_name")
    private String lunarFestivalName;
    
    @Column(name = "festival_start_date")
    private String festivalStartDate;
    
    @Column(name = "festival_end_date")
    private String festivalEndDate;
    
    @Column(name = "workday_list")
    private String workdayList;
}
