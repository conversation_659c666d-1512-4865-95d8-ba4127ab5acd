package com.gtg56.idas.common.tool.compress;

import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;
import org.xerial.snappy.Snappy;
import org.xerial.snappy.SnappyInputStream;
import org.xerial.snappy.SnappyOutputStream;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class SnappyCompressor implements Compressor {
    @Override
    public String name() {
        return "snappy";
    }
    
    @SneakyThrows
    @Override
    public byte[] compress(byte[] plain) {
        return Snappy.compress(plain);
    }
    
    @Override
    public void compress(InputStream plain, OutputStream out) throws IOException {
        SnappyOutputStream snappy = new SnappyOutputStream(out);
        StreamUtil.inToOut(plain, snappy);
    }
    
    @SneakyThrows
    @Override
    public byte[] decompress(byte[] compressed) {
        return Snappy.uncompress(compressed);
    }
    
    @Override
    public void decompress(InputStream compressed, OutputStream out) throws IOException {
        SnappyInputStream snappy = new SnappyInputStream(compressed);
        StreamUtil.inToOut(snappy, out);
    }
}
