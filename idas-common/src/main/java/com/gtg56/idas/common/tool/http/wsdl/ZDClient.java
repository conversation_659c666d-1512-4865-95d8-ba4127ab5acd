package com.gtg56.idas.common.tool.http.wsdl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.common.entity.sensor.zd.*;
import com.gtg56.idas.common.tool.http.wsdl.axis2.zd.BSQSdkServiceStub;
import com.gtg56.idas.common.util.DateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.axis2.client.Options;
import org.apache.axis2.transport.http.HTTPConstants;
import org.apache.axis2.transport.http.impl.httpclient3.HttpTransportPropertiesImpl;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.UsernamePasswordCredentials;
import org.apache.commons.httpclient.auth.CredentialsProvider;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class ZDClient {
    private final BSQSdkServiceStub stub;
    private final String endpoint;
    private final String username;
    private final String password;
    
    @SneakyThrows
    public ZDClient(String endpoint, String username, String password) {
        this.username = username;
        this.password = password;
        this.endpoint = endpoint;
        
        this.stub = new BSQSdkServiceStub(endpoint);
        
        Options options = stub._getServiceClient().getOptions();
        options.setUserName(username);
        options.setPassword(password);
        
        options.setProperty(org.apache.axis2.transport.http.HTTPConstants.CHUNKED, Boolean.FALSE);
        
        HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
        
        auth.setUsername(username);
        auth.setPassword(password);
        auth.setPreemptiveAuthentication(true);
        options.setProperty(HTTPConstants.AUTHENTICATE, auth);
        
        options.setManageSession(true);
        
        CredentialsProvider pro = (scheme, host, port, proxy) -> new UsernamePasswordCredentials(username, password);
        HttpClient client = new HttpClient();
        client.getParams().setAuthenticationPreemptive(true);
        client.getParams().setParameter(CredentialsProvider.PROVIDER, pro);
        stub._getServiceClient().getServiceContext().getConfigurationContext().setProperty(HTTPConstants.CACHED_HTTP_CLIENT, client);
    }
    
    @SneakyThrows
    public List<ZDCorpInfo> getCorpInfo() {
        BSQSdkServiceStub.GetCorpInfo query = new BSQSdkServiceStub.GetCorpInfo();
        query.setUsername(username);
        query.setPassword(password);
        query.setStartDate("2018-01-01 00:00:00");
        BSQSdkServiceStub.GetCorpInfoResponse res = stub.getCorpInfo(query);
        
        return mapResults(res.getResult(), ZDCorpInfo.class);
    }
    
    @SneakyThrows
    public List<ZDWarehouse> getWarehouse(String corpId) {
        BSQSdkServiceStub.GetCorpWarehouse query = new BSQSdkServiceStub.GetCorpWarehouse();
        query.setUsername(username);
        query.setPassword(password);
        query.setCorpid(corpId);
        BSQSdkServiceStub.GetCorpWarehouseResponse res = stub.getCorpWarehouse(query);
        return mapResults(res.getResult(), ZDWarehouse.class);
    }
    
    @SneakyThrows
    public List<ZDCorpDevice> getDevice(String corpId) {
        BSQSdkServiceStub.GetCorpDevice query = new BSQSdkServiceStub.GetCorpDevice();
        query.setUsername(username);
        query.setPassword(password);
        query.setCorpid(corpId);
        BSQSdkServiceStub.GetCorpDeviceResponse res = stub.getCorpDevice(query);
        return mapResults(res.getResult(), ZDCorpDevice.class);
    }
    
    @SneakyThrows
    public List<ZDSensorRecord> getCurrent() {
        BSQSdkServiceStub.GetCurrentTempHumi query = new BSQSdkServiceStub.GetCurrentTempHumi();
        query.setUsername(username);
        query.setPassword(password);
        BSQSdkServiceStub.GetCurrentTempHumiResponse res = stub.getCurrentTempHumi(query);
        return mapResults(res.getResult(), ZDSensorRecord.class);
    }
    
    @SneakyThrows
    public List<ZDSensorRecord> getHistory(String deviceId, Date start, Integer limit) {
        BSQSdkServiceStub.GetDeviceTempHumi query = new BSQSdkServiceStub.GetDeviceTempHumi();
        query.setUsername(username);
        query.setPassword(password);
        query.setDeviceid(deviceId);
        query.setStartDate(DateUtil.ymdhms().format(start));
        query.setMaxNumRecords(limit);
        BSQSdkServiceStub.GetDeviceTempHumiResponse res = stub.getDeviceTempHumi(query);
        return mapResults(res.getResult(), ZDSensorRecord.class);
    }

    @SneakyThrows
    public List<ZDAlarm> getDeviceAlarm(String deviceId, Date start, Integer limit) {
        BSQSdkServiceStub.GetDeviceAlarm query = new BSQSdkServiceStub.GetDeviceAlarm();
        query.setUsername(username);
        query.setPassword(password);
        query.setDeviceid(deviceId);
        query.setStartDate(DateUtil.ymdhms().format(start));
        query.setMaxNumRecords(limit);

        BSQSdkServiceStub.GetDeviceAlarmResponse res = stub.getDeviceAlarm(query);
        return mapResults(res.getResult(), ZDAlarm.class);
    }

    private <T> List<T> mapResults(String result, Class<T> clz) {
        JSONObject json = processResult(result);
        if (json.getBoolean("success")) {
            JSONArray array = json.getJSONArray("results");
            List<T> ret = new LinkedList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                T obj = JSON.parseObject(jsonObject.toJSONString(), clz);
                ret.add(obj);
            }
            return ret;
        } else {
            throw new BizException(BizException.ExceptionInfo.of(ResponseData.CODE_FAIL_SERVER, "请求泽大服务器" + endpoint + "失败 " + json.getString("error")));
        }
    }

    private JSONObject processResult(String result) {
        if (!result.startsWith("{\"")) {
            String ret = "{\"" + StringUtils.right(result, result.length() - 2);
            return JSONObject.parseObject(ret);
        }
        return JSONObject.parseObject(result);
    }

    @SneakyThrows
    public static void main(String[] args) {
        ZDClient zdClient = new ZDClient("http://10.1.103.254:8200/BSQSdk?wsdl", "wuhan", "6560777d");
        zdClient.getHistory("1", DateUtil.ymdhms().parse("2023-11-06 16:00:00"), 86400).forEach(record -> System.out.println(JSON.toJSONString(record)));
      //  ZDClient zdClient = new ZDClient("http://10.5.30.3:8200/BSQSdk?wsdl", "wuhan", "6560777d");
       // zdClient.getHistory("7DC356715", DateUtil.ymd().parse("2023-08-29"), 100).forEach(record -> System.out.println(JSON.toJSONString(record)));
    }
}
