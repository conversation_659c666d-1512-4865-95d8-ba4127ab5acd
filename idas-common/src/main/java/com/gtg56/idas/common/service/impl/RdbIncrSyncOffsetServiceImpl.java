package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.mapper.RdbIncrSyncOffsetMapper;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Service("rdbIncrSyncOffsetService")
public class RdbIncrSyncOffsetServiceImpl
        extends AbstractService<RdbIncrSyncOffset, RdbIncrSyncOffsetMapper>
        implements IRdbIncrSyncOffsetService {
    
    @Override
    public RdbIncrSyncOffset get(String url, String table) {
        RdbIncrSyncOffset res = getBy(url, table);
        if(res == null) {
            RdbIncrSyncOffset offset = new RdbIncrSyncOffset();
            offset
                    .setUrl(url)
                    .setTableName(table)
                    .setLastId(0L)
                    .setCreateTime(new Date())
                    .setModifyTime(new Date());
    
            return insert(offset);
        } else {
            return res;
        }
    }
    
    private RdbIncrSyncOffset getBy(String url, String table) {
        Condition condition = newCondition();
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("url",url)
                .andEqualTo("tableName",table);
    
        List<RdbIncrSyncOffset> res = findByCondition(condition);
        if(res.isEmpty()) return null;
        else return res.stream().findFirst().orElse(null);
    }
    
    private RdbIncrSyncOffset insert(RdbIncrSyncOffset offset) {
        mapper.insert(offset);
        return offset;
    }
    
    @Override
    public RdbIncrSyncOffset set(RdbIncrSyncOffset offset) {
        mapper.set(offset);
        return offset;
    }
}
