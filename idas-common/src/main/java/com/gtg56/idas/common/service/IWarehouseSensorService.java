package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.SensorCompare;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;

import java.util.List;

public interface IWarehouseSensorService extends IService<WarehouseSensor> {
    List<WarehouseSensor> listWarehouse();

    boolean createOrUpdate(WarehouseSensor warehouseSensor);

    WarehouseSensor getBy(String warehouseCode, String regionCode, String sensorCode);

    List<WarehouseSensor> findBy(WarehouseSensorQuery query);

    List<WarehouseSensor> listByCompares(List<SensorCompare> compares);
}
