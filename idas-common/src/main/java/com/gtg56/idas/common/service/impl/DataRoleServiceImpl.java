package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.mapper.DataRoleMapper;
import com.gtg56.idas.common.service.IDataRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 数据角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service("dataRoleService")
public class DataRoleServiceImpl extends AbstractService<DataRole,DataRoleMapper> implements IDataRoleService {

    @Resource(name = "dataRoleMapper")
    private DataRoleMapper dataRoleMapper;

    /**
     * @param dataRole
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRole saveData(DataRole dataRole) {
        dataRole.setId(null);
        dataRole.baseSet();
        save(dataRole);
        dataRole = findByCode(dataRole.getCode());
        return dataRole;
    }

    /**
     * @param dataRole
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRole updateData(DataRole dataRole) {
        dataRole.baseSet();
        update(dataRole);
        dataRole = findByCode(dataRole.getCode());
        return dataRole;
    }

    @Override
    public DataRole findByCode(String code) {
        return dataRoleMapper.selectByCode(code);
    }
}
