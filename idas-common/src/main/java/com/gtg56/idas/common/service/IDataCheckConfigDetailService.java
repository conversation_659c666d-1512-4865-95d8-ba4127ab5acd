package com.gtg56.idas.common.service;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
/**
 * <p>
 * 数据完整性检测配置明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IDataCheckConfigDetailService extends IService<DataCheckConfigDetail> {

    /**
     *
     * @param dataCheckConfigDetail
     * @return
     */
    public DataCheckConfigDetail saveData(DataCheckConfigDetail dataCheckConfigDetail);

    /**
     *
     * @param dataCheckConfigDetail
     */
    public DataCheckConfigDetail updateData(DataCheckConfigDetail dataCheckConfigDetail);

    /**
     * 通过 配置表ID 查询明细 dataCheckConfigId
     * @param dataCheckConfigId
     */
    public List<DataCheckConfigDetailDTO> findByConfigId(Long dataCheckConfigId);

    }
