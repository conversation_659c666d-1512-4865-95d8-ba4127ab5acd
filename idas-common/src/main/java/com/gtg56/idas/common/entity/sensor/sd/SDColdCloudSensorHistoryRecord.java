package com.gtg56.idas.common.entity.sensor.sd;

import com.gtg56.idas.common.entity.base.JsonBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SDColdCloudSensorHistoryRecord implements JsonBean {
    private static final long serialVersionUID = -4363759725988199791L;

    private String warehousename;
    private Integer sensorcode;
    private BigDecimal temperature;
    private BigDecimal maxtemperature;
    private BigDecimal mintemperature;
    private BigDecimal humidity;
    private BigDecimal maxhumidity;
    private BigDecimal minhumidity;
    private Date operdate;
    private String remarks;
    private Integer isover;
}
