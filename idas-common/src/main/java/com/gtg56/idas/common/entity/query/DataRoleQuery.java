package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据角色
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataRoleQuery", description="数据角色")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataRoleQuery extends SimpleQuery implements ImABean {

    @ApiModelProperty(value = "角色编码")
    private String code;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色状态")
    private String status;

    @ApiModelProperty(value = "角色类型 dictCode: dataRoleType WAREHOUSE AREA SENSOR")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;


// "code,name,status,type,remark,creator,modifier,mofidyTime,"
}