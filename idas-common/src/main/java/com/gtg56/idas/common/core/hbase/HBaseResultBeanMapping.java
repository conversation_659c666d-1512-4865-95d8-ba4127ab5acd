package com.gtg56.idas.common.core.hbase;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by Link on 2017/5/27.
 * 用注解风格设置HBaseBaseBean的表/列族信息
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface HBaseResultBeanMapping {
    String nameSpace() default "";
    String tableName();
    String columnFamily();
}
