package com.gtg56.idas.common.service;

import com.gtg56.idas.common.core.jdbc.IService;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据角色 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IDataRoleService  extends IService<DataRole> {

    /**
     *
     * @param dataRole
     * @return
     */
    public DataRole saveData(DataRole dataRole);

    /**
     *
     * @param dataRole
     */
    public DataRole updateData(DataRole dataRole);

    /**
     * 根据Code获取数据角色
     * @PARAM CODE
     * @RETURN
     */
    public DataRole findByCode(String code);
}
