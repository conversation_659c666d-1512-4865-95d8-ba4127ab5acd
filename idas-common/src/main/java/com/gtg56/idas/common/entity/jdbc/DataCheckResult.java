package com.gtg56.idas.common.entity.jdbc;

import java.util.Date;
import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <p>
 * 数据完整性检测结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckResult", description="数据完整性检测结果表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_check_result")
public class DataCheckResult extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据完整性检测配置表ID")
    private Long dataCheckConfigId;

    @ApiModelProperty(value = "数据完整性检测配置明细表ID")
    private Long dataCheckConfigDetailId;

    @ApiModelProperty(value = "检测开始日期")
    private Date checkStartDate;

    @ApiModelProperty(value = "检测结束日期")
    private Date checkEndDate;

    @ApiModelProperty(value = "调度时间")
    private Date excuteTime;

    @ApiModelProperty(value = "最后执行时间")
    private Date startTime;

    @ApiModelProperty(value = "最后执行时间")
    private Date endTime;

    @ApiModelProperty(value = "运行时长（秒）")
    private Long useTime;

    @ApiModelProperty(value = "状态正常设备*天数")
    private Integer normalQuantity;

    @ApiModelProperty(value = "状态异常设备*天数")
    private Integer unnormalQuantity;

    @ApiModelProperty(value = "最后执行状态 ０　正常 １　异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;

    @ApiModelProperty(value = "是否删除")
    private Long deletedFlag;

    @Transient
    @ApiModelProperty(value = "数据完整性检测配置名称")
    private String dataCheckConfigName;

    @Transient
    @ApiModelProperty(value = "数据完整性检测配置编码")
    private String dataCheckConfigCode;

    @Transient
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @Transient
    @ApiModelProperty(value = "库房名称")
    private String regionName;
}
