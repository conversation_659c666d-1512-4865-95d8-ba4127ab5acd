package com.gtg56.idas.common.convert;

import com.gtg56.idas.common.entity.dto.auth.DataRoleDTO;
import com.gtg56.idas.common.entity.jdbc.DataRole;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 * 转换类，统一放vo转entity,entity转vo之类的方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public class DataRoleConvert {

    public static BiConsumer<List<DataRole>, List<DataRoleDTO>> toVO() {
        return (dataRoleList, dataRoleVOList) -> {
            //添加自定义转换操作
            //第一个参数为源list，第二个参数为目标list
            for (int i = 0; i < dataRoleVOList.size(); i++) {
                DataRole entity = dataRoleList.get(i);
                DataRoleDTO vo = dataRoleVOList.get(i);
                if(vo != null) {
                    vo.setId(String.valueOf(entity.getId()));
                }
            }
        };
    }

    public static BiConsumer<List<DataRoleDTO>, List<DataRole>> toEntity() {
        return (dataRoleVOList, dataRoleList) -> {
            //添加自定义转换操作

        };
    }

}