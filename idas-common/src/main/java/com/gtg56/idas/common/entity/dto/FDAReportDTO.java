package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class FDAReportDTO implements ImABean {
    
    private static final long serialVersionUID = -8872770521573774980L;
    
    private String fdaCode;
    private Date reportTime;
    private Integer reportCount;
    private String reportExtInfo;
    private String reportContent;
    private String statusMsg;
    private Integer statusFlag;
}
