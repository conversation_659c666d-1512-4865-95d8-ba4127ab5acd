package com.gtg56.idas.common.entity.sensor.kbs;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.Date;

@Data
public class KBSOverLimitHandling implements ImABean {

    private static final long serialVersionUID = 6958174523299205312L;

    private Integer id;
    private String sensorCode;
    private String errMsg;
    private Date startTime;
    private Date handleTime;
    private String suggestion;
    private String principal;



}
