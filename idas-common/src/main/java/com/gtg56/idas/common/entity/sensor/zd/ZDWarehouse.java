package com.gtg56.idas.common.entity.sensor.zd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "泽大仓库记录")
public class ZDWarehouse {
    @ApiModelProperty(value = "企业代码")
    private String qydm;
    @ApiModelProperty(value = "仓库代码")
    private String ckdm;
    @ApiModelProperty(value = "仓库名称")
    private String ckmc;
    @ApiModelProperty(value = "仓库地址")
    private String ckdz;
    @ApiModelProperty(value = "仓库类型")
    private Integer cklx;
}
