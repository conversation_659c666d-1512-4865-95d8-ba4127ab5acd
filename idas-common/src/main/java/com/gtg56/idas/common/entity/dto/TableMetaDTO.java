package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by Link on 2019/12/31.
 */
@Data
public class TableMetaDTO implements ImABean {
    private static final long serialVersionUID = 499979918053990401L;

    private String dataType;
    private String tableName;
    private String namespace;
    private Boolean isHiveTable;

    private String metaStorePath;
    private String dataStorePath;

    private List<String> partitionColumns;
    private Map<String,String> tableProperties;

    private String externalParam;
}
