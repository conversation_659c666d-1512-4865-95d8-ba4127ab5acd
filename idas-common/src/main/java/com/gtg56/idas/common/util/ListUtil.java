package com.gtg56.idas.common.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ListUtil {

    public static <T> List<List<T>> getPages(List<T> c, Integer pageSize) {
        if (c == null || c.isEmpty()) {
            return Collections.emptyList();
        }

        List<T> list = new ArrayList<>(c);

        if (pageSize == null || pageSize <= 0 || pageSize > list.size()) {
            pageSize = list.size();
        }

        int numPages = (int) Math.ceil((double) list.size() / (double) pageSize);

        List<List<T>> pages = new ArrayList<>(numPages);

        for (int pageNum = 0; pageNum < numPages; ) {
            pages.add(list.subList(pageNum * pageSize, Math.min(++pageNum * pageSize, list.size())));
        }

        return pages;
    }
}
