package com.gtg56.idas.common.util;

import com.gtg56.idas.common.core.exception.BizException;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

public class AssertUtil {
    public static void notNull(Object obj,String message) {
        if(obj == null) throw new BizException(message);
    }
    
    public static void notNull(Object obj, BizException.ExceptionInfo exceptionInfo) {
        if(obj == null) throw new BizException(exceptionInfo);
    }
    
    public static void isNull(Object obj,String message) {
        if(obj != null) throw new BizException(message);
    }
    
    public static void isNull(Object obj,BizException.ExceptionInfo exceptionInfo) {
        if(obj != null) throw new BizException(exceptionInfo);
    }
    
    public static void isEmpty(Collection<?> collection,String message) {
        notNull(collection,message);
        if(!collection.isEmpty()) throw new BizException(message);
    }
    
    public static void isNotEmpty(Collection<?> collection,String message) {
        notNull(collection,message);
        if(collection.isEmpty()) throw new BizException(message);
    }
    
    public static void isEmpty(Map<?,?> map, String message) {
        notNull(map,message);
        if(!map.isEmpty()) throw new BizException(message);
    }
    
    public static void isNotEmpty(Map<?,?> map, String message) {
        notNull(map,message);
        if(map.isEmpty()) throw new BizException(message);
    }
    
    public static void isEmpty(Object[] array,String message) {
        notNull(array,message);
        if(array.length > 0) throw new BizException(message);
    }
    
    public static void isNotEmpty(Object[] array,String message) {
        notNull(array,message);
        if(array.length == 0) throw new BizException(message);
    }
    
    public static void isBlank(String str,String message) {
        if(StringUtils.isNotBlank(str)) throw new BizException(message);
    }
    
    public static void isNotBlank(String str,String message) {
        if(StringUtils.isBlank(str)) throw new BizException(message);
    }
    
    public static void stringEquals(String str1,String str2,String message) {
        if(!StringUtils.equals(str1,str2)) throw new BizException(message);
    }
    
    public static void stringEquals(String str1, String str2, BizException.ExceptionInfo exceptionInfo) {
        if(!StringUtils.equals(str1,str2)) throw new BizException(exceptionInfo);
    }
    
    public static void equals(Object obj1, Object obj2, String message) {
        if(!Objects.equals(obj1,obj2)) throw new BizException(message);
    }
    
    public static void isTrue(boolean condition,String message) {
        if(!condition) throw new BizException(message);
    }
    
    public static void isTrue(boolean condition,BizException.ExceptionInfo exceptionInfo) {
        if(!condition) throw new BizException(exceptionInfo);
    }
    
    public static void isFalse(boolean condition,String message) {
        if(condition) throw new BizException(message);
    }
    
    public static void isFalse(boolean condition,BizException.ExceptionInfo exceptionInfo) {
        if(condition) throw new BizException(exceptionInfo);
    }
    
    public static void isFitPattern(String str,String regexPattern,String message) {
        if(StringUtils.isNotBlank(RegExUtils.removePattern(str,regexPattern)))
            throw new BizException(message);
    }
    
    public static void isFitPattern(String str,String regexPattern,BizException.ExceptionInfo exceptionInfo) {
        if(StringUtils.isNotBlank(RegExUtils.removePattern(str,regexPattern)))
            throw new BizException(exceptionInfo);
    }
    
    public static void isNotFitPattern(String str,String regexPattern,String message) {
        if(StringUtils.isBlank(RegExUtils.removePattern(str,regexPattern)))
            throw new BizException(message);
    }
    
    public static void isNotFitPattern(String str,String regexPattern,BizException.ExceptionInfo exceptionInfo) {
        if(StringUtils.isBlank(RegExUtils.removePattern(str,regexPattern)))
            throw new BizException(exceptionInfo);
    }
    
}
