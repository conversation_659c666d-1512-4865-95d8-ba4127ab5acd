package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreatorModifier;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserAuth extends JDBCWithCreatorModifier {
    private static final long serialVersionUID = 8921798280521655417L;
    
    @Column(name = "user_id")
    private String userId;
    
    @Column(name = "username")
    private String username;
    
    @Column(name = "auth_realm")
    private String authRealm;
    
    @Column(name = "auth_subject")
    private String authSubject;
}
