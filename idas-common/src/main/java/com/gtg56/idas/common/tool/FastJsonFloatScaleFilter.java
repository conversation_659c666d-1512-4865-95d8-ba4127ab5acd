package com.gtg56.idas.common.tool;

import com.alibaba.fastjson.serializer.ValueFilter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class FastJsonFloatScaleFilter implements ValueFilter {
    
    private int scale;
    private Set<String> effectKeys;
    
    public FastJsonFloatScaleFilter(int scale,String... effectKeys) {
        this.scale = scale;
        if(effectKeys != null && effectKeys.length > 0) {
            this.effectKeys = new HashSet<>(Arrays.asList(effectKeys));
        }
    }
    
    @Override
    public Object process(Object object, String name, Object value) {
        if( ( effectKeys.isEmpty() || effectKeys.contains(name) ) && value instanceof Number) {
            return new BigDecimal(value.toString()).setScale(scale,BigDecimal.ROUND_CEILING);
        }
        return value;
    }
}
