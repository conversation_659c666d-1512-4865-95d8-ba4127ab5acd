package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.core.jdbc.IMapper;
import com.gtg56.idas.common.entity.dto.sensor.WarehouseSensorDTO;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WarehouseSensorMapper extends IMapper<WarehouseSensor> {
    List<WarehouseSensor> listWarehouse();

    WarehouseSensor getBy(@Param("warehouseCode") String warehouseCode,
                          @Param("regionCode") String regionCode,
                          @Param("sensorCode") String sensorCode);

    List<WarehouseSensor> findBy(WarehouseSensorQuery query);

    List<WarehouseSensor> findBySensorCodes(@Param("sensorCodes") List<String> sensorCodes);

    /**
     * 通过权限ID检查类型为SENSOR的监测点
     * @param permissionId
     * @return
     */
    List<WarehouseSensor> findWarehouseSensorDTOList(@Param("permissionId") Long permissionId);
}
