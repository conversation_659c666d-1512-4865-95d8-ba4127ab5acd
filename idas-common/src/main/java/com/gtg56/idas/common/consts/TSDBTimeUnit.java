package com.gtg56.idas.common.consts;

import lombok.Getter;

public enum  TSDBTimeUnit {
    MILLIS("ms"),
    SECOND("s"),
    MINUTE("m"),
    HOUR("h"),
    DAY("d"),
    WEEK("w"),
    MONTH("n"),
    YEAR("y");
    
    @Getter
    private final String val;
    
    TSDBTimeUnit(String val) {
        this.val = val;
    }
    
    public String toDuration(int amount) {
        return amount + val;
    }
    
    public String toInterval(int amount) {
        return toDuration(amount) + "-ago";
    }
    
    public static String toInterval(int amount, TSDBTimeUnit unit) {
        return unit.toInterval(amount);
    }
}
