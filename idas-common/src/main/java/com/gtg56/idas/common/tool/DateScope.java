package com.gtg56.idas.common.tool;

import com.gtg56.idas.common.util.AssertUtil;
import lombok.Getter;

import java.util.Date;

@Getter
public class DateScope {
    
    
    private final Date start, end;
    
    public DateScope(Date start, Date end) {
        AssertUtil.isTrue(start.before(end), "开始时间必须小于结束时间");
        this.start = start;
        this.end = end;
    }
    
    public boolean inScope(Date date) {
        return start.before(date) && end.after(date);
    }
}
