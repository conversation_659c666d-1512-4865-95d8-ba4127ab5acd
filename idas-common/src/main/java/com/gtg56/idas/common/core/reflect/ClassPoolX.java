package com.gtg56.idas.common.core.reflect;

import javassist.ClassPool;
import javassist.CtClass;

public class ClassPoolX extends ClassPool {
    private ClassPoolX() {
        super(null);
    }
    
    @Override
    public CtClass removeCached(String classname) {
        return super.removeCached(classname);
    }
    
    private static transient ClassPoolX instance;
    
    public static synchronized ClassPoolX getDefault() {
        if(instance == null) {
            instance = new ClassPoolX();
            instance.appendSystemPath();
        }
        return instance;
    }
}
