package com.gtg56.idas.common.node;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;

import java.util.Date;

@Getter
public final class WorkloadNodeInstance {
    
    private final WorkloadNode node;
    
    private final NodeArgsDTO args;
    private final String instanceId;
    
    @Setter
    private String returnJson;

    @Setter
    private Date submitTime,executionTime,endTime;
    @Setter
    private String lastExceptionLocalized;
    
    public WorkloadNodeInstance(WorkloadNode node, NodeArgsDTO args, String instanceId) {
        node.processArgs(args);
        
        this.node = node;
        this.args = args;
        this.instanceId = instanceId;
    }
    
    public void setSpark(SparkSession spark) {
        this.node.setSpark(spark);
    }
    
    public void setLog(Logger log) {
        this.node.setLog(log);
    }
    
    public void dropSpark() {
        this.node.dropSpark();
    }
    
    public String execution() {
        return node.execution(args);
    }
    
    public void beforeExecution() {
        if(requireSpark())
            node.getSpark().sparkContext().setJobGroup(getInstanceId(), getNode().describe(),true);
        node.beforeExecution();
    }
    
    public void afterExecution() {
        if(requireSpark())
            node.getSpark().sparkContext().clearJobGroup();
        node.afterExecution();
    }
    
    public void onException(Exception e) {
        node.onException(e);
    }
    
    public boolean requireSpark() {
        return node.requireSpark();
    }
}
