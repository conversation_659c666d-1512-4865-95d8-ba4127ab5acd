package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WarehouseSensorQuery implements ImABean {
    private static final long serialVersionUID = -8013189954402062818L;

    @ApiModelProperty("厂商编码")
    private String corpCode;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("区域编码")
    private String regionCode;
    @ApiModelProperty("传感器编码")
    private String sensorCode;
    @ApiModelProperty("传感器功能（SENSOR：仓库测点；CAR：冷藏车；BOX：保温箱；OTHER：其他）")
    private String sensorFunction;

    @ApiModelProperty("用户 (前端勿传)")
    private UserDTO user;
//    @ApiModelProperty("传感器类型")
//    private String sensorType;
}
