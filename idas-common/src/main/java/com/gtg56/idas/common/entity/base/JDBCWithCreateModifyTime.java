package com.gtg56.idas.common.entity.base;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.util.Date;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public abstract class JDBCWithCreateModifyTime extends JDBCBaseDO {
    private static final long serialVersionUID = -7582429122483087565L;
    
    @Column(name = "create_time")
    protected Date createTime;
    
    @Column(name = "modify_time")
    protected Date modifyTime;
    
    public void baseSet() {
        Date date = new Date();
        setModifyTime(date);
        if(createTime == null) {
            setCreateTime(date);
        }

        if (id == null) {
            setCreateTime(date);
        }
    }
}
