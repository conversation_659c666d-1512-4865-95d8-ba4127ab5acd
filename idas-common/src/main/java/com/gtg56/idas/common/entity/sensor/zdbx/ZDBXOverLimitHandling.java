package com.gtg56.idas.common.entity.sensor.zdbx;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class ZDBXOverLimitHandling implements ImABean {
    private static final long serialVersionUID = -907215052154514453L;
    private Integer id;
    private Date startTime;
    private Date endTime;
    private Date handleTime;
    private String principal;
    private String suggestion;
    private String nodeIds;
}
