package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户数据角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@ApiModel(value="DataRoleUserQuery", description="用户数据角色表")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataRoleUserQuery extends SimpleQuery implements ImABean {


// "userId,dataRoleId,"
}