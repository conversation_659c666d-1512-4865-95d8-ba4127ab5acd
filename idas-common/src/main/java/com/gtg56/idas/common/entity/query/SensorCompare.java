package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SensorCompare implements ImABean {
    private static final long serialVersionUID = -2186790897254730242L;
    private String warehouseCode;
    private String regionCode;
    private String sensorCode;

    public SensorCompare(WarehouseSensor sensor) {
        this.warehouseCode = sensor.getWarehouseCode();
        this.regionCode = sensor.getRegionCode();
        this.sensorCode = sensor.getSensorCode();
    }

    public SensorCompare(String warehouseCode, String regionCode, String sensorCode) {
        this.warehouseCode = warehouseCode;
        this.regionCode = regionCode;
        this.sensorCode = sensorCode;
    }
}
