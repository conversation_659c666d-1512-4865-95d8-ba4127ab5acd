package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCBaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class GncAppKey extends JDBCBaseDO {
    private static final long serialVersionUID = 6750058570668893397L;

    @ApiModelProperty("app key")
    private String appKey;
    @ApiModelProperty("project name")
    private String projectName;
}
