package com.gtg56.idas.common.entity.sensor.kbs;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class KBSSensorRecordVO implements ImABean {
    private static final long serialVersionUID = -9131357674938758661L;

    private Integer recordId;
    private String orgCode,orgName,nodeCode,nodeName;
    private BigDecimal temperature,humidity,
            temperatureHighLimit,temperatureLowLimit,
            humidityHighLimit,humidityLowLimit;
    private Date recordDateTime;
}
