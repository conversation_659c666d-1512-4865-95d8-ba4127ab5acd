package com.gtg56.idas.common.entity.query;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class FDAReportQuery implements ImABean {
    private static final long serialVersionUID = 3486045286130251609L;
    
    @ApiModelProperty("药监局编码")
    private String fdaCode;
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
    @ApiModelProperty("页码")
    private Integer pageNum;
    @ApiModelProperty("页大小")
    private Integer pageSize;
}
