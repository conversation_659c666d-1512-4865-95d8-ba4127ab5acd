package com.gtg56.idas.common.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WarehouseSensorHistoryQuery extends WarehouseSensorLastQuery {
    
    private static final long serialVersionUID = 7490456235256541144L;
    
    @ApiModelProperty(value = "开始时间，默认当日0点", required = true)
    private Date startTime;
    @ApiModelProperty(value = "结束时间，默认当前时间", required = true)
    private Date endTime;
    
}
