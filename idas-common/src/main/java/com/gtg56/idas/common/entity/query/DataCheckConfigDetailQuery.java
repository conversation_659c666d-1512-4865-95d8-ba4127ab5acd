package com.gtg56.idas.common.entity.query;

import java.util.Date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 数据完整性检测配置明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckConfigDetailQuery", description="数据完整性检测配置明细表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckConfigDetailQuery extends SimpleQuery {

    @ApiModelProperty(value = "数据完整性检测配置ID")
    private String dataCheckConfigId;

    @ApiModelProperty(value = "数据权限ID")
    private String dataPermissionId;

    @ApiModelProperty(value = "创建用户编号 表：ba_user")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "修改用户编号 表：ba_user")
    private Long modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeStart;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeEnd;

    @ApiModelProperty(value = "是否删除")
    private Integer deletedFlag;

//    public Date getCreateTimeStart() {
//        if(createTimeStart != null) {
//            return DateUtil.toDayStart(createTimeStart);
//        }
//        return createTimeStart;
//    }
//    public Date getCreateTimeEnd() {
//        if(createTimeEnd != null) {
//            return DateUtil.toDayEnd(createTimeEnd);
//        }
//        return createTimeEnd;
//    }
//    public Date getModifyTimeStart() {
//        if(modifyTimeStart != null) {
//            return DateUtil.toDayStart(modifyTimeStart);
//        }
//        return modifyTimeStart;
//    }
//    public Date getModifyTimeEnd() {
//        if(modifyTimeEnd != null) {
//            return DateUtil.toDayEnd(modifyTimeEnd);
//        }
//        return modifyTimeEnd;
//    }

// "dataCheckConfigId,dataPermissionId,modifyTime,"
}