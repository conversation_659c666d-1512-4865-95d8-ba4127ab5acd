package com.gtg56.idas.common.entity.sensor.sd;

import com.gtg56.idas.common.entity.base.JsonBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class SDColdCloudSensorInfo implements JsonBean {
    private static final long serialVersionUID = -7563110181442348915L;

    private Integer sensorid;
    private String sn;
    private Integer sensorcode;
    private Integer isenable;
    private Date createdat;
    private Integer deviceid;
    private String devicename;
    private Integer warehouseid;
    private String warehousename;
}
