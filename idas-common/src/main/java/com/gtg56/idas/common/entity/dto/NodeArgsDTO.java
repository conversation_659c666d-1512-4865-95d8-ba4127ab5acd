package com.gtg56.idas.common.entity.dto;

import com.gtg56.idas.common.entity.base.ImABean;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Link on 2019/12/30.
 */
public class NodeArgsDTO extends HashMap<String,String> implements Map<String,String> , ImABean {
    private static final long serialVersionUID = -7192129392055674698L;
    
    public Integer getAsInteger(String key) {
        return get(key) == null ? null : Integer.parseInt(get(key));
    }
    
    public Long getAsLong(String key) {
        return get(key) == null ? null : Long.parseLong(get(key));
    }
    
    public Float getAsFloat(String key) {
        return get(key) == null ? null : Float.parseFloat(get(key));
    }
    
    public Double getAsDouble(String key) {
        return get(key) == null ? null : Double.parseDouble(get(key));
    }
    
    public Boolean getAsBoolean(String key) {
        return get(key) == null ? null : Boolean.parseBoolean(get(key));
    }
}
