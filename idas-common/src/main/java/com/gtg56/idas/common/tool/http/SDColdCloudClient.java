package com.gtg56.idas.common.tool.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.sensor.sd.SDColdCloudSensorHistoryRecord;
import com.gtg56.idas.common.entity.sensor.sd.SDColdCloudSensorInfo;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.util.*;

@Slf4j
public class SDColdCloudClient extends BaseClient {
    private String token;
    private Integer customerId;
    private Integer deviceId;

    public SDColdCloudClient(String token, Integer customerId, Integer deviceId) {
        super("https://www.coldcloud.com");
        this.token = token;
        this.customerId = customerId;
        this.deviceId = deviceId;
    }

    public List<SDColdCloudSensorInfo> listAllSensor() {
        int page = 1;
        Integer count = 100;
        List<SDColdCloudSensorInfo> res = new ArrayList<>();

        while (res.size() < count) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("name", "");
            params.put("page", page++);
            HttpPost request = buildRequest("/localmanagement/getlocalsensorslist", params);
            JSONObject json = request(request);

            if (json != null) {
                JSONObject values = json.getJSONObject("values");
                count = values.getInteger("count");
                JSONArray sensorsList = values.getJSONArray("sensorslist");

                transformArrayToObject(sensorsList, res, SDColdCloudSensorInfo.class);
            } else {
                break;
            }
        }

        return res;
    }

    public List<SDColdCloudSensorHistoryRecord> listHistoryRecord(Date fromDate, Date toDate) {
        int page = 1;
        Integer count = 100;
        List<SDColdCloudSensorHistoryRecord> res = new ArrayList<>();

        while (res.size() < count) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("deviceid", deviceId);
            params.put("isintervalrecord", 1);
            params.put("searchtype", 1);
            params.put("sensorid", null);
            params.put("type", 1);
            params.put("warehouseid", null);
            params.put("fromdate", DateUtil.ymdhms().format(fromDate));
            params.put("todate", DateUtil.ymdhms().format(toDate));

            params.put("page", page++);
            HttpPost request = buildRequest("/datacenter/gethistorytable", params);
            JSONObject json = request(request);

            if (json != null) {

                JSONObject values = json.getJSONObject("values");
                count = values.getJSONObject("summary").getInteger("count");
                JSONArray data = values.getJSONArray("data");

                transformArrayToObject(data, res, SDColdCloudSensorHistoryRecord.class);
            } else {
                break;
            }
        }

        return res;
    }

    private static <T> void transformArrayToObject(JSONArray array, List<T> list, Class<T> clz) {
        for (int i = 0; i < array.size(); i++) {
            list.add(array.getObject(i, clz));
        }
    }

    private HttpPost buildRequest(String uri, Map<String, Object> params) {
        String requestUrl = getBaseAddress() + uri;
        HttpPost request = new HttpPost(requestUrl);

        params.put("customerid", customerId);

//        List<BasicNameValuePair> pairs = new ArrayList<>(params.size());
//        for (String key : params.keySet()) {
//            pairs.add(new BasicNameValuePair(key, Objects.toString(params.get(key))));
//        }
//        request.setEntity(new UrlEncodedFormEntity(pairs, StandardCharsets.UTF_8));

        request.setEntity(new StringEntity(JSON.toJSONString(params), ContentType.APPLICATION_JSON));
        request.addHeader("Authorization", token);

        return request;
    }

    @SneakyThrows
    private JSONObject request(HttpPost request) {
        HttpResponse response = httpClient.execute(request);
        String content = new String(StreamUtil.fromStream(response.getEntity().getContent()));
        if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
            return JSON.parseObject(content);
        } else {
            log.warn("request shudian cold cloud api {} fail {} \n {}",
                    request.getURI().toASCIIString(),
                    response.getStatusLine().getStatusCode(),
                    content);

            return null;
        }
    }

//    @SneakyThrows
//    public static void main(String[] args) {
//        SDColdCloudClient client = new SDColdCloudClient("eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEwMDEsIlVzZXJOYW1lIjoi6ZmG5paH6ImzIiwiVXNlclR5cGVJZCI6NCwiTW9iaWxlUGhvbmUiOiIxNTMzNTI3NTEzNCIsIkN1c3RvbWVySWQiOjQyOCwiaWF0IjoxNjIzMzc5MzUyLCJleHAiOjE2MjU5NzEzNTIsImlzcyI6Im9uc29mdC5jbiJ9.KEApXu7FIWAGjYqHu2fH61hKn67hmvScWxe18FpUtdxSGoGbOT-B-Fq7ZaHbrJ_j8zr6QhsAQWsCT13JAufneg", 428,1596);
//        List<SDColdCloudSensorHistoryRecord> re = client.listHistoryRecord(
//                DateUtil.ymd().parse("2021-05-01"),
//                DateUtil.ymd().parse("2021-05-02"));
//        re.forEach(System.out::println);
//    }
}
