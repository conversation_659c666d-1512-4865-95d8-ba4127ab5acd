package com.gtg56.idas.common.entity.sensor.zdbx;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZDBXDimVO implements ImABean {
    private static final long serialVersionUID = 4316382943353629640L;
    private String regionCode;
    private String regionName;
    private String sensorCodeOrigin;
    private String sensorName;
    private Short sensorType;
    private BigDecimal temperatureHighLimit;
    private BigDecimal temperatureLowLimit;
    private BigDecimal humidityHighLimit;
    private BigDecimal humidityLowLimit;
}
