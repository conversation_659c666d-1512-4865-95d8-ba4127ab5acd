package com.gtg56.idas.common.entity.tsdb.response;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class TSDBQueryResponseDTO implements ImABean {
    private static final long serialVersionUID = -5329939890150554425L;
    
    private String metric;
    private Map<String,String> tags;
    private List<String> aggregateTags;
    private Map<Integer, BigDecimal> dps;
}
