package com.gtg56.idas.common.entity.meta;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
public class SchemaChange implements Comparable<SchemaChange> {
    
    private ChangeType changeType;
    private String source;
    private String target;
    private String columnName;
    private String newDataType;
    private String oldDataType;
    
    @Override
    public int compareTo(SchemaChange o) {
        return StringUtils.compare(this.compareStr(),o.compareStr());
    }
    
    private String compareStr() {
        return source + "." + columnName;
    }
    
    public enum ChangeType {
        AddColumn,
        TypeChange,
        DeleteColumn
    }
}
