package com.gtg56.idas.common.tool.http;

import com.gtg56.idas.common.util.StreamUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.http.HttpEntity;
import org.apache.http.client.HttpClient;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public abstract class BaseClient {

    @Getter
    private final String baseAddress;

    protected final HttpClient httpClient;

    public BaseClient(String baseAddress) {
        this.baseAddress = baseAddress;
        httpClient = HttpClientBuilder.create().build();
    }

    protected final static Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    @SneakyThrows
    protected static HttpEntity formDataToBody(Map<String, Object> formData) {
        StringBuilder formBodyBuilder = new StringBuilder();
        for (Map.Entry<String, Object> singleEntry : formData.entrySet()) {
            if (formBodyBuilder.length() > 0) {
                formBodyBuilder.append("&");
            }
            String key = singleEntry.getKey();
            Object value = singleEntry.getValue();
            if (value == null) continue;

            String valueStr = value.toString();
            formBodyBuilder.append(URLEncoder.encode(key, DEFAULT_CHARSET.displayName()));
            formBodyBuilder.append("=");
            formBodyBuilder.append(URLEncoder.encode(valueStr, DEFAULT_CHARSET.displayName()));
        }
        return new StringEntity(formBodyBuilder.toString());
    }

    @SneakyThrows
    protected static String getResponseString(HttpEntity response) {
        InputStream is = response.getContent();
        return StreamUtil.fromStreamToString(is, DEFAULT_CHARSET.displayName());
    }
}
