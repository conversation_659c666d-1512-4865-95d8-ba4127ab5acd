package com.gtg56.idas.common.entity.sensor.zdbx;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ZDBXSensorRecordVO implements ImABean {
    private static final long serialVersionUID = 7804429828176242121L;
    
    private Integer recordId;
    private String orgCode,orgName,nodeCode,nodeName;
    private BigDecimal temperature,humidity,
            temperatureHighLimit,temperatureLowLimit,
            humidityHighLimit,humidityLowLimit;
    private Date recordDateTime;
}
