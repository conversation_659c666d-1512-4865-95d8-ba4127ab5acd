package com.gtg56.idas.common.entity.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class WarehouseOverlimitHandlingQuery implements ImABean {
    private static final long serialVersionUID = 9001939765334058482L;
    
    @ApiModelProperty("对比对象")
    private List<SensorCompare> compares;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("超标开始时间")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("超标结束时间")
    private Date endTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("超标时间")
    private Date overTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("处理开始时间")
    private Date handleStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("处理结束时间")
    private Date handleEndTime;
}
