package com.gtg56.idas.common.core.kafka;

import lombok.Getter;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.time.Duration;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * Created by Link on 2017/9/14.
 */
public class MQConsumer extends Thread implements Closeable {

    private static Logger log = LoggerFactory.getLogger(MQConsumer.class);

    final private Consumer<String,String> consumer;
    @Getter
    final private Properties properties;
    @Getter
    final private String topic;
    @Getter
    final private String consumerGroupId;
    @Getter
    final private IEventHandler eventHandler;
    final private ExecutorService es;

    private boolean gogogo;

    public MQConsumer(Properties prop, String topic, String cgi, IEventHandler handler, Integer threadCount) {
        this.properties = (Properties) prop.clone();
        this.topic = topic;
        this.consumerGroupId = cgi;
        this.eventHandler=handler;

        this.properties.setProperty("group.id",cgi);

        this.consumer = new KafkaConsumer<>(properties);

        this.es = Executors.newFixedThreadPool(threadCount);

        this.setName("MQConsumer-" + topic);
    }


    @Override
    public void run() {
        this.gogogo = true;
    
        consumer.subscribe(Collections.singletonList(topic));
    
        while (gogogo) {
            try {
                ConsumerRecords<String, String> newEvent = consumer.poll(Duration.ofSeconds(1));

                Iterable<ConsumerRecord<String, String>> records = newEvent.records(topic);
                
                records.forEach(event -> {
                    try {
                        es.submit(new EventHandlerInvokerIgnoreException(eventHandler, event));
                    } catch (Throwable t) {
                        log.warn("MQConsumer try to submit EventHandler but catch exception",t);
                    }
                });

                consumer.commitSync();
            } catch (Throwable t) {
                log.warn("MQConsumer poll Event From Kafka fail",t);
            }
        }

    }

    @Override
    public void close() throws IOException {
        gogogo = false;

        try {
            consumer.close();
            es.shutdownNow();
        } catch (Throwable t) {
            throw new IOException(t);
        }
    }

    /**
     * 对EventHandler不进行failover处理，只日志
     */
    private static class EventHandlerInvokerIgnoreException implements Runnable {
        private IEventHandler handler;
        private ConsumerRecord<String, String> event;
        private EventHandlerInvokerIgnoreException(IEventHandler handler, ConsumerRecord<String, String> event) {
            this.handler = handler;
            this.event = event;
        }

        @Override
        public void run() {
            try {
                handler.handle(event);
            } catch (Throwable t) {
                Class<? extends IEventHandler> eventHandlerClass = handler.getClass();

                LoggerFactory.getLogger(eventHandlerClass).warn("MQConsumer try to invoke " +
                        eventHandlerClass.getName() + ".handle but catch exception ",t);
            }
        }
    }
}
