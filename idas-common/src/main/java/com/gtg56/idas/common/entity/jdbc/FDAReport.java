package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("药监上报")
@Table(name = "fda_report")
public class FDAReport extends JDBCWithCreateModifyTime {
    
    private static final long serialVersionUID = -3330127217290022089L;
    
    @Column(name = "fda_code")
    private String fdaCode;
    
    @Column(name = "report_time")
    private Date reportTime;
    
    @Column(name = "report_count")
    private Integer reportCount;
    
    @Column(name = "report_ext_info")
    private String reportExtInfo;
    
    @Column(name = "report_content")
    private byte[] reportContent;
    
    @Column(name = "report_content_compress")
    private String reportContentCompress;
    
    @Column(name = "status_msg")
    private String statusMsg;
    
    @Column(name = "status_flag")
    private Integer statusFlag;
}
