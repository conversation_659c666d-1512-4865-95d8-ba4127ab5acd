package com.gtg56.idas.common.entity.sensor.zdbx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "中大百迅传感器记录")
public class ZDBXSensorRecord {
    @ApiModelProperty(name = "主键")
    private Integer id;
    @ApiModelProperty(name = "序列号")
    private Integer sequenceNumber;
    @ApiModelProperty(name = "设备编码")
    private String deviceId;
    @ApiModelProperty(name = "节点编码")
    private String nodeId;
    @ApiModelProperty(name = "温度")
    private BigDecimal temperature;
    @ApiModelProperty(name = "湿度")
    private BigDecimal humidity;
    @ApiModelProperty(name = "氧气")
    private BigDecimal oxygen;
    @ApiModelProperty(name = "氨气")
    private BigDecimal ammonia;
    @ApiModelProperty(name = "接收时间")
    private Date receivedDateTime;
    @ApiModelProperty(name = "标记")
    private Integer flag;
    @ApiModelProperty(name = "原始测量数据")
    private String originalData;
    @ApiModelProperty(name = "测量时间")
    private Date recordDateTime;
    @ApiModelProperty(name = "温度状态")
    private Integer temperatureStatus;
    @ApiModelProperty(name = "湿度状态")
    private Integer humidityStatus;
    @ApiModelProperty(name = "氧气状态")
    private Integer oxygenStatus;
    @ApiModelProperty(name = "氨气状态")
    private Integer ammoniaStatus;
    @ApiModelProperty(name = "电量状态")
    private Integer batteryStatus;
    @ApiModelProperty(name = "激活状态")
    private Integer activatedStatus;
    @ApiModelProperty(name = "存储状态")
    private Integer storageStatus;
    @ApiModelProperty(name = "传感器类型")
    private Integer sensorType;
    @ApiModelProperty(name = "当前供电方式",notes = "0：外部供电 1：电池供电")
    private Integer powerSupplyStatus;
}
