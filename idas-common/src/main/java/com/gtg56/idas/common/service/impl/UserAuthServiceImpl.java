package com.gtg56.idas.common.service.impl;

import com.gtg56.idas.common.core.jdbc.AbstractService;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.entity.query.UserAuthQuery;
import com.gtg56.idas.common.mapper.UserAuthMapper;
import com.gtg56.idas.common.service.IUserAuthService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("userAuthService")
public class UserAuthServiceImpl extends AbstractService<UserAuth, UserAuthMapper> implements IUserAuthService {
    @Override
    public List<UserAuth> find(UserAuthQuery query) {
        return mapper.find(query);
    }
}
