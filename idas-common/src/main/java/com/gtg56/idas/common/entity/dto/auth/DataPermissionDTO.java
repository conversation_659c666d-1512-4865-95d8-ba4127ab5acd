package com.gtg56.idas.common.entity.dto.auth;

import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataPermissionDTO", description="数据权限")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
public class DataPermissionDTO extends SimpleVO implements ImABean {

    @ApiModelProperty(value = "权限类型 dictCode: dataRoleType WAREHOUSE REGION SENSOR")
    private String type;

    @ApiModelProperty(value = "权限类型")
    private String typeName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库房编码")
    private String regionCode;

    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @ApiModelProperty(value = "测点终端编码")
    private String sensorCode;

    @ApiModelProperty(value = "测点终端名称")
    private String sensorName;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "备注")
    private String remark;

}