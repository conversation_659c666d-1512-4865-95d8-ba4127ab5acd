package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCWithCreateModifyTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 数据权限
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@ApiModel(value="DataPermission", description="数据权限")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Entity
@Table(name = "data_permission")
public class DataPermission extends JDBCWithCreateModifyTime {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权限类型 dictCode:dataRoleType")
    private String type;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库房编码")
    private String regionCode;

    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @ApiModelProperty(value = "测点终端编码")
    private String sensorCode;

    @ApiModelProperty(value = "测点终端名称")
    private String sensorName;

    @ApiModelProperty(value = "创建者ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改者ID")
    private Long modifier;
}
