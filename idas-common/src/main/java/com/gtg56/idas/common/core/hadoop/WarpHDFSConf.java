package com.gtg56.idas.common.core.hadoop;

import com.gtg56.idas.common.util.StreamUtil;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * Created by Link on 2017/9/28.
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class WarpHDFSConf extends Configuration {

    @SneakyThrows
    protected InputStream fileOrResources(String filePath, String resourcesPath) {
        File file = new File(filePath);
        if (file.exists()) {
            log.info("use local configuration file {}", filePath);
            return new FileInputStream(file);
        } else {
            log.info("use jar resources file {}", resourcesPath);
            return StreamUtil.resourceInputStream(resourcesPath);
        }
    }

    @SneakyThrows
    private InputStream core() {
        return fileOrResources("/etc/hadoop/conf/core-site.xml", "hadoop/core-site.xml");
    }

    @SneakyThrows
    private InputStream hdfs() {
        return fileOrResources("/etc/hadoop/conf/hdfs-site.xml", "hadoop/hdfs-site.xml");
    }
    
    @SneakyThrows
    private InputStream mapred() {
        return fileOrResources("/etc/hadoop/conf/mapred-site.xml", "hadoop/mapred-site.xml");
    }
    
    @SneakyThrows
    private InputStream yarn() {
        return fileOrResources("/etc/hadoop/conf/yarn-site.xml", "hadoop/yarn-site.xml");
    }
    
    public WarpHDFSConf() {
        super(true);
    }
    
    public void init() {
        super.addResource(core());
        super.addResource(hdfs());
        super.addResource(mapred());
        super.addResource(yarn());
    }

}
