package com.gtg56.idas.common.tool.compress;

import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;

import java.io.*;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class GZipCompressor implements Compressor {
    
    @Override
    public String name() {
        return "gzip";
    }
    
    @SneakyThrows
    @Override
    public byte[] compress(byte[] plain) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        compress(new ByteArrayInputStream(plain), bos);
        return bos.toByteArray();
    }
    
    @Override
    public void compress(InputStream plain, OutputStream out) throws IOException {
        GZIPOutputStream gz = new GZIPOutputStream(out);
        StreamUtil.inToOut(plain, gz);
    }
    
    @SneakyThrows
    @Override
    public byte[] decompress(byte[] compressed) {
        ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        decompress(bis, bos);
        return bos.toByteArray();
    }
    
    @Override
    public void decompress(InputStream compressed, OutputStream out) throws IOException {
        GZIPInputStream gz = new GZIPInputStream(compressed);
        StreamUtil.inToOut(gz, out);
    }
}
