package com.gtg56.idas.common.entity.query;

import java.util.Date;
import com.gtg56.lark.common.base.query.SimpleQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 数据完整性检测结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckResultQuery", description="数据完整性检测结果表")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckResultQuery extends SimpleQuery {

    @ApiModelProperty(value = "数据完整性检测配置名称")
    private String dataCheckConfigName;

    @ApiModelProperty(value = "数据完整性检测配置编码")
    private String dataCheckConfigCode;

    @ApiModelProperty(value = "数据完整性检测配置表ID")
    private String dataCheckConfigId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "调度时间")
    private String excuteTimeStart;

    @ApiModelProperty(value = "调度时间")
    private String excuteTimeEnd;

    @ApiModelProperty(value = "最后执行时间")
    private String startTimeStart;

    @ApiModelProperty(value = "最后执行时间")
    private String startTimeEnd;

    @ApiModelProperty(value = "最后执行时间")
    private String endTimeStart;

    @ApiModelProperty(value = "最后执行时间")
    private String endTimeEnd;

    @ApiModelProperty(value = "运行时长（秒）")
    private Long useTime;

    @ApiModelProperty(value = "最后执行状态 ０　正常 １　异常")
    private String checkStatus;

    @ApiModelProperty(value = "创建用户编号 表：ba_user")
    private String creatorId;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "修改用户编号 表：ba_user")
    private Long modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeStart;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTimeEnd;

    @ApiModelProperty(value = "是否删除")
    private Integer deletedFlag;

//    public Date getExcuteTimeStart() {
//        if(excuteTimeStart != null) {
//            return DateUtil.toDayStart(excuteTimeStart);
//        }
//        return excuteTimeStart;
//    }
//    public Date getExcuteTimeEnd() {
//        if(excuteTimeEnd != null) {
//            return DateUtil.toDayEnd(excuteTimeEnd);
//        }
//        return excuteTimeEnd;
//    }
//    public Date getStartTimeStart() {
//        if(startTimeStart != null) {
//            return DateUtil.toDayStart(startTimeStart);
//        }
//        return startTimeStart;
//    }
//    public Date getStartTimeEnd() {
//        if(startTimeEnd != null) {
//            return DateUtil.toDayEnd(startTimeEnd);
//        }
//        return startTimeEnd;
//    }
//    public Date getEndTimeStart() {
//        if(endTimeStart != null) {
//            return DateUtil.toDayStart(endTimeStart);
//        }
//        return endTimeStart;
//    }
//    public Date getEndTimeEnd() {
//        if(endTimeEnd != null) {
//            return DateUtil.toDayEnd(endTimeEnd);
//        }
//        return endTimeEnd;
//    }
//    public Date getCreateTimeStart() {
//        if(createTimeStart != null) {
//            return DateUtil.toDayStart(createTimeStart);
//        }
//        return createTimeStart;
//    }
//    public Date getCreateTimeEnd() {
//        if(createTimeEnd != null) {
//            return DateUtil.toDayEnd(createTimeEnd);
//        }
//        return createTimeEnd;
//    }
//    public Date getModifyTimeStart() {
//        if(modifyTimeStart != null) {
//            return DateUtil.toDayStart(modifyTimeStart);
//        }
//        return modifyTimeStart;
//    }
//    public Date getModifyTimeEnd() {
//        if(modifyTimeEnd != null) {
//            return DateUtil.toDayEnd(modifyTimeEnd);
//        }
//        return modifyTimeEnd;
//    }

// "dataCheckConfigId,remark,excuteTime,startTime,endTime,useTime,checkStatus,modifyTime,"
}