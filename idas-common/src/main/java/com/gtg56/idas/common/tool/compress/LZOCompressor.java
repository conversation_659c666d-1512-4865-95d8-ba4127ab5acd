package com.gtg56.idas.common.tool.compress;

import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;
import org.anarres.lzo.*;

import java.io.*;

public class LZOCompressor implements Compressor {
    
    private final static LzoAlgorithm algorithm = LzoAlgorithm.LZO1X;
    private final static LzoConstraint constraint = LzoConstraint.COMPRESSION;
    
    @Override
    public String name() {
        return "lzo";
    }
    
    @SneakyThrows
    @Override
    public byte[] compress(byte[] plain) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ByteArrayInputStream bis = new ByteArrayInputStream(plain);
        compress(bis, bos);
        return bos.toByteArray();
    }
    
    @Override
    public void compress(InputStream plain, OutputStream out) throws IOException {
        LzoCompressor lzo = LzoLibrary.getInstance().newCompressor(algorithm, constraint);
        LzoOutputStream los = new LzoOutputStream(out, lzo, 1024);
        StreamUtil.inToOut(plain, los);
    }
    
    @SneakyThrows
    @Override
    public byte[] decompress(byte[] compressed) {
        ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        decompress(bis, bos);
        return bos.toByteArray();
    }
    
    @Override
    public void decompress(InputStream compressed, OutputStream out) throws IOException {
        LzoDecompressor lzo = LzoLibrary.getInstance().newDecompressor(algorithm, constraint);
        LzoInputStream lis = new LzoInputStream(compressed, lzo);
        StreamUtil.inToOut(lis, out);
    }
}
