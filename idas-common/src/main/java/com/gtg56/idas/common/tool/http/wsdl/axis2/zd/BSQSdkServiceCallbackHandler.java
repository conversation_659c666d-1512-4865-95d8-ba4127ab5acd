/**
 * BSQSdkServiceCallbackHandler.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis2 version: 1.7.9  Built on : Nov 16, 2018 (12:05:37 GMT)
 */
package com.gtg56.idas.common.tool.http.wsdl.axis2.zd;


/**
 *  BSQSdkServiceCallbackHandler Callback class, Users can extend this class and implement
 *  their own receiveResult and receiveError methods.
 */
@SuppressWarnings("ALL")
public abstract class BSQSdkServiceCallbackHandler {
    protected Object clientData;

    /**
     * User can pass in any object that needs to be accessed once the NonBlocking
     * Web service call is finished and appropriate method of this CallBack is called.
     * @param clientData Object mechanism by which the user can pass in user data
     * that will be avilable at the time this callback is called.
     */
    public BSQSdkServiceCallbackHandler(Object clientData) {
        this.clientData = clientData;
    }

    /**
     * Please use this constructor if you don't want to set any clientData
     */
    public BSQSdkServiceCallbackHandler() {
        this.clientData = null;
    }

    /**
     * Get the client data
     */
    public Object getClientData() {
        return clientData;
    }

    /**
     * auto generated Axis2 call back method for getCorpInfo method
     * override this method for handling normal response from getCorpInfo operation
     */
    public void receiveResultgetCorpInfo(
            BSQSdkServiceStub.GetCorpInfoResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getCorpInfo operation
     */
    public void receiveErrorgetCorpInfo(java.lang.Exception e) {
    }

    /**
     * auto generated Axis2 call back method for getDeviceAlarm method
     * override this method for handling normal response from getDeviceAlarm operation
     */
    public void receiveResultgetDeviceAlarm(
            BSQSdkServiceStub.GetDeviceAlarmResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getDeviceAlarm operation
     */
    public void receiveErrorgetDeviceAlarm(java.lang.Exception e) {
    }

    /**
     * auto generated Axis2 call back method for getCorpDevice method
     * override this method for handling normal response from getCorpDevice operation
     */
    public void receiveResultgetCorpDevice(
            BSQSdkServiceStub.GetCorpDeviceResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getCorpDevice operation
     */
    public void receiveErrorgetCorpDevice(java.lang.Exception e) {
    }

    /**
     * auto generated Axis2 call back method for getCurrentTempHumi method
     * override this method for handling normal response from getCurrentTempHumi operation
     */
    public void receiveResultgetCurrentTempHumi(
            BSQSdkServiceStub.GetCurrentTempHumiResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getCurrentTempHumi operation
     */
    public void receiveErrorgetCurrentTempHumi(java.lang.Exception e) {
    }

    /**
     * auto generated Axis2 call back method for getCorpWarehouse method
     * override this method for handling normal response from getCorpWarehouse operation
     */
    public void receiveResultgetCorpWarehouse(
            BSQSdkServiceStub.GetCorpWarehouseResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getCorpWarehouse operation
     */
    public void receiveErrorgetCorpWarehouse(java.lang.Exception e) {
    }

    /**
     * auto generated Axis2 call back method for getDeviceTempHumi method
     * override this method for handling normal response from getDeviceTempHumi operation
     */
    public void receiveResultgetDeviceTempHumi(
            BSQSdkServiceStub.GetDeviceTempHumiResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from getDeviceTempHumi operation
     */
    public void receiveErrorgetDeviceTempHumi(java.lang.Exception e) {
    }
}
