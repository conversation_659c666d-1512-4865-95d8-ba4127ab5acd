package com.gtg56.idas.common.mapper;

import com.gtg56.idas.common.core.jdbc.IMapper;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface DataRoleMapper extends IMapper<DataRole> {

    int updateById(DataRole entity);

    DataRole selectByCode(@Param("code")String code);
}
