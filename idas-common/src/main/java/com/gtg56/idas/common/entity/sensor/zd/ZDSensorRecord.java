package com.gtg56.idas.common.entity.sensor.zd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "泽大温湿度记录")
public class ZDSensorRecord {
    @ApiModelProperty(value = "设备代码")
    private String sbdm;
    @ApiModelProperty(value = "温度值")
    private String wdz;
    @ApiModelProperty(value = "湿度值")
    private String sdz;
    @ApiModelProperty(value = "采集时间")
    private String cjsj;
    @ApiModelProperty(value = "经度")
    private String lng;
    @ApiModelProperty(value = "纬度")
    private String lat;

}
