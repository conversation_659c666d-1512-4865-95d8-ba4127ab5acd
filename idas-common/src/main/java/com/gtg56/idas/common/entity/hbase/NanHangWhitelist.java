package com.gtg56.idas.common.entity.hbase;

import com.gtg56.idas.common.core.hbase.HBaseResultBeanMapping;
import com.gtg56.idas.common.entity.base.StringRowKeyHBaseBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

import java.io.UnsupportedEncodingException;

@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Data
@HBaseResultBeanMapping(nameSpace = "test",tableName = "nanhang_whitelist",columnFamily = "info")
public class NanHangWhitelist extends StringRowKeyHBaseBaseDO {
    private static final long serialVersionUID = 6314126049076910307L;
    
    private String whiteListGroup;
    private String name;
    private String idNum;
    private String sex;
    private String phone;
    
    
    private static final String[] familyNames = {"张","王","李","赵","何","史","廖","林","白","蔡"};
    
    public static NanHangWhitelist random(String whiteListGroup) {
        String name = familyNames[RandomUtils.nextInt(0,familyNames.length)]
                + (RandomUtils.nextInt(0,10) < 3 ? getRandomChar() : getRandomChar() + getRandomChar());
        String idNum = RandomStringUtils.random(16,false,true);
        String sex = RandomUtils.nextInt(0,100) > 51 ? "女" : "男";
        String phone = "1" + RandomStringUtils.random(10,false,true);
    
        NanHangWhitelist nanHangWhiteList = new NanHangWhitelist()
                .setIdNum(idNum)
                .setName(name)
                .setSex(sex)
                .setPhone(phone)
                .setWhiteListGroup(whiteListGroup);
        
        nanHangWhiteList.autoSetRowKey();
        return nanHangWhiteList;
    }
    
    private static String getRandomChar() {
        String str = "";
        int highCode;
        int lowCode;
        
        highCode = (176 + Math.abs(RandomUtils.nextInt(0,39))); //B0 + 0~39(16~55) 一级汉字所占区
        lowCode = (161 + Math.abs(RandomUtils.nextInt(0,93))); //A1 + 0~93 每区有94个汉字
        
        byte[] b = new byte[2];
        b[0] = (Integer.valueOf(highCode)).byteValue();
        b[1] = (Integer.valueOf(lowCode)).byteValue();
        
        try {
            str = new String(b, "GBK");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return str;
    }
    
    public void autoSetRowKey() {
        toRowKeyAndSet(whiteListGroup + "-" + idNum);
    }
}
