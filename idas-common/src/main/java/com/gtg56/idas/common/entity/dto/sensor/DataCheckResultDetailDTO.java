package com.gtg56.idas.common.entity.dto.sensor;

import java.util.Date;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.lark.common.base.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.persistence.Transient;

/**
 * <p>
 * 数据完整性检测结果明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@ApiModel(value="DataCheckResultDetailDTO", description="数据完整性检测结果明细表")
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataCheckResultDetailDTO extends SimpleVO implements ImABean {

    @ApiModelProperty(value = "数据完整性检测配置名称")
    private String dataCheckConfigName;

    @ApiModelProperty(value = "数据完整性检测配置编码")
    private String dataCheckConfigCode;

    @ApiModelProperty(value = "数据日期")
    private Date tradeDate;

    @ApiModelProperty(value = "数据完整性检测配置表ID")
    private Long dataCheckConfigDetailId;

    @ApiModelProperty(value = "数据权限ID")
    private Long warehouseSensorId;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库房编码")
    private String regionCode;

    @ApiModelProperty(value = "库房名称")
    private String regionName;

    @ApiModelProperty(value = "测点终端编码")
    private String sensorCode;

    @ApiModelProperty(value = "测点终端名称")
    private String sensorName;

    @ApiModelProperty(value = "缺失数据数量")
    private int missQuantity;

    @ApiModelProperty(value = "检测时间")
    private Date checkTime;

    @ApiModelProperty(value = "复核时间")
    private Date recheckTime;

    @ApiModelProperty(value = "最后执行状态 ０　正常  １　异常")
    private Integer checkStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}