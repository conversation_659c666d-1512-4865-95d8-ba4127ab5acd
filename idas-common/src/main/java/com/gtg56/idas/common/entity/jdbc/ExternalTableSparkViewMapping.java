package com.gtg56.idas.common.entity.jdbc;

import com.gtg56.idas.common.entity.base.JDBCBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "external_table_spark_view_mapping")
public class ExternalTableSparkViewMapping extends JDBCBaseDO {
    
    private static final long serialVersionUID = 3627219332221239080L;
    
    @Column(name = "database_name")
    private String databaseName;
    
    @Column(name = "view_name")
    private String viewName;
}
