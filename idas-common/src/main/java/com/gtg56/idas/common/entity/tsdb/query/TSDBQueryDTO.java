package com.gtg56.idas.common.entity.tsdb.query;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.TSDBAggregator;
import com.gtg56.idas.common.consts.TSDBDownSample;
import com.gtg56.idas.common.consts.TSDBFillPolicy;
import com.gtg56.idas.common.consts.TSDBTimeUnit;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.util.AssertUtil;
import com.gtg56.idas.common.util.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Data
@Accessors(chain = true)
public class TSDBQueryDTO implements ImABean {
    private static final long serialVersionUID = -2175305892026702571L;
    
    private TSDBQueryDTO() {
        this.queries = new LinkedList<>();
    }
    
    public TSDBQueryDTO(int amount, TSDBTimeUnit timeUnit) {
        this();
        this.start = timeUnit.toInterval(amount);
    }
    
    public TSDBQueryDTO(Date start) {
        this();
        this.start = DateUtil.tsdb().format(start);
    }
    
    private String start;
    private String end;
    private List<QueryMetric> queries;
    
    public TSDBQueryDTO setEnd(int amount,TSDBTimeUnit timeUnit) {
        this.end = timeUnit.toInterval(amount);
        return this;
    }
    
    public TSDBQueryDTO setEnd(Date end) {
        this.end = DateUtil.tsdb().format(end);
        return this;
    }
    
    public synchronized TSDBQueryDTO addQueryMetric(QueryMetric queryMetric) {
        this.queries.add(queryMetric);
        return this;
    }
    
    @Data
    @Accessors(chain = true)
    public static class QueryMetric implements ImABean {
        
        private static final long serialVersionUID = 4977505042220081175L;
        
        public QueryMetric() {
            this.tags = new LinkedHashMap<>();
        }
        
        private String metric;
        private String aggregator;
        private String downsample;
        private Map<String, String> tags;
        private List<Filter> filters;
    
        public QueryMetric setDownsample(String duration, String aggregator, String fillPolicy) {
            AssertUtil.isNotBlank(duration, "duration cannot be blank");
            AssertUtil.isNotBlank(aggregator, "aggregator cannot be blank");
            if (StringUtils.isNotBlank(fillPolicy)) {
                this.downsample = duration + "-" + aggregator + "-" + fillPolicy;
            } else {
                this.downsample = duration + "-" + aggregator + "-" + TSDBFillPolicy.NONE.getName();
            }
            return this;
        }
    
        public QueryMetric setDownsample(int amount, TSDBTimeUnit timeUnit, TSDBAggregator aggregator, TSDBFillPolicy fillPolicy) {
            return this.setDownsample(TSDBDownSample.downSample(amount, timeUnit, aggregator, fillPolicy));
        }
    
        public QueryMetric setDownsample(String interval, TSDBAggregator aggregator, TSDBFillPolicy fillPolicy) {
            return this.setDownsample(interval, aggregator.getName(), fillPolicy.getName());
        }
        
        public QueryMetric setAggregator(TSDBAggregator aggregator) {
            this.aggregator = aggregator.getName();
            return this;
        }
        
        public QueryMetric addTag(String tagK,String tagV) {
            if (StringUtils.isNotBlank(tagK) && StringUtils.isNotBlank(tagV)) {
                this.tags.put(tagK, tagV);
            }
            return this;
        }
        
        public QueryMetric addTags(Map<String, String> tags) {
            tags.forEach(this::addTag);
            return this;
        }
        
    }
    
    @Data
    @Accessors(chain = true)
    public static class Filter implements ImABean {
        private static final long serialVersionUID = 3340090755586508707L;
        private String type;
        private String tagk;
        private String filter;
        private boolean groupBy = false;
    }
    
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
