package com.gtg56.idas.common.entity.dto.sensor;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SensorRecordDTO implements ImABean {
    private static final long serialVersionUID = -8180536624318136426L;
    
    private String warehouseCode;
    private String warehouseName;
    private String regionCode;
    private String sensorCode;
    @ApiModelProperty("温度")
    private BigDecimal temperature;
    @ApiModelProperty("湿度")
    private BigDecimal humidity;
    @ApiModelProperty("温度状态")
    private String temperatureStatus;
    @ApiModelProperty("湿度状态")
    private String humidityStatus;
    @ApiModelProperty("记录时间")
    private Date recordTime;
    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHighLimit;
    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLowLimit;
    @ApiModelProperty("湿度上限")
    private BigDecimal humidityHighLimit;
    @ApiModelProperty("湿度下限")
    private BigDecimal humidityLowLimit;
}
