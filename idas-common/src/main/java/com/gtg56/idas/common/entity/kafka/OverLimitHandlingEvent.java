package com.gtg56.idas.common.entity.kafka;

import com.gtg56.idas.common.entity.base.ImABean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class OverLimitHandlingEvent implements ImABean {
    private static final long serialVersionUID = 6857767753909504712L;
    
    @ApiModelProperty("温湿度监控系统厂商编码")
    private String corpCode;
    @ApiModelProperty("仓库编码")
    private String warehouseCode;
    @ApiModelProperty("区域编码")
    private String regionCode;
    @ApiModelProperty("传感器编码")
    private String sensorCode;
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
    @ApiModelProperty("处理时间")
    private Date handleTime;
    @ApiModelProperty("处理人")
    private String principal;
    @ApiModelProperty("处理意见")
    private String suggestion;
    @ApiModelProperty("是否时间范围")
    private Boolean range;
    @ApiModelProperty("温度上限")
    private BigDecimal temperatureHighLimit;
    @ApiModelProperty("温度下限")
    private BigDecimal temperatureLowLimit;
    @ApiModelProperty("湿度上限")
    private BigDecimal humidityHighLimit;
    @ApiModelProperty("湿度下限")
    private BigDecimal humidityLowLimit;
}
