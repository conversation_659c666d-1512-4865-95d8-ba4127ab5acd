package com.gtg56.idas.common.entity.sensor.zxly;


import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;

import java.util.Date;

@Data
public class ZXLYRealTempHum implements ImABean {
    private static final long serialVersionUID = 1906267599528309647L;

    private Long id;
    private String areaCode;
    private String wzName;
    private String deviceCode;
    private String ipAddr;
    private Float wdz;
    private Float sdz;
    private Date dt;
    private String remark;
    private String kfsx;
    private String gpsjd;
    private String gpswd;
    private Float wdbjsx;
    private Float wdbjxx;
    private Float sdbjsx;
    private Float sdbjxx;


}
