package com.gtg56.idas.common.tool.compress;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public interface Compressor {
    String name();
    
    byte[] compress(byte[] plain);
    
    void compress(InputStream plain, OutputStream out) throws IOException;
    
    byte[] decompress(byte[] compressed);
    
    void decompress(InputStream compressed, OutputStream out) throws IOException;
    
    ;
}
