package com.gtg56.idas.common.entity.base;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;

public interface JsonBean extends ImABean {
    default String toJson() {
        return JSON.toJSONString(this);
    }

    default void fromJson(String json) {
        JsonBean bean = JSON.parseObject(json, this.getClass());
        BeanUtils.copyProperties(bean, this);
    }
}
