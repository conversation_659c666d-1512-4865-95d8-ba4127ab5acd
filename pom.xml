<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gtg56</groupId>
    <artifactId>idas</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <developers>
        <developer>
            <id>wang<PERSON><PERSON>n</id>
            <name>王嘉麟</name>
        </developer>
        <developer>
            <id>zhangchuyi</id>
            <name>张楚义</name>
        </developer>

    </developers>

    <description>
        <![CDATA[
            这里是广交物流数据平台项目根pom

            本pom主要维护：
            项目主版本
            开发者列表
            子模块列表

            其他配置请移步idas-parent/pom.xml

            维护规约：
            1、对本项目进行过任何修改者，请在上方
               developers处输入您的尊姓大名。
               拼音为id，中文名为name；邮箱等选填。
        ]]>
    </description>

    <modules>
        <module>idas-parent</module>

        <module>idas-common</module>

        <module>idas-inner-client</module>

        <module>idas-service-define</module>
        <module>idas-service-spark</module>
        <module>idas-service-flink</module>
        <module>idas-service-nodeProvider</module>
        <module>idas-service-sensor</module>
        <module>idas-service-auth</module>

        <module>idas-service-collect</module>

        <module>idas-api-facade</module>
        <module>idas-api-external</module>
    </modules>


</project>