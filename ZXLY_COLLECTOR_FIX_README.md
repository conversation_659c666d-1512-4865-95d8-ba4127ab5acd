# ZXLYCollector OpenTSDB 400错误修复

## 问题描述

ZXLYCollector在执行同步任务时报错：
```
java.lang.RuntimeException: put datapoint to openTSDB fail , status code : 400 message {"error":{"code":400,"message":"One or more data points had errors"}}
```

## 问题分析

OpenTSDB返回400状态码表示数据点格式有问题，常见原因包括：

1. **空值处理问题**：温湿度数值(wdz、sdz)或限制值可能为null
2. **无效数值**：数值可能为NaN、Infinity等无效值
3. **时间戳问题**：时间戳可能无效或超出合理范围
4. **标签值问题**：标签中可能包含空值、空字符串或非法字符（如空格、特殊字符）

## 修复方案

### 1. 数据验证过滤
- 添加`isValidRecord()`方法验证记录的基本有效性
- 检查必要字段（ID、日期、区域代码、设备代码）是否为空

### 2. 安全数值转换
- 添加`safeConvertToBigDecimal()`方法安全转换Float到BigDecimal
- 处理null值和无效数值（NaN、Infinity）

### 3. 数值范围验证
- 添加`isValidValue()`方法验证温湿度数值是否在合理范围内
- 温度范围：-50°C 到 150°C
- 湿度范围：0% 到 150%（放宽范围以适应不同传感器）

### 4. 时间戳验证
- 添加`validateTimestamp()`方法验证时间戳有效性
- 检查时间戳是否在合理范围内（过去10年到未来1小时）

### 5. 标签值清理
- 添加`cleanTagValue()`方法清理标签值
- 移除空白字符，验证是否包含OpenTSDB不支持的字符

### 6. 异常处理增强
- 在数据处理和发送过程中添加try-catch块
- 记录详细的错误日志，包括具体的数据内容

## 修改的文件

### 主要修改
- `idas-service-collect/src/main/java/com/gtg56/idas/service/collect/worker/ZXLYCollector.java`

### 新增测试
- `idas-service-collect/src/test/java/com/gtg56/idas/service/collect/worker/ZXLYCollectorTest.java`

## 修改详情

### 1. syncSensorRecord方法改进
```java
List<WarehouseSensorRecord> produce = realList.parallelStream()
    .peek(record -> record.setDeviceCode(strWarehouseCode + "-" + record.getDeviceCode()))
    .filter(this::isValidRecord) // 添加数据验证过滤
    .flatMap(record -> {
        try {
            // 安全地转换数值，处理null值
            BigDecimal temperature = safeConvertToBigDecimal(record.getWdz());
            BigDecimal humidity = safeConvertToBigDecimal(record.getSdz());
            
            // 验证温湿度数值有效性
            if (!isValidValue(temperature) || !isValidValue(humidity)) {
                log.warn("Invalid temperature or humidity value for record ID: {}, temp: {}, hum: {}", 
                        record.getId(), temperature, humidity);
                return Stream.empty();
            }
            
            // ... 其他验证和处理逻辑
        } catch (Exception e) {
            log.error("Error processing record ID: {}, error: {}", record.getId(), e.getMessage(), e);
            return Stream.empty();
        }
    })
    .filter(record -> record.getTags() != null)
    .collect(Collectors.toList());
```

### 2. 新增辅助方法
- `isValidRecord()` - 验证记录基本有效性
- `safeConvertToBigDecimal()` - 安全数值转换
- `isValidValue()` - 数值范围验证
- `validateTimestamp()` - 时间戳验证
- `cleanTagValue()` - 标签值清理
- `isEmptyOrWhitespace()` - 空白字符检查

### 3. 错误处理改进
```java
produce.forEach(record -> {
    try {
        log.debug("Sending data to OpenTSDB: {}", record.toPutBody());
        openTSDBClient.put(record);
        sensorRecordProducer.produce(strWarehouseCode,record.toPutBody());
    } catch (Exception e) {
        log.error("Failed to send data to OpenTSDB for sensor: {}, error: {}, data: {}", 
                record.getTags().getSensorCode(), e.getMessage(), record.toPutBody(), e);
    }
});
```

## 测试建议

1. **运行单元测试**：
   ```bash
   mvn test -Dtest=ZXLYCollectorTest
   ```

2. **启用DEBUG日志**：
   在application.yml中设置：
   ```yaml
   logging:
     level:
       com.gtg56.idas.service.collect.worker.ZXLYCollector: DEBUG
   ```

3. **监控日志**：
   关注以下日志信息：
   - 数据验证失败的警告日志
   - OpenTSDB发送失败的错误日志
   - 处理的数据点详情（DEBUG级别）

## 预期效果

1. **减少400错误**：通过数据验证过滤掉无效数据点
2. **提高数据质量**：确保发送到OpenTSDB的数据都是有效的
3. **增强可观测性**：详细的日志帮助快速定位问题
4. **提高系统稳定性**：异常处理防止单个数据点错误影响整个批次

## 注意事项

1. 修复后可能会过滤掉一些数据，这是正常的，因为这些数据本身就是无效的
2. 建议在生产环境部署前先在测试环境验证
3. 可以根据实际业务需求调整数值范围验证的阈值
4. 如果发现大量数据被过滤，需要检查数据源的数据质量
