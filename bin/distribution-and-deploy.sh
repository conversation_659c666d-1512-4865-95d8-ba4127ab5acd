#!/bin/bash
set -e

pattern=$1
port=$2
profile=$3
nodes=$(grep -E $pattern /etc/hosts | awk '{print $2}' | xargs)

chmod +x bin/*
chmod +x idas-service-collect/bin/*
chmod +x idas-service-nodeProvider/bin/*
chmod +x idas-service-spark/bin/*
chmod +x idas-service-sensor/bin/*
chmod +x idas-service-auth/bin/*
#chmod +x idas-service-flink/bin/*
chmod +x idas-api-facade/bin/*
chmod +x idas-api-external/bin/*
chmod +x idas-inner-client/bin/*

rm -f idas-inner-client/bin/startup.sh
ln -s ./startup-$profile.sh idas-inner-client/bin/startup.sh

function dist() {
  node=$1
  port=$2
  echo "**** distribution to $node:$port start ****"
  sudo ssh -p $port root@$node rm -rf /data/src/gtg56/idas/*/target/
  sudo ssh -p $port root@$node rm -rf /data/src/gtg56/idas/jars/*
  sudo ssh -p $port root@$node rm -rf /data/src/gtg56/idas/bin/*
  sudo ssh -p $port root@$node rm -rf /data/src/gtg56/idas/*/bin/*.sh

  sudo scp -P $port -r ./* root@$node:/data/src/gtg56/idas/
  sudo scp -P $port bin/spark-shellx root@$node:/usr/local/bin/
  echo "**** distribution to $node:$port done ****"
}

for node in $nodes; do
  dist $node $port &
done

#fs=$(hdfs getconf -confKey fs.defaultFS)
#echo "deploy to $fs start"
#sudo -u hdfs hadoop fs -rm -r -f /user/hdfs/idas/sparkService/newJars/
#sudo -u hdfs hadoop fs -rm -r -f /user/hdfs/idas/sparkService/oldJars/
#sudo -u hdfs hadoop fs -mkdir -p /user/hdfs/idas/sparkService/newJars/
#
#sudo -u hdfs hadoop fs -put idas-service-spark/target/lib/* /user/hdfs/idas/sparkService/newJars/
#
#sudo -u hdfs hadoop fs -mv /user/hdfs/idas/sparkService/jars /user/hdfs/idas/sparkService/oldJars
#sudo -u hdfs hadoop fs -mv /user/hdfs/idas/sparkService/newJars /user/hdfs/idas/sparkService/jars
#sudo -u hdfs hadoop fs -rm -r -f /user/hdfs/idas/sparkService/oldJars/
#
#echo "deploy to $fs done"
