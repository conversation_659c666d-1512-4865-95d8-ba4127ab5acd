#!/bin/bash
set -e
pidBaseDir="/tmp"
profile=$1
moduleName=$2

if [ -n "$(ls ${pidBaseDir}/${moduleName}-${profile}-*.pid >/dev/null 2>&1)" ]; then
  for pidFile in ${pidBaseDir}/${moduleName}-${profile}-*.pid; do
    pid=$(cat ${pidFile})

    if ps -p $pid >/dev/null; then
      echo "kill $moduleName pid $pid"
      kill -9 $pid
    fi

    rm -f ${pidFile}
  done
else
  pids=$(ps -ef | grep java | grep ${moduleName} | grep ${profile} | awk '{print $2}' | xargs)

  if [ -z $pids ]; then
    exit 0
  fi

  for pid in $pids; do
    if ps -p $pid >/dev/null; then
      echo "kill $moduleName pid $pid"
      kill -9 $pid
    fi
  done
fi
