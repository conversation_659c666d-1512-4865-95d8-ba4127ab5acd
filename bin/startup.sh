#!/bin/bash
SHELL_FOLDER=$(dirname $(readlink -f "$0"))
set -e
projectBaseDir=${SHELL_FOLDER}/..
stdoutBaseDir="/var/log/idas"
pidBaseDir="/tmp"

defaultVersion="1.0-SNAPSHOT"
profile=$1
moduleName=$2
mainClass=$3

moduleDir="${projectBaseDir}/${moduleName}/target"

dateStr=`date +%Y-%m-%d_%H.%M.%S`

moduleLib=${moduleDir}/lib
moduleJar=${moduleDir}/${moduleName}-${defaultVersion}.jar
moduleStdout=${stdoutBaseDir}/${moduleName}-${dateStr}.out

for f in ${moduleDir}/lib/*.jar; do
    echo "$CLASSPATH"|grep "$f" > /dev/null 2>&1 || CLASSPATH=$CLASSPATH:$f;
done

CLASSPATH=$CLASSPATH:${moduleJar}

nohup java -classpath "$CLASSPATH" -Dspring.profiles.active=$profile ${mainClass} > ${moduleStdout} 2>&1 &

pid=$!
pidFile=$pidBaseDir/$moduleName-$profile-$pid.pid
echo "service $moduleName pid $pid , pid file at $pidFile"
echo $pid > $pidFile
echo "stdout file : ${moduleStdout}"