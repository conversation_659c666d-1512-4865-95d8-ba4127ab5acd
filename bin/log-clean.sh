#!/bin/bash
SHELL_FOLDER=$(dirname $(readlink -f "$0"))
stdoutBaseDir="/var/log/idas/"

files=$(find "${stdoutBaseDir}" -mtime +30 -type f -name "*.out" | xargs)
if [[ -n "${files}" ]]; then
    for file in ${files}
    do
        echo "delete ${file}"
        rm -f ${file}
    done
fi

files=$(find "${SHELL_FOLDER}/../" -mtime +30 -type f -name "idas*.log*" | xargs)
if [[ -n "${files}" ]]; then
    for file in ${files}
    do
        echo "delete ${file}"
        rm -f ${file}
    done
fi