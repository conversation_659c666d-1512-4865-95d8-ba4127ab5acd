#!/bin/bash
for f in /data/src/gtg56/idas/idas-service-spark/target/lib/*.jar; do
    echo "CP"|grep "$f" > /dev/null 2>&1 || CP="$CP,local:$f";
done

sudo -u hdfs spark-shell --name SparkShellX --master yarn --deploy-mode client \
     --conf "spark.yarn.jars=local:/opt/cloudera/parcels/CDH/lib/spark/jars/*,local:/opt/cloudera/parcels/CDH/lib/spark/hive/*,file:/data/src/gtg56/idas/idas-service-spark/target/idas-service-spark-1.0-SNAPSHOT.jar,${CP}" \
     --jars "local:/opt/cloudera/parcels/CDH/lib/spark/jars/*,local:/opt/cloudera/parcels/CDH/lib/spark/hive/*,file:/data/src/gtg56/idas/idas-service-spark/target/idas-service-spark-1.0-SNAPSHOT.jar,${CP}"