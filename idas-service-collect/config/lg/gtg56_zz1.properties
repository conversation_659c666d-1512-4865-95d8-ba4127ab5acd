com.gtg56.idas.service.collect.collectorType=LGCollector

com.gtg56.idas.service.collect.active=true

# 仓库编码
com.gtg56.idas.service.collect.warehouseCode=GZZ002
com.gtg56.idas.service.collect.warehouseName=广交郑州仓

# jdbc
com.gtg56.idas.service.collect.jdbc.url=******************************************************
com.gtg56.idas.service.collect.jdbc.user=sa
com.gtg56.idas.service.collect.jdbc.password=sa123

#jdbc_interface
com.gtg56.idas.service.collect.jdbc.interfaceurl=*****************************************************************************************************************************************
com.gtg56.idas.service.collect.jdbc.interfaceuser=admin
com.gtg56.idas.service.collect.jdbc.interfacepassword=IdasRds#2021

# 常规采集周期
com.gtg56.idas.service.collect.cycle=5000

# 每多少个周期采集一次维度数据
com.gtg56.idas.service.collect.dim.round=100

# 是否增量同步
com.gtg56.idas.service.collect.incr=true

# 是第一次运行
com.gtg56.idas.service.collect.firstRun = true

# 错误处理时间周期，每天定时执行一次,单位：天
com.gtg56.idas.service.collect.overlimitRound =7