com.gtg56.idas.service.collect.collectorType=ZDBXCollector

com.gtg56.idas.service.collect.active=true

# 仓库编码
com.gtg56.idas.service.collect.warehouseCode=SSCYL0927
com.gtg56.idas.service.collect.warehouseName=广交三山仓
# jdbc
com.gtg56.idas.service.collect.jdbc.url=*************************************************
com.gtg56.idas.service.collect.jdbc.user=sa
com.gtg56.idas.service.collect.jdbc.password=zdbx
# 常规采集周期
com.gtg56.idas.service.collect.cycle=5000
com.gtg56.idas.service.collect.table=MonitorCacheRecord
# 是否增量同步
com.gtg56.idas.service.collect.incr=true
# 每多少个周期采集一次维度数据
com.gtg56.idas.service.collect.dim.round=100


