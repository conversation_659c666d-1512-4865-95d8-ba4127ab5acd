com.gtg56.idas.service.collect.collectorType=ZDBXIoTCollector
com.gtg56.idas.service.collect.active=true
# 仓库编码
com.gtg56.idas.service.collect.warehouseCode=BOX_CAR
com.gtg56.idas.service.collect.warehouseName=冷链设备
# jdbc
com.gtg56.idas.service.collect.host=https://iotservice.zd100com.cn
com.gtg56.idas.service.collect.user=gzjtjt
com.gtg56.idas.service.collect.password=1234560
# 常规采集周期
com.gtg56.idas.service.collect.cycle=60000
# 每多少个周期采集一次维度数据
com.gtg56.idas.service.collect.dim.round=2
# 每轮同步 T-7 to now
com.gtg56.idas.service.collect.lag_days=7
