# Collector Type
com.gtg56.idas.service.collect.collectorType=ZDV2Collector
com.gtg56.idas.service.collect.active=true

# 仓库编码
com.gtg56.idas.service.collect.warehouseCode=GZGJDJKCYY
com.gtg56.idas.service.collect.warehouseName=广州广交大健康产业园
com.gtg56.idas.service.collect.cycle=60000

# API Info
com.gtg56.idas.service.collect.api.url=http://10.8.20.25:3001
com.gtg56.idas.service.collect.api.username=sscdatadocking
com.gtg56.idas.service.collect.api.password=ssc123456&

# 异常记录回溯前几天的数据
com.gtg56.idas.service.collect.recycle=3

# Collect interval
com.gtg56.idas.service.collect.interval=60000