#!/bin/bash
SHELL_FOLDER=$(dirname $(readlink -f "$0"))
MODULE_DIR="${SHELL_FOLDER}/../"
TARGET_DIR="${MODULE_DIR}/target"
LIB_DIR="${TARGET_DIR}/lib"
stdoutBaseDir="/var/log/idas"
pidBaseDir="/tmp"
profile=$1

dateStr=`date +%Y-%m-%d_%H.%M.%S`
moduleStdout=${stdoutBaseDir}/idas-service-collect-${dateStr}.out

for f in ${LIB_DIR}/*.jar; do
    echo "$CLASSPATH"|grep "$f" > /dev/null 2>&1 || CLASSPATH=$CLASSPATH:$f;
done

CLASSPATH=$CLASSPATH:$TARGET_DIR/idas-service-collect-1.0-SNAPSHOT.jar

nohup java -classpath "$CLASSPATH" -Dspring.profiles.active=$profile \
 com.gtg56.idas.service.collect.Application "$@" > ${moduleStdout} 2>&1 &

pid=$!
pidFile=$pidBaseDir/idas-service-collect-$profile-$pid.pid
echo "service idas-service-collect pid $pid , pid file at $pidFile"
echo $pid > $pidFile
echo "stdout file : ${moduleStdout}"
