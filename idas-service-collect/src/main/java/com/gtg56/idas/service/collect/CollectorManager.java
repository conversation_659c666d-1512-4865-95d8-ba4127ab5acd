package com.gtg56.idas.service.collect;

import com.gtg56.idas.service.collect.worker.BaseCollector;
import lombok.SneakyThrows;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Properties;

public class CollectorManager {
    
    private static final String collectorPackage = "com.gtg56.idas.service.collect.worker";
    
    private Map<String,CollectorThread> collectors;
    
    public CollectorManager() {
        this.collectors = new LinkedHashMap<>();
    }
    
    @SuppressWarnings("unchecked")
    @SneakyThrows
    public BaseCollector startCollector(String configFilePath) {
        File file = new File(configFilePath);
        if(!file.exists() || file.isDirectory())
            throw new RuntimeException("collector config file " + configFilePath + " illegal");
    
        FileInputStream fis = new FileInputStream(file);
        Properties prop = new Properties();
        prop.load(new InputStreamReader(fis, StandardCharsets.UTF_8));
    
        String collectorType = prop.getProperty("com.gtg56.idas.service.collect.collectorType");
        String className = collectorPackage + "." + collectorType;
        Class<BaseCollector> collectorClass = (Class<BaseCollector>) Class.forName(className);
        Constructor<BaseCollector> constructor = collectorClass.getConstructor(Properties.class);
        
        BaseCollector baseCollector = constructor.newInstance(prop);
    
        CollectorThread collectorThread = new CollectorThread(baseCollector);
        String name = baseCollector.collectorName();
        
        collectors.put(name,collectorThread);
        collectorThread.start();
        
        return baseCollector;
    }
    
    public static class CollectorThread extends Thread {
        private BaseCollector collector;
        
        public CollectorThread(BaseCollector collector) {
            super(collector);
            this.collector = collector;
            this.setName(collector.collectorName());
        }
    }
}
