package com.gtg56.idas.service.collect;

import com.gtg56.idas.common.tool.compress.Compressor;
import com.gtg56.idas.common.tool.compress.CompressorFactory;
import com.gtg56.idas.common.tool.http.HttpDownloader;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;

import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
//                "com.gtg56.idas.common.service",
                "com.gtg56.idas.common.util",
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class,
                org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration.class
        })
//@DubboComponentScan
//@MapperScan("com.gtg56.idas.common.mapper")
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(value = {"classpath:/pubchemBeans.xml"})
public class PubchemDownload {
    

    public static String url = "https://ftp.ncbi.nlm.nih.gov/pubchem/Compound/CURRENT-Full/XML/";
    public static Map<String, String> md5Map = null;

    @SneakyThrows
    public static void main(String[] args) {
        String localPath = "";
        if (args.length == 0) {
            throw new RuntimeException("usage: 必须输入下载后的文件目录！");

        }
        localPath = args[0];


        ConfigurableApplicationContext context = SpringApplication.run(PubchemDownload.class, args);
        Configuration hdfsConf = context.getBean("hdfsConf", Configuration.class);

        //1. 获取网络上md5文件的内容K:文件名，V:文件内容
        md5Map = getMd5Map();

        File file = null;

        for (Map.Entry<String, String> entry : md5Map.entrySet()) {

            String urlFile = url + entry.getKey();
            String LocalFile = localPath + entry.getKey()+".md5";

            file = new File(LocalFile);

            //1.判断本地是否存在md5文件，不存在直接下载，存在的话比较md5文件内容
            //2.如果文件存在则比较本地的md5文件内容是否一致
            //3.md5文件内容一致表示文件没有改变不用下载
            if (file.exists()) {
                //比较md5内容
                if (!compareMd5File(file, entry)) {
                    //下载文件
                    //MD5文件直接从Map中读取写入本地
                      StreamUtil.storeToLocal(file,(entry.getValue()+"  "+entry.getKey()).getBytes());
                      downLoadFile(urlFile, LocalFile.replace(".md5",""),10);
                      put2Hdsf(LocalFile.replace(".md5",""), entry.getKey().replace(".gz",""), true,hdfsConf);

                }


            } else {

                //下载文件
                //MD5文件直接从Map中读取写入本地
                file.createNewFile();
                StreamUtil.storeToLocal(file,(entry.getValue()+"  "+entry.getKey()).getBytes());
                downLoadFile(urlFile, LocalFile.replace(".md5",""),10);
                put2Hdsf(LocalFile.replace(".md5",""), entry.getKey().replace(".gz",""), true,hdfsConf);


            }

            log.info("{}",entry.getKey() + ":" + entry.getValue());
        }


    }

    /**
     * 下载文件
     *
     * @param urlFile   网络文件URL地址
     * @param localFile 下载后的文件地址
     * @throws IOException
     */
    private static void downLoadFile(String urlFile, String localFile, int threadNum) throws IOException {

        log.info("正在下载文件{}", localFile);

        new HttpDownloader(urlFile, localFile, threadNum, 5000).get();

        log.info("结束下载文件 {}", localFile);
    }

    /**
     * 比较两个md5文件内容
     *
     * @param file
     * @param entry
     * @return
     * @throws IOException
     */
    private static boolean compareMd5File(File file, Map.Entry<String, String> entry) throws IOException {

//        BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file)));
//        String s = reader.readLine();
        byte[] bytes = StreamUtil.fileData(file);
        String s = new String(bytes);
        String[] split = s.split("  ");
        String md5Net = md5Map.get(entry.getKey());
        String md5Local = split[0];

        return StringUtils.equals(md5Net, md5Local);
    }

    /*
     *1. 每次程序启动从网页抓取要下载的md5文件，放入map中
     *2. 循环map比较文件新下载的md5文件和已下载的md5文件是否一样
     */
    private static Map<String, String> getMd5Map() throws IOException {
        Document document = Jsoup.connect(url).get();
        Elements elements = document.select("a[href]");


        //抓取网页上所有的文件名
        List<String> files = elements.eachAttr("href");

        //过滤出md5文件名
        List<String> md5files = files.stream().filter(recorde -> recorde.endsWith(".md5")).collect(Collectors.toList());


        //读取网络md5文件和本地md5文件做比较
        md5Map = new HashMap<>();
        URL md5url = null;
        BufferedReader reader = null;
        for (String file : md5files) {
            md5url = new URL(url + file);
            InputStream inputStream = md5url.openStream();
            byte[] bytes = StreamUtil.fromStream(inputStream);
            String s = new String(bytes);
//            reader = new BufferedReader(new InputStreamReader(md5url.openStream()));
//            String s = reader.readLine();
            //7922c6ae73d695719572af8aec5ca309  Compound_127000001_127500000.xml.gz
            String[] split = s.split("  ");
            md5Map.put(split[1].replace("\n",""), split[0]);
        }

        if (reader != null) {
            reader.close();
        }


        return md5Map;
    }

    /**
     * 上传到HDSF,解压同时上传文件到HDFS
     *
     * @param src
     */
    private static void put2Hdsf(String src, String fileName, Boolean isDeCommpress2Hdfs,Configuration conf) {

        try {
//
//            URI uri = new URI("hdfs://*********:8020");
//            Configuration configuration = new Configuration();

            FileSystem fs = FileSystem.get(conf);


            //解压缩，解压后的文件直接指向分布式文件系统
            Compressor gZipCompressor = CompressorFactory.forName("gzip");
            FileInputStream fsin = new FileInputStream(src);
            FSDataOutputStream fsDout = null;
            FileOutputStream fsout = null;
            //是否直接解压到HDFS
            if (isDeCommpress2Hdfs && !fileName.endsWith(".md5")) {
                fsDout = fs.create(new Path("/mydata/" + fileName));
                gZipCompressor.decompress(fsin, fsDout);

            } else if (!isDeCommpress2Hdfs && !fileName.endsWith(".md5")) //先解压到本地再上传
            {
                String decompressFile = src.replace(".gz", "");
                fsout = new FileOutputStream(decompressFile);
                gZipCompressor.decompress(fsin, fsout);
                Path srcPath = new Path(decompressFile);
                Path tarPath = new Path("/mydata/" + fileName);
                fs.copyFromLocalFile(srcPath, tarPath);
            } else //MD5文件直接上传
            {

                Path srcPath = new Path(src);
                Path tarPath = new Path("/mydata/" + fileName);
                fs.copyFromLocalFile(srcPath, tarPath);
            }


            //关闭文件流
            if (fsin != null) {
                fsin.close();
            }

            if (fsout != null) {
                fsout.close();
            }

            if (fsDout != null) {
                fsDout.close();
            }

            if (fs != null) {
                fs.close();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }


    }


}
