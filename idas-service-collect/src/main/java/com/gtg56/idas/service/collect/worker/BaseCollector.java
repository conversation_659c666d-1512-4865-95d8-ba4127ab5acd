package com.gtg56.idas.service.collect.worker;

import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.entity.dto.CollectorStatusDTO;
import com.gtg56.idas.common.util.NetworkUtil;
import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.service.collect.util.ZkReportUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.LeaderSelector;
import org.apache.curator.framework.recipes.leader.LeaderSelectorListener;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;

import java.lang.management.ManagementFactory;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public abstract class BaseCollector implements Runnable {
    
    private AtomicLong cycleAcc = new AtomicLong(0L);
    private AtomicLong totalCollect = new AtomicLong(0L);
    private AtomicLong totalProduce = new AtomicLong(0L);
    
    private Properties properties;
    
    public BaseCollector(Properties properties) {
        this.properties = properties;
    }
    
    private Date upTime;
    
    @Override
    public void run() {
        upTime = new Date();
        initZk();
        if (requireElection()) {
            log.info("##### start election #####");
            election();
        } else {
            leader = true;
            collect();
        }
    }

    private void collect() {
        while (leader) {
            log.info("collector {} start new cycle , current cycle {}",
                    collectorName(), cycleAcc.incrementAndGet());

            long startTime = System.currentTimeMillis();
            try {
                CollectResult collectResult = collectAction();
                incrCollect(collectResult.getCollect());
                incrProduce(collectResult.getProduce());
            } catch (Exception e) {
                log.warn("collector {} cycle({}) catch exception \n", collectorName(), currentCycle(), e);
            }
            try {
                updateZk();
            } catch (Exception e) {
                log.warn("collector {} cycle({}) update zk failed \n{}", collectorName(), currentCycle(), e);
            }
            long cost = System.currentTimeMillis() - startTime;

            log.info("collector {} cycle({}) done, cost {}ms",
                    collectorName(), currentCycle(), cost);

            if (cost < cycling()) {
                long sleep = cycling() - cost;
                log.info("collector {} cycle({}) sleep {}ms for next cycle",
                        collectorName(), currentCycle(), sleep);

                try {
                    Thread.sleep(sleep);
                } catch (InterruptedException e) {
                    log.warn("collector " + collectorName() +
                            " cycle(" + currentCycle() + ") start sleep fail", e);
                }
            } else {
                log.warn("collector {} cycle({}) cost over cycling() , no sleep",
                        collectorName(), currentCycle());
            }
        }
        log.info("collector {} exit, total cycle {}", collectorName(), currentCycle());
    }

    protected abstract boolean requireElection();

    public String collectorName() {
        return this.getClass().getSimpleName();
    }

    public Long currentCycle() {
        return cycleAcc.get();
    }
    
    public boolean active() {
        return Boolean.parseBoolean(
                activeProperties().getProperty("com.gtg56.idas.service.collect.active","false")
        );
    }
    
    protected abstract CollectResult collectAction();
    
    protected long cycling() {
        return Long.parseLong(
                activeProperties().getProperty("com.gtg56.idas.service.collect.cycle","1000")
        );
    }
    
    public final Properties activeProperties() {
        return properties;
    }
    
    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class CollectResult {
        private Long collect;
        private Long produce;
        
        public CollectResult incrCollect(Long amount) {
            return this.setCollect(this.getCollect() + amount);
        }
    
        public CollectResult incrProduce(Long amount) {
            return this.setProduce(this.getProduce() + amount);
        }
        
        public CollectResult incr(CollectResult other) {
            return this.incrCollect(other.getCollect())
                    .incrProduce(other.getProduce());
        }
    }
    
    private AtomicLong currCollect = new AtomicLong(0);
    private AtomicLong currProduce = new AtomicLong(0);
    
    private Long incrCollect(long amount) {
        currCollect.set(amount);
        return totalCollect.addAndGet(amount);
    }
    
    private Long incrProduce(long amount) {
        currProduce.set(amount);
        return totalProduce.addAndGet(amount);
    }
    
    private String collectorZKPath;
    
    private void initZk() {
        CuratorFramework zkClient = SpringUtil.getBean("zkClient", CuratorFramework.class);
        String collectorName = collectorName();
        String host = NetworkUtil.getLocalIpv4();
        
        collectorZKPath = CommonConsts.ZK_PATH_COLLECTOR_REPORT + "/" + collectorName + "@" + host;
        
        String name = ManagementFactory.getRuntimeMXBean().getName();
        String s = name.split("@")[0];
        String prof = System.getProperty("spring.profiles.active");
    
        CollectorStatusDTO dto = new CollectorStatusDTO();
        dto.setCollectorClass(this.getClass().getName())
                .setCollectorName(collectorName)
                .setCycle(currentCycle())
                .setPid(Integer.parseInt(s))
                .setSpringActiveProfiles(prof)
                .setUpTime(upTime)
                .setHost(host)
                .setTotalCollect(totalCollect.get())
                .setTotalProduce(totalProduce.get())
                .setCurrCollect(currCollect.get())
                .setCurrProduce(currProduce.get());
        
        ZkReportUtil.set(zkClient,collectorZKPath,dto);
    }
    
    private void updateZk() {
        updateZk(0);
    }
    
    private void updateZk(int retry) {
        CuratorFramework zkClient = SpringUtil.getBean("zkClient", CuratorFramework.class);
    
        CollectorStatusDTO dto = ZkReportUtil.get(zkClient,collectorZKPath);
        if(dto == null) {
            initZk();
            if(retry < 5) {
                updateZk(retry + 1);
            }
        } else {
            dto.setCycle(currentCycle())
                    .setTotalCollect(totalCollect.get())
                    .setTotalProduce(totalProduce.get())
                    .setCurrCollect(currCollect.get())
                    .setCurrProduce(currProduce.get())
                    .setLeader(leader);
        
            ZkReportUtil.set(zkClient, collectorZKPath, dto);
        }
    }
    
    private boolean leader = false;
    
    @SneakyThrows
    private void election() {
        CuratorFramework zkClient = SpringUtil.getBean("zkClient", CuratorFramework.class);
        String electionPath = CommonConsts.ZK_PATH_COLLECTOR_ELECTION + "/" + collectorName();
        Stat stat = zkClient.checkExists().forPath(electionPath);
        if (stat == null) {
            zkClient.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT).forPath(electionPath);
        }
        log.info("election path on {}", electionPath);

        LeaderSelector leaderSelector = new LeaderSelector(zkClient, electionPath, new LeaderSelectorListener() {

            @Override
            public void stateChanged(CuratorFramework client, ConnectionState stat) {
                log.info("{}", stat);
            }

            @Override
            public void takeLeadership(CuratorFramework client) throws Exception {
                log.info("********** now on lead **********");
                leader = true;
                collect();
            }
        });
        leaderSelector.autoRequeue();
        leaderSelector.start();
    }

}
