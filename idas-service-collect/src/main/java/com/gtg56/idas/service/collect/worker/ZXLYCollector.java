package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.sensor.lg.LGDimVo;
import com.gtg56.idas.common.entity.sensor.zxly.ZXLYDimVo;
import com.gtg56.idas.common.entity.sensor.zxly.ZXLYOverLimitHandling;
import com.gtg56.idas.common.entity.sensor.zxly.ZXLYRealTempHum;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ZXLYCollector extends BaseCollector {

    private final WarehouseSensorClient openTSDBClient;//时序数据库客户端
    private final IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;//增量同步数据处理
    private final IWarehouseSensorService warehouseSensorService;//传感器维度配置
    private final MQProducer sensorRecordProducer;  //实时数据处理kafka
    private final MQProducer overLimitHandlingProducer;//异常处理数据kafka

    private final String jdbcUrl;
    private final String jdbcUser;
    private final String jdbcPassword;
    private final String strWarehouseCode;//仓库编号
    private final String strWarehouseName;//仓库名字
    private final Integer intRound;//多少个采集周期采集一次维度
    private final Boolean blincr;//是否增量同步

    private Map<String, LGDimVo> dimMap;
    private Integer overLimitRound;
    private Date dtoverLimitRount_last = new Date();
    private Date dtoverLimitRount_Now;
    private Date ovelimitS_Date;


    public ZXLYCollector(Properties properties) throws ParseException {
        super(properties);

        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);

        jdbcUrl = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.url");
        jdbcUser = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.user");
        jdbcPassword = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.password");

        strWarehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        strWarehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");

        //  intCycle = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.cycle"));
        intRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));

        blincr = Boolean.parseBoolean(activeProperties().getProperty("com.gtg56.idas.service.collect.incr"));
        overLimitRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.overlimitRound"));

    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + strWarehouseCode;
    }

    /*维度信息*/
    private static final String Dim_SQL =
            "SELECT ID, WZName, IPAddr, KFSX, WDBJSX, WDBJXX, WDYJSX, WDYJXX, SDBJSX, SDBJXX, SDYJSX, SDYJXX, AreaCode, DeviceCode\n" +
                    "\t FROM cdpz  ";
   /*实时数据*/
    private static final String rel_SQL =" select\n" +
            "\trel.id,\n" +
            "\trel.AreaCode,\n" +
            "\trel.wzname,\n" +
            "\trel.DeviceCode,\n" +
            "\trel.ipaddr,\n" +
            "\trel.wdz,\n" +
            "\trel.sdz,\n" +
            "\trel.dt,\n" +
            "\trel.remark,\n" +
            "\trel.kfsx,\n" +
            "\trel.gpsjd,\n" +
            "\trel.gpswd,\n" +
            "\tdim.WDBJSX ,\n" +
            "\tdim.WDBJXX ,\n" +
            "\tdim.SDBJSX ,\n" +
            "\tdim.SDBJXX \n" +
            "from\n" +
            "\treal_temphum rel ,\n" +
            "\tcdpz dim\n" +
            "where\n" +
            "\trel.AreaCode = dim.AreaCode\n" +
            "\tand rel.DeviceCode = dim.DeviceCode\n" +
            "\tand rel.id > ?\n" +
            "limit 100";
   /*错误处理数据*/
    private static final String overLimit_SQL = "select\n" +
           "\tcx.id,\n" +
           "\tcx.WZName,\n" +
           "\tcx.IPAddr,\n" +
           "\tcx.WDZ,\n" +
           "\tcx.SDZ,\n" +
           "\tcx.DT,\n" +
           "\tcx.KFSX,\n" +
           "\tcx.CDoEvent,\n" +
           "\tcx.CReason,\n" +
           "\tcx.COperator,\n" +
           "\tcx.AreaCode,\n" +
           "\tcx.DeviceCode,\n" +
           "\tcx.Remark,\n" +
           "\tdim.WDBJSX ,\n" +
           "\tdim.WDBJXX ,\n" +
           "\tdim.SDBJSX ,\n" +
           "\tdim.SDBJXX\n" +
           "from\n" +
           "\tcxrecord_temphum cx ,\n" +
           "\tcdpz dim\n" +
           "where cx.AreaCode = dim.AreaCode\n" +
           "\tand cx.DeviceCode = dim.DeviceCode \n" +
           "    and cx.id >? \n" +
           "    limit  100 ";

    @Override
    protected CollectResult collectAction() {

        CollectResult ret = new CollectResult(0L, 0L);
        try (Connection conn = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)) {


                //采集维度数据；

                long currentcount = currentCycle();

                //第一次同步时先采集维度信息--不再从中间库获取维度信息
                 if (currentcount  == 1 ) {
                    //CollectResult dimResult = syncDim(conn);
                    //ret.incr(dimResult);
                }

                //采集实时数据；
                CollectResult recordResult = syncSensorRecord(conn);
                ret.incr(recordResult);

/*记录错误处理的数据
                 * #1.每天记录一次
                 * */

                ovelimitS_Date = new Date();//初始化 SQL的日期
                dtoverLimitRount_Now = new Date();//初始化当前日期
            DateFormat sdf = DateUtil.ymd();


/* 格式化日期，去除时分秒 */

                String stroverLimitHandlingNow = sdf.format(dtoverLimitRount_Now);
                String stroverLimitHandlingLast = sdf.format(dtoverLimitRount_last);
                String stroverLimitS_Date = sdf.format(ovelimitS_Date);

/*格式话后的日期重新赋值*/

                dtoverLimitRount_Now = sdf.parse(stroverLimitHandlingNow);
                dtoverLimitRount_last = sdf.parse(stroverLimitHandlingLast);
                ovelimitS_Date = sdf.parse(stroverLimitS_Date);

/*判断最后执行记录错误记录的时间是否与当前时间为同一天，（每天只记录一次）*/

                if (dtoverLimitRount_Now.after(dtoverLimitRount_last)){
                    ovelimitS_Date.setTime(ovelimitS_Date.getTime() - Duration.ofDays(overLimitRound).toMillis());

                    CollectResult croverLimitHandling = syncOverlimitHandling(conn);
                    ret.incr(croverLimitHandling);
/*记录最后的执行时间*/

                    dtoverLimitRount_last.setTime(dtoverLimitRount_Now.getTime());
                }

            }catch (SQLException e) {

                log.warn(e.getMessage() + "  失败！！！", e);
            }
            catch (ParseException e) {
            e.printStackTrace();
        }
        return ret;
    }

    //维度数据采集；
    private CollectResult syncDim(Connection conn) throws SQLException {

        PreparedStatement psDim = conn.prepareStatement(Dim_SQL);
        ResultSet rs = psDim.executeQuery();
        List<ZXLYDimVo>  listdim = JDBCUtil.resultSetToBeanList(rs, ZXLYDimVo.class);
        long l_count = listdim.parallelStream()
                .map(mpDim -> {

                    WarehouseSensor ws = new WarehouseSensor();
                    ws.setCorpCode(SensorConsts.CORP_ZXLY);//设备厂商编号
                    ws.setWarehouseCode(strWarehouseCode);
                    ws.setWarehouseName(strWarehouseName);
                    ws.setRegionCode(mpDim.getAreaCode().replaceAll("\\s", ""));//区域编号
                    ws.setRegionName(mpDim.getWzName().replaceAll("\\s", ""));//区域名称
                    ws.setSensorCode(strWarehouseCode + '-' + mpDim.getDeviceCode().replaceAll("\\s", ""));//设备编号
                    ws.setSensorCodeOrigin(mpDim.getDeviceCode().replaceAll("\\s", ""));//原始设备编码
                    ws.setSensorName(mpDim.getIpAddr().replaceAll("\\s", ""));//设备名称

                    ws.setSensorType(" ");//设备类型【暂无】
                    ws.setSensorFunction(SensorConsts.FUNC_SENSOR);

                    ws.setTemperatureHighLimit(BigDecimal.valueOf(mpDim.getWdbjsx()));//温度上限
                    ws.setTemperatureLowLimit(BigDecimal.valueOf(mpDim.getWdbjxx()));//温度下限
                    ws.setHumidityHighLimit(BigDecimal.valueOf(mpDim.getSdbjsx()));//湿度上限
                    ws.setHumidityLowLimit(BigDecimal.valueOf(mpDim.getWdbjxx()));//湿度下限
                    return ws;
                })
                .filter(ws -> warehouseSensorService.createOrUpdate(ws))
                .count();

        psDim.close();

        return new CollectResult((long) listdim.size(), l_count);


    }

    /*采集实时数据*/
    private CollectResult syncSensorRecord(Connection conn) throws SQLException {
        log.info("-------------- 开始实时数据采集 --------------");

        String strSQL = rel_SQL;
        PreparedStatement ps = conn.prepareStatement(strSQL);

        //在表rdb_incr_sync_offset 中记录已经被抽取的最后一次的数据ID,抽取的数据源来至 JdbcUrl,表来自Real_temphun
        RdbIncrSyncOffset rdbIncrSyncOffset = rdbIncrSyncOffsetService.get(jdbcUrl, "real_temphum");  // 关系型数据库数据

        Long LastID = rdbIncrSyncOffset.getLastId();
        ps.setLong(1, LastID);//传SQL参数；

        ResultSet rs = ps.executeQuery();//获取数据结果集

        List<ZXLYRealTempHum> realList =JDBCUtil.resultSetToBeanList(rs, ZXLYRealTempHum.class);
        List<WarehouseSensorRecord> produce = realList.parallelStream()
                .peek(record -> record.setDeviceCode(strWarehouseCode + "-" + record.getDeviceCode()))
                .flatMap(record -> {
                    BigDecimal temperature =BigDecimal.valueOf(record.getWdz());
                    BigDecimal humidity = BigDecimal.valueOf(record.getSdz());

                    String temStatus = SensorConsts.toStatus(temperature, BigDecimal.valueOf(record.getWdbjsx()), BigDecimal.valueOf(record.getWdbjxx()));
                    String humStatus = SensorConsts.toStatus(humidity, BigDecimal.valueOf(record.getSdbjsx()), BigDecimal.valueOf(record.getSdbjxx()));
                    long timestamp = record.getDt().getTime() / 1000;
                    WarehouseSensorRecord tem = new WarehouseSensorRecord();
                    tem.setTimestamp(timestamp)
                            .setValue(temperature)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(strWarehouseCode)
                                    .setRegionCode(record.getAreaCode().replaceAll("\\s", ""))
                                    .setSensorCode(record.getDeviceCode().replaceAll("\\s", ""))
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(temStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));

                    WarehouseSensorRecord hum = new WarehouseSensorRecord();
                    hum.setTimestamp(timestamp)
                            .setValue(humidity)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(strWarehouseCode)
                                    .setRegionCode(record.getAreaCode().replaceAll("\\s", ""))
                                    .setSensorCode(record.getDeviceCode().replaceAll("\\s", ""))
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(humStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));

                    log.info("Record Tem Hum Info: recordID:{}, tem:{}, hum:{}",record.getId(), tem.toPutBody(),hum.toPutBody());
                    return Stream.of(tem, hum);
                })
                .filter(record -> record.getTags() != null)
                .collect(Collectors.toList());

        produce
                .forEach(record -> {
                    log.info("Record Info:{}",record.toPutBody());
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(strWarehouseCode,record.toPutBody());
                });

        if(blincr && rdbIncrSyncOffset != null) {
            if (!realList.isEmpty()) {
                realList.parallelStream()
                        .mapToLong(ZXLYRealTempHum::getId)
                        .max()
                        .ifPresent(currMaxId -> rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(currMaxId)));

            } else { // 数据库自增id回滚至0
                String minMaxId = JDBCUtil.getTableMinMaxId(jdbcUrl , jdbcUser, jdbcPassword, "real_temphum", "Id");
                String[] split = minMaxId.split(",");
                if (split.length == 2) {
                    long dbMax = Long.parseLong(split[1]);
                    if (rdbIncrSyncOffset.getLastId() > dbMax) {
                        rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(0L));
                    }
                }
            }
        }
        ps.close();

        log.info("-------------- 结束实时数据采集 --------------");
        return new CollectResult((long)realList.size(),(long)produce.size());
    }

    //错误日志记录
    private CollectResult syncOverlimitHandling(Connection conn) throws SQLException {

        String strSQL = overLimit_SQL;
        PreparedStatement ps = conn.prepareStatement(strSQL);

        //在表rdb_incr_sync_offset 中记录已经被抽取的最后一次的数据ID,抽取的数据源来至 JdbcUrl,表来自Real_temphun
        RdbIncrSyncOffset rdbIncrSyncOffset = rdbIncrSyncOffsetService.get(jdbcUrl, "cxrecord_temphum");  // 关系型数据库数据

        Long LastID = rdbIncrSyncOffset.getLastId();
        ps.setLong(1, LastID);//传SQL参数；

        ResultSet rs = ps.executeQuery();//获取数据结果集

        List<ZXLYOverLimitHandling> zxlyOverLimitHandlingList = JDBCUtil.resultSetToBeanList(rs, ZXLYOverLimitHandling.class);
        zxlyOverLimitHandlingList.parallelStream()
                .forEach(overlimithandling -> {

                    OverLimitHandlingEvent overLimitHandlingEvent = new OverLimitHandlingEvent();
                    overLimitHandlingEvent.setCorpCode(SensorConsts.CORP_ZXLY);//设备厂家编码
                    overLimitHandlingEvent.setWarehouseCode(strWarehouseCode);//仓库编码
                    overLimitHandlingEvent.setRegionCode(overlimithandling.getAreaCode());//区域编码
                    overLimitHandlingEvent.setSensorCode(strWarehouseCode+ "-"+ overlimithandling.getDeviceCode());//传感器编码
                    overLimitHandlingEvent.setTemperatureHighLimit(BigDecimal.valueOf(overlimithandling.getWdbjsx()));//温度上限
                    overLimitHandlingEvent.setTemperatureLowLimit(BigDecimal.valueOf(overlimithandling.getWdbjxx()));//温度下限
                    overLimitHandlingEvent.setHumidityHighLimit(BigDecimal.valueOf(overlimithandling.getSdbjsx()));//湿度上限
                    overLimitHandlingEvent.setHumidityLowLimit(BigDecimal.valueOf(overlimithandling.getSdbjxx()));//湿度下限
                    overLimitHandlingEvent.setStartTime(overlimithandling.getDt());//开始时间
                    overLimitHandlingEvent.setHandleTime(overlimithandling.getDt());
                    overLimitHandlingEvent.setRange(false);//无是否时间范围，读取开始时间
                    overLimitHandlingEvent.setPrincipal(overlimithandling.getCOperator());//处理人
                    overLimitHandlingEvent.setSuggestion(overlimithandling.getCDoEvent());//处理意见

                   // log.info("{}",JSON.toJSONString(overLimitHandlingEvent));
                    overLimitHandlingProducer.produce(strWarehouseCode, JSON.toJSONString(overLimitHandlingEvent));
                });
        if(rdbIncrSyncOffset != null) {
            if (!zxlyOverLimitHandlingList.isEmpty()) {
                zxlyOverLimitHandlingList.parallelStream()
                        .mapToLong(ZXLYOverLimitHandling::getId)
                        .max()
                        .ifPresent(currMaxId -> rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(currMaxId)));

            } else { // 数据库自增id回滚至0
                String minMaxId = JDBCUtil.getTableMinMaxId(jdbcUrl , jdbcUser, jdbcPassword, "cxrecord_temphum", "Id");
                String[] split = minMaxId.split(",");
                if (split.length == 2) {
                    long dbMax = Long.parseLong(split[1]);
                    if (rdbIncrSyncOffset.getLastId() > dbMax) {
                        rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(0L));
                    }
                }
            }
        }
        ps.close();

        return new CollectResult((long) zxlyOverLimitHandlingList.size(), (long) zxlyOverLimitHandlingList.size());


    }


}