package com.gtg56.idas.service.collect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gargoylesoftware.htmlunit.HttpMethod;
import com.gargoylesoftware.htmlunit.Page;
import com.gargoylesoftware.htmlunit.WebClient;
import com.gargoylesoftware.htmlunit.WebRequest;
import com.gargoylesoftware.htmlunit.html.*;
import com.gargoylesoftware.htmlunit.util.UrlUtils;
import com.gtg56.idas.common.util.HtmlUnitUtil;
import com.gtg56.idas.common.util.RegexUtil;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.PropertySource;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
//                "com.gtg56.idas.common.service",
//                "com.gtg56.idas.common.util",
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class,
                org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration.class
        })
//@DubboComponentScan
//@MapperScan("com.gtg56.idas.common.mapper")
@PropertySource(value = {"classpath:/application.properties"})
//@ImportResource(value = {"classpath:/collectServiceBeans.xml"})
public class JianKeCollect {

    private static List<String> categories = Arrays.asList(
            "感冒发热",
            "头痛牙痛",
            "肌肉关节",
            "风湿",
            "癌症疼痛",
            "创伤疼痛",
            "其他用药",
            "解暑化湿",
            "痔疮用药",
            "肠道菌群紊乱",
            "胃炎用药",
            "腹痛腹泻",
            "消化不良",
            "泛酸打嗝",
            "驱虫类药",
            "便秘",
            "胃肠道感染",
            "胃痛胀气",
            "恶心呕吐",
            "其他用药",
            "烧烫创伤",
            "皮肤过敏",
            "扁平苔癣",
            "皮肤瘙痒",
            "手足癣病",
            "带状疱疹",
            "蚊虫叮咬",
            "皮炎湿疹",
            "银屑病",
            "痤疮",
            "疥疮",
            "白癜风",
            "麻风病",
            "尖锐湿疣",
            "皮肤感染",
            "祛斑类",
            "头皮糠疹",
            "其他用药",
            "跌打损伤内服",
            "跌打损伤外用",
            "风湿骨痛内服",
            "风湿骨痛外用",
            "晕车晕船",
            "神经衰弱",
            "帕金森类",
            "精神分裂",
            "老年痴呆",
            "眩晕失眠",
            "焦虑症",
            "抑郁症类",
            "肌无力类",
            "面瘫癫痫",
            "其他用药",
            "偏头疼类",
            "记忆减退",
            "高血压类",
            "高血脂类",
            "中风偏瘫",
            "冠心病",
            "低血压",
            "心肌病",
            "肺动脉高压",
            "心律不齐",
            "外周血管疾病",
            "动脉硬化",
            "心力衰竭",
            "其他用药",
            "中药降糖",
            "西药降糖",
            "营养调节",
            "预防病变",
            "保健养生",
            "改善补益",
            "补充钙类",
            "改善记忆",
            "改善睡眠",
            "改善贫血",
            "改善视力",
            "骨质疏松",
            "缓解疲劳",
            "保肝降脂",
            "降糖降压",
            "清咽润喉",
            "减肥瘦身",
            "润肠通便",
            "美容养颜",
            "维生素类",
            "提高免疫",
            "生长发育",
            "参茸饮片",
            "戒烟解酒",
            "补肾壮阳",
            "男士营养",
            "女性营养",
            "调节内分泌",
            "孕妇营养");

    @SneakyThrows
    public static void main(String[] args) {

        Workbook wb = WorkbookFactory.create(true);
        Sheet sheet = wb.createSheet("jianke");
        int l = 0;
        Row title = sheet.createRow(l++);
        int i = 0;
        title.createCell(i++, CellType.STRING).setCellValue("一级");
        title.createCell(i++, CellType.STRING).setCellValue("二级");
        title.createCell(i++, CellType.STRING).setCellValue("商品主标题");
        title.createCell(i++, CellType.STRING).setCellValue("商品副标题");
        title.createCell(i++, CellType.STRING).setCellValue("通用名称");
        title.createCell(i++, CellType.STRING).setCellValue("批注文号");
        title.createCell(i++, CellType.STRING).setCellValue("产品规格");
        title.createCell(i++, CellType.STRING).setCellValue("价格");
        title.createCell(i++, CellType.STRING).setCellValue("生产厂家");
        title.createCell(i++, CellType.STRING).setCellValue("产品品名");
        title.createCell(i++, CellType.STRING).setCellValue("主要原料");
        title.createCell(i++, CellType.STRING).setCellValue("主要作用");
        title.createCell(i++, CellType.STRING).setCellValue("用法用量");
        title.createCell(i++, CellType.STRING).setCellValue("生产企业");
        title.createCell(i++, CellType.STRING).setCellValue("说明书");
        title.createCell(i++, CellType.STRING).setCellValue("左侧小图");
        title.createCell(i++, CellType.STRING).setCellValue("介绍大图");
        title.createCell(i++, CellType.STRING).setCellValue("源链接");

        FileReader fr = new FileReader("d:\\1.json");
        BufferedReader br = new BufferedReader(fr);
        String line;
        while ((line = br.readLine()) != null) {
            Product product = JSON.parseObject(line, Product.class);
            Row row = sheet.createRow(l);
            i = 0;
            row.createCell(i++, CellType.STRING).setCellValue(product.getFirstCategory());
            row.createCell(i++, CellType.STRING).setCellValue(product.getSecondCategory());
            row.createCell(i++, CellType.STRING).setCellValue(product.getTitle());
            row.createCell(i++, CellType.STRING).setCellValue(product.getViceTitle());
            row.createCell(i++, CellType.STRING).setCellValue(product.getCommonName());
            row.createCell(i++, CellType.STRING).setCellValue(product.getRegisterNo());
            row.createCell(i++, CellType.STRING).setCellValue(product.getSpecifications());
            row.createCell(i++, CellType.STRING).setCellValue(product.getPrice());
            row.createCell(i++, CellType.STRING).setCellValue(product.getFactory());
            row.createCell(i++, CellType.STRING).setCellValue(product.getProductName());
            row.createCell(i++, CellType.STRING).setCellValue(product.getMainMaterials());
            row.createCell(i++, CellType.STRING).setCellValue(product.getMainEffect());
            row.createCell(i++, CellType.STRING).setCellValue(product.getUsage());
            row.createCell(i++, CellType.STRING).setCellValue(product.getProductFactory());
            row.createCell(i++, CellType.STRING).setCellValue(product.getInstructions());
            row.createCell(i++, CellType.STRING).setCellValue(StringUtils.join(product.getMainPics(), "\n"));
            row.createCell(i++, CellType.STRING).setCellValue(StringUtils.join(product.getDetailPics(), "\n"));
            row.createCell(i++, CellType.STRING).setCellValue(product.getUrl());
            l++;
        }
        wb.write(new FileOutputStream("d:\\jianke.xlsx"));
        wb.close();
    }

    @SneakyThrows
    public static void a111(String[] args) {

        ConfigurableApplicationContext context = SpringApplication.run(JianKeCollect.class, args);
        String categoriesUrl = "https://www.jianke.com/help/sitemap.html";
        String categoryUrlRegex = "www.jianke.com/list-[0-9]{6}-.+\\.html";
        String categoryATagRegex = "<a href=\"//" + categoryUrlRegex + "\">\\s+.+\\s+</a>";
        int maxPreCategory = 20;
        String productUrlRegex = "www.jianke.com/product/[0-9]+\\.html";

        WebClient wc = HtmlUnitUtil.newWebClient(6000, 1000);

        HtmlPage categoryPage = wc.getPage(categoriesUrl);
        HtmlDivision mapWrapDiv = categoryPage.getFirstByXPath("/html/body/div[4]/div[2]");
        String[] aTags = RegexUtil.getSubStrings(mapWrapDiv.asXml(), categoryATagRegex);

        Map<String, String> categoryCodes = new HashMap<>();

        for (String aTag : aTags) {
            String name = aTag.replaceAll("<.+>", "").replaceAll("\\s", "");
            if (categories.contains(name)) {
                String url = RegexUtil.getFirstMatch(aTag, categoryUrlRegex);
                String[] split = url.split("-");
                if (split.length > 3) {
                    String categoryCode = split[1];
                    categoryCodes.put(name, categoryCode);
                }
            }
        }

        String categoryListUrlPattern = "https://www.jianke.com/list-${c}-0-${p}-0-1-0-0-0-0-0.html";

        Set<String> fetchedUrls = new HashSet<>();
        FileOutputStream fos = new FileOutputStream("d:\\1.json");
        for (String name : categoryCodes.keySet()) {
            String code = categoryCodes.get(name);
            int numProd = 0;
            for (int page = 1; ; page++) {
                String pageUrl = categoryListUrlPattern.replace("${c}", code).replace("${p}", page + "");

                log.info("fetch {} at {}", name, pageUrl);
                HtmlPage listPage = wc.getPage(pageUrl);
                HtmlUnorderedList ulist = listPage.getFirstByXPath("/html/body/div[4]/div[2]/div[2]/div[2]/ul");
                if (ulist == null) break;
                String[] productUrls = RegexUtil.getSubStrings(ulist.asXml(), productUrlRegex);
                if (productUrls == null) break;
                List<String> urls = Arrays.stream(productUrls).distinct().collect(Collectors.toList());
                log.info("total {} products", urls.size());
                for (String u : urls) {
                    String url = "https://" + u;
                    if (fetchedUrls.contains(url)) continue;
                    String[] products = fetchProducts(wc, url);
                    for (String prodUrl : products) {
                        if (fetchedUrls.contains(prodUrl)) continue;
                        log.info("fetch {}", prodUrl);
                        Product product = fetchProduct(wc, prodUrl);
                        if (StringUtils.isNotBlank(product.getCommonName())) {
                            numProd++;
                            String json = JSONObject.toJSONString(product);
                            log.info("{}", json);
                            fos.write(json.getBytes());
                            fos.write("\n".getBytes());
                            fetchedUrls.add(prodUrl);
                            if (numProd > maxPreCategory) break;
                        }
                    }
                    if (numProd > maxPreCategory) break;
                }
                if (numProd > maxPreCategory) break;
            }
        }
        fos.close();
    }

    @SneakyThrows
    private static String[] fetchProducts(WebClient wc, String productUrl) {
        List<String> list = new ArrayList<>();
        list.add(productUrl);

        HtmlPage page = wc.getPage(productUrl);
        HtmlDefinitionDescription cn = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/dl[4]/div/div[1]/dl[5]/dd");
        if (cn != null && "specif".equals(cn.getAttribute("class"))) {
            DomNodeList<DomNode> ans = cn.getChildNodes();
            for (DomNode an : ans) {
                if (an instanceof HtmlAnchor) {
                    String href = ((HtmlAnchor) an).getHrefAttribute();
                    if (!"#".equals(href)) {
                        list.add(href);
                    }
                }
            }
        }

        return list.toArray(new String[0]);
    }

    @SneakyThrows
    private static Product fetchProduct(WebClient wc, String productUrl) {
        HtmlPage page = wc.getPage(productUrl);

        HtmlAnchor firstA = page.getFirstByXPath("/html/body/div[3]/div[1]/div/a[2]");
        HtmlAnchor secA = page.getFirstByXPath("/html/body/div[3]/div[1]/div/a[3]");
        HtmlHeading1 title = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/div[1]/div[2]/h1");
        HtmlSpan viceTitle = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/div[2]/span[1]");

        HtmlAnchor commonName = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/dl[1]/dd/a");
        HtmlSpan registerNo = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/dl[3]/dd/span");

        HtmlEmphasis price = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/dl[5]/div/div[1]/dl[1]/dd/em");
        HtmlAnchor factory = page.getFirstByXPath("/html/body/div[3]/div[2]/div[3]/dl[5]/div/div[1]/dl[6]/dd/a");

        HtmlTableBody describe = page.getFirstByXPath("/html/body/div[3]/div[3]/div[2]/div/div[2]/div[1]/table/tbody");


        HtmlDivision instructions = page.getFirstByXPath("/html/body/div[3]/div[3]/div[2]/div/div[2]/div[2]/div");

        HtmlDivision detailPics = page.getFirstByXPath("/html/body/div[3]/div[3]/div[2]/div/div[2]/div[1]/div/div[1]/div[1]");

        HtmlDivision mainPics = page.getFirstByXPath("/html/body/div[3]/div[2]/div[1]/div[2]/div[2]");

        Product product = new Product()
                .setFirstCategory(safeGetHtmlElementText(firstA))
                .setSecondCategory(safeGetHtmlElementText(secA))
                .setTitle(safeGetHtmlElementText(title))
                .setViceTitle(safeGetHtmlElementText(viceTitle))
                .setCommonName(safeGetHtmlElementText(commonName))
                .setRegisterNo(safeGetHtmlElementText(registerNo))
                .setFactory(safeGetHtmlElementText(factory));

        if (price != null) {
            product.setPrice(safeGetHtmlElementText(price));
        } else {
            product.setPrice(getPrice(wc, productUrl));
        }

        product.setProductName(getTableBodyValue(describe, "产品品名"));
        product.setMainMaterials(getTableBodyValue(describe, "主要原料"));
        product.setMainEffect(getTableBodyValue(describe, "主要作用"));
        product.setSpecifications(getTableBodyValue(describe, "产品规格"));
        product.setUsage(getTableBodyValue(describe, "用法用量"));
        product.setProductFactory(getTableBodyValue(describe, "生产企业"));

        if (instructions != null) {
            String ix = instructions.asXml();
            product.setInstructions(
                    ix.replaceAll("<div.+>", "")
                            .replaceAll("<\\w{1,3}>", "")
                            .replaceAll("<!--.+-->", "")
                            .replaceAll("&nbsp;", "")
                            .replaceAll("<textarea([\\w\\W]*)</div>", "")
                            .replaceAll("\\s{2,}", "")
                            .replaceAll("</\\w{1,3}>", "\n")
                            .replaceAll("\n\n", "\n")
                            .replaceAll(" ", "")
            );
        }

        product.setDetailPics(fetchDescribeImg(detailPics));
        product.setMainPics(fetchMainPic(mainPics));

        product.setUrl(productUrl);

        return product;
    }

    private static String safeGetHtmlElementText(HtmlElement elm) {
        if (elm != null && elm.getFirstChild() != null) {
            return elm.getFirstChild().asText();
        }
        return null;
    }

    private static String getTableBodyValue(HtmlTableBody tbody, String key) {
        if (tbody == null) return null;
        DomNodeList<DomNode> trs = tbody.getChildNodes();
        for (DomNode dn : trs) {
            if (dn instanceof HtmlTableRow) {
                HtmlTableRow tr = (HtmlTableRow) dn;
                HtmlTableCell keyCell = tr.getCell(0);
                if (keyCell.getTextContent().contains(key)) {
                    return tr.getCell(1).getTextContent();
                }
            }
        }
        return null;
    }

    private static String[] fetchDescribeImg(HtmlDivision div) {
        List<String> list = new ArrayList<>();
        if (div != null) {
            DomNodeList<DomNode> childNodes = div.getChildNodes();
            for (DomNode dn : childNodes) {
                if (dn instanceof HtmlImage) {
                    list.add(((HtmlImage) dn).getSrcAttribute());
                }
            }
        }
        return list.toArray(new String[0]);
    }

    private static String[] fetchMainPic(HtmlDivision div) {
        List<String> list = new ArrayList<>();
        if (div != null) {
            DomNodeList<DomNode> divCn = div.getChildNodes();
            for (DomNode fc : divCn) {
                if (fc instanceof HtmlUnorderedList) {
                    HtmlUnorderedList ul = (HtmlUnorderedList) fc;
                    DomNodeList<DomNode> childNodes = ul.getChildNodes();
                    for (DomNode dn : childNodes) {
                        if (dn instanceof HtmlListItem) {
                            HtmlListItem li = (HtmlListItem) dn;
                            DomNodeList<DomNode> lic = li.getChildNodes();
                            for (DomNode licn : lic) {
                                if (licn instanceof HtmlAnchor) {
                                    DomNodeList<DomNode> acns = licn.getChildNodes();
                                    for (DomNode acn : acns) {
                                        if (acn instanceof HtmlImage) {
                                            list.add("https:" + ((HtmlImage) acn).getSrcAttribute());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return list.toArray(new String[0]);
    }

    @SneakyThrows
    private static String getPrice(WebClient wc, String productUrl) {
        String productCode = RegexUtil.getFirstMatch(productUrl, "[0-9]+");
        if (productCode == null) return null;
        URL url = UrlUtils.toUrlSafe("https://www.jianke.com/wcgi//promos/promos/pc/" + productCode);
        WebRequest webRequest = new WebRequest(url, HttpMethod.GET);
        Page page = wc.getPage(webRequest);
        InputStream content = page.getWebResponse().getContentAsStream();
        byte[] data = StreamUtil.fromStream(content);
        String jsonStr = new String(data);
        JSONObject json = JSONObject.parseObject(jsonStr);
        JSONObject item = json.getJSONObject("item");

        if (item != null && item.getInteger("price") != null) {
            return String.format("￥%.2f", (item.getInteger("price") / 100.0f));
        }
        return null;
    }

    @Data
    @Accessors(chain = true)
    private static class Product {
        private String firstCategory;
        private String secondCategory;
        private String title;
        private String viceTitle;
        private String commonName;
        private String registerNo;
        private String specifications;
        private String price;
        private String factory;
        private String productName;
        private String mainMaterials;
        private String mainEffect;
        private String usage;
        private String productFactory;
        private String instructions;
        private String[] mainPics, detailPics;
        private String url;
    }
}
