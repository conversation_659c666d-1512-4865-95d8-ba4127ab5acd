package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.sensor.lb.LBOverLimitHandlingInfo;
import com.gtg56.idas.common.entity.sensor.lb.LBRealTimeInfo;
import com.gtg56.idas.common.entity.sensor.lb.LBSensorInfo;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class LBCollector extends BaseCollector {

    private final WarehouseSensorClient openTSDBClient;
    private final String warehouseCode;
    private final String warehouseName;
    private final IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;
    private final IWarehouseSensorService warehouseSensorService;
    private final MQProducer sensorRecordProducer;
    private final MQProducer overLimitHandlingProducer;
    private final String url;
    private final String user;
    private final String password;
    private final Integer dimRound;

    public LBCollector(Properties properties) {
        super(properties);
        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);

        warehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        url = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.url");
        user = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.user");
        password = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.password");
        dimRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));
    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {
        CollectResult cr = new CollectResult(0L, 0L);

        try (Connection conn = DriverManager.getConnection(url, user, password)) {
            if (currentCycle() % dimRound == 1) {
                cr.incr(collectDimension(conn));
            }

            cr.incr(collectRealTime(conn));
            cr.incr(collectOverLimit(conn));

        } catch (Exception e) {
            log.error("query from lb database fail \n{}", ExceptionUtils.getStackTrace(e));
        }

        return cr;
    }

    private static final String DIM_SQL = "SELECT h.id as regionCode,h.`name` as regionName, \n" +
            "concat(d.measureCode,'_',d.meterNo) as sensorCodeOrigin, d.terminalname as sensorName,\n" +
            "d.t_high as temperatureHighLimit ,d.t_low as temperatureLowLimit,d.h_high as humidityHighLimit,d.h_low as humidityLowLimit\n" +
            "FROM lb_device_information d \n" +
            "LEFT JOIN lb_house_type h \n" +
            "ON d.house_code = h.id\n" +
            "ORDER BY h.id";

    private CollectResult collectDimension(Connection conn) throws SQLException {
        PreparedStatement ps = conn.prepareStatement(DIM_SQL);
        ResultSet res = ps.executeQuery();
        List<LBSensorInfo> sensors = JDBCUtil.resultSetToBeanList(res, LBSensorInfo.class);

        long created = sensors.stream().map(s -> {
            WarehouseSensor ws = new WarehouseSensor();
            BeanUtils.copyProperties(s, ws);

            ws.setTemperatureHighLimit(new BigDecimal(s.getTemperatureHighLimit().toString()))
                    .setTemperatureLowLimit(new BigDecimal(s.getTemperatureLowLimit().toString()))
                    .setHumidityHighLimit(new BigDecimal(s.getHumidityHighLimit().toString()))
                    .setHumidityLowLimit(new BigDecimal(s.getHumidityLowLimit().toString()));

            ws.setSensorCode(warehouseCode + "-" + s.getSensorCodeOrigin());
            ws.setSensorType("")
                    .setCorpCode(SensorConsts.CORP_LB)
                    .setWarehouseCode(warehouseCode)
                    .setWarehouseName(warehouseName)
                    .setSensorFunction(SensorConsts.FUNC_SENSOR);

            return ws;
        }).filter(warehouseSensorService::createOrUpdate).count();

        return new CollectResult((long) sensors.size(), created);
    }

    private static final String REAL_TIME_SQL = "SELECT \n" +
            "b.temperature,b.humidity,b.createDate as devTime,\n" +
            "h.id as regionCode,h.`name` as regionName, \n" +
            "concat(d.measureCode,'_',d.meterNo) as sensorCodeOrigin, d.terminalname as sensorName,\n" +
            "d.t_high as temperatureHighLimit ,d.t_low as temperatureLowLimit,d.h_high as humidityHighLimit,d.h_low as humidityLowLimit\n" +
            "FROM lb_base_data_home b\n" +
            "LEFT JOIN lb_device_information d \n" +
            "ON b.measureCode = d.measureCode and b.meterNo = d.meterNo\n" +
            "LEFT JOIN lb_house_type h \n" +
            "ON d.house_code = h.id";

    private CollectResult collectRealTime(Connection conn) throws SQLException {
        PreparedStatement ps = conn.prepareStatement(REAL_TIME_SQL);
        ResultSet res = ps.executeQuery();
        List<LBRealTimeInfo> data = JDBCUtil.resultSetToBeanList(res, LBRealTimeInfo.class);
        List<WarehouseSensorRecord> produce = data.stream().flatMap(rt -> {
            Date time = rt.getDevTime();
            Long ts = time.getTime() / 1000L;
            String sensorCode = warehouseCode + "-" + rt.getSensorCodeOrigin();

            BigDecimal t = new BigDecimal(rt.getTemperature().toString());
            BigDecimal h = new BigDecimal(rt.getHumidity().toString());
            BigDecimal th = new BigDecimal(rt.getTemperatureHighLimit().toString());
            BigDecimal tl = new BigDecimal(rt.getTemperatureLowLimit().toString());
            BigDecimal hh = new BigDecimal(rt.getHumidityHighLimit().toString());
            BigDecimal hl = new BigDecimal(rt.getHumidityLowLimit().toString());

            //noinspection DuplicatedCode
            WarehouseSensorRecord tem = new WarehouseSensorRecord();
            WarehouseSensorRecord.Tags temTags = new WarehouseSensorRecord.Tags();
            temTags.setWarehouseCode(warehouseCode)
                    .setRegionCode(rt.getRegionCode())
                    .setSensorCode(sensorCode)
                    .setStatus(SensorConsts.toStatus(t, th, tl))
                    .setType(SensorConsts.TYPE_TEMPERATURE)
                    .setNormal(SensorConsts.STATUS_NORMAL.equals(temTags.getStatus()) ? CommonConsts.TRUE : CommonConsts.FALSE);
            tem.setValue(t)
                    .setTimestamp(ts)
                    .setTags(temTags);

            WarehouseSensorRecord hum = new WarehouseSensorRecord();
            WarehouseSensorRecord.Tags humTags = new WarehouseSensorRecord.Tags();
            humTags.setWarehouseCode(warehouseCode)
                    .setRegionCode(rt.getRegionCode())
                    .setSensorCode(sensorCode)
                    .setStatus(SensorConsts.toStatus(h, hh, hl))
                    .setType(SensorConsts.TYPE_HUMIDITY)
                    .setNormal(SensorConsts.STATUS_NORMAL.equals(humTags.getStatus()) ? CommonConsts.TRUE : CommonConsts.FALSE);
            hum.setValue(h)
                    .setTimestamp(ts)
                    .setTags(humTags);

            return Stream.of(tem, hum);
        }).collect(Collectors.toList());

        produce.forEach(r -> {
            openTSDBClient.put(r);
            sensorRecordProducer.produce(warehouseCode, r.toPutBody());
        });

        return new CollectResult((long) data.size(), (long) produce.size());
    }

    private static final String OVER_SQL = "SELECT s.rn , s.warningTime as startTime , s.handleTime, \n" +
            "s.handleUser as principal, s.handleType as suggestion , \n" +
            "s.measureMeterCode as sensorCodeOrigin , d.house_code as regionCode,\n" +
            "d.t_high as temperatureHighLimit,d.t_low as temperatureLowLimit, \n" +
            "d.h_high as humidityHighLimit,d.h_low as humidityLowLimit\n" +
            "FROM\n" +
            "(SELECT \n" +
            "\t@rownum := @rownum+1 as rn , \n" +
            "\ta.* from lb_warning_handle a , \n" +
            "\t(SELECT @rownum := 0)b\n" +
            "ORDER BY a.createTime) s \n" +
            "LEFT JOIN lb_device_information d \n" +
            "ON s.measureMeterCode = concat(d.measureCode,'_',d.meterNo)\n" +
            "WHERE s.rn > ?\n" +
            "ORDER BY s.rn\n" +
            "LIMIT 100";


    private CollectResult collectOverLimit(Connection conn) throws SQLException {
        RdbIncrSyncOffset offset = rdbIncrSyncOffsetService.get(url, "lb_warning_handle");

        PreparedStatement ps = conn.prepareStatement(OVER_SQL);
        ps.setLong(1, offset.getLastId());

        ResultSet res = ps.executeQuery();
        List<LBOverLimitHandlingInfo> overLimit = JDBCUtil.resultSetToBeanList(res, LBOverLimitHandlingInfo.class);

        overLimit.stream().map(ov -> {
            OverLimitHandlingEvent event = new OverLimitHandlingEvent();
            BeanUtils.copyProperties(ov, event);

            Date handleTime = null;
            try {
                handleTime = DateUtil.ymdhms().parse(ov.getHandleTime());
            } catch (ParseException ignored) {
            }

            event.setCorpCode(SensorConsts.CORP_LB)
                    .setWarehouseCode(warehouseCode)
                    .setSensorCode(warehouseCode + "-" + ov.getSensorCodeOrigin())
                    .setHandleTime(handleTime)
                    .setTemperatureHighLimit(new BigDecimal(ov.getTemperatureHighLimit().toString()))
                    .setTemperatureLowLimit(new BigDecimal(ov.getTemperatureLowLimit().toString()))
                    .setHumidityHighLimit(new BigDecimal(ov.getHumidityHighLimit().toString()))
                    .setHumidityLowLimit(new BigDecimal(ov.getHumidityLowLimit().toString()))
                    .setRange(false);
            return event;
        }).forEach(event -> overLimitHandlingProducer.produce(warehouseCode, JSON.toJSONString(event)));

        if (!overLimit.isEmpty()) {
            overLimit.stream()
                    .mapToLong(info -> info.getRn().longValue())
                    .max()
                    .ifPresent(max -> {
                        offset.setLastId(max);
                        rdbIncrSyncOffsetService.set(offset);
                    });
        }

        return new CollectResult((long) overLimit.size(), (long) overLimit.size());
    }
}
