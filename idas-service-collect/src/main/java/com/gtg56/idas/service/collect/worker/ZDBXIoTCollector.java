package com.gtg56.idas.service.collect.worker;

import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.tool.http.ZDBXIoTClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;
import java.util.stream.IntStream;

public class ZDBXIoTCollector extends BaseCollector {
    private ZDBXIoTClient client;
    private WarehouseSensorClient openTSDBClient;
    private String warehouseCode;
    private String warehouseName;
    private IWarehouseSensorService warehouseSensorService;
    private MQProducer sensorRecordProducer;
    private Integer lagDays;
    private Integer dimRound;


    public ZDBXIoTCollector(Properties properties) {
        super(properties);

        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);

        warehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        String host = activeProperties().getProperty("com.gtg56.idas.service.collect.host");
        String user = activeProperties().getProperty("com.gtg56.idas.service.collect.user");
        String pwd = activeProperties().getProperty("com.gtg56.idas.service.collect.password");
        lagDays = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.lag_days"));
        dimRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));

        client = new ZDBXIoTClient(host);
        client.setName(user);
        client.setPwd(pwd);
    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }

    @Override
    protected CollectResult collectAction() {
        CollectResult ret = new CollectResult(0L, 0L);
        boolean collectDim = currentCycle() % dimRound == 1;

        HashMap<String, WarehouseSensor> sensors = new HashMap<>();

        Date now = new Date();
        IntStream.range(-1 * lagDays, 0).forEach(lag -> {
            Date start = DateUtil.dayAdd(now, lag).getTime();
            Date end = DateUtil.dayAdd(now, lag + 1).getTime();

            List<ZDBXIoTClient.Record> records = client.getRecords(start, end);
            if (records.isEmpty()) return;
            ret.incrCollect((long) records.size());

            records.forEach(record -> {
                WarehouseSensorRecord tem = transRecord(record, SensorConsts.TYPE_TEMPERATURE);
                WarehouseSensorRecord hum = transRecord(record, SensorConsts.TYPE_HUMIDITY);
                openTSDBClient.put(tem);
                openTSDBClient.put(hum);
                sensorRecordProducer.produce(warehouseCode, tem.toPutBody());
                sensorRecordProducer.produce(warehouseCode, hum.toPutBody());
                ret.incrProduce(2L);
                if (collectDim) {
                    WarehouseSensor sensor = transConfig(record);
                    sensors.put(sensor.getSensorCode(), sensor);
                }

            });
        });
        long count = sensors.values().stream()
                .filter(warehouseSensorService::createOrUpdate)
                .count();
        ret.incrProduce(count);

        return ret;
    }

    @SneakyThrows
    private WarehouseSensorRecord transRecord(ZDBXIoTClient.Record record, String type) {
        BigDecimal value = SensorConsts.TYPE_TEMPERATURE.equals(type) ? record.getTvalue() : record.getHvalue();
        BigDecimal highLimit = SensorConsts.TYPE_TEMPERATURE.equals(type) ? record.getTmax() : record.getHmax();
        BigDecimal lowLimit = SensorConsts.TYPE_TEMPERATURE.equals(type) ? record.getTmin() : record.getHmin();
        String status = SensorConsts.toStatus(value, highLimit, lowLimit);
        String normal = SensorConsts.STATUS_NORMAL.equals(status) ? CommonConsts.TRUE : CommonConsts.FALSE;

        String timeStr = record.getTime() + ":00";
        Date time = DateUtil.ymdhms().parse(timeStr);
        Long timel = time.getTime() / 1000L;

        WarehouseSensorRecord r = new WarehouseSensorRecord();
        r.setValue(value)
                .setTimestamp(timel)
                .setTags(
                        new WarehouseSensorRecord.Tags()
                                .setWarehouseCode(warehouseCode)
                                .setRegionCode(record.getDevicecode())
                                .setSensorCode(warehouseCode + "-" + record.getNodecode())
                                .setType(type)
                                .setStatus(status)
                                .setNormal(normal)
                );

        return r;
    }

    private WarehouseSensor transConfig(ZDBXIoTClient.Record record) {
        WarehouseSensor sensor = new WarehouseSensor();
        sensor.setWarehouseCode(warehouseCode)
                .setWarehouseName(warehouseName)
                .setRegionCode(record.getDevicecode())
                .setRegionName(record.getDevicename())
                .setSensorCode(warehouseCode + "-" + record.getNodecode())
                .setSensorCodeOrigin(record.getNodecode())
                .setSensorName(record.getNodename())
                .setSensorType("")
                .setSensorFunction(SensorConsts.FUNC_CAR)
                .setCorpCode(SensorConsts.CORP_ZDBXIOT)
                .setTemperatureHighLimit(record.getTmax())
                .setTemperatureLowLimit(record.getTmin())
                .setHumidityHighLimit(record.getHmax())
                .setHumidityLowLimit(record.getHmin());

        return sensor;
    }
}
