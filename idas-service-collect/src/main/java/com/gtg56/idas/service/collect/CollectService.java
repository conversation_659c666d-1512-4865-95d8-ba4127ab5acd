package com.gtg56.idas.service.collect;

import com.gtg56.idas.common.entity.dto.CollectorStatusDTO;
import com.gtg56.idas.service.collect.util.ZkReportUtil;
import com.gtg56.idas.service.define.ICollectService;
import org.apache.curator.framework.CuratorFramework;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

@DubboService(interfaceClass = ICollectService.class, protocol = "dubbo", registry = "zookeeper", filter = "-exception")
public class CollectService implements ICollectService {
    
    @Resource(name = "zkClient")
    CuratorFramework zkClient;
    
    @Override
    public List<CollectorStatusDTO> listCollectors() {
        return ZkReportUtil.list(zkClient);
    }
}
