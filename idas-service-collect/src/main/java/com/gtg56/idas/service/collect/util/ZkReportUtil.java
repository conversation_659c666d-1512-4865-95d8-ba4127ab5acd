package com.gtg56.idas.service.collect.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.entity.dto.CollectorStatusDTO;
import lombok.SneakyThrows;
import org.apache.curator.framework.CuratorFramework;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.KeeperException;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ZkReportUtil {
    
    @SneakyThrows
    public static void set(CuratorFramework zkClient, String path, CollectorStatusDTO dto) {
        String produce = JSON.toJSONString(dto);
        
        boolean exists = zkClient.checkExists().forPath(path) != null;
        
        if(exists) {
            zkClient.setData()
                    .forPath(path, produce.getBytes());
        } else {
            zkClient.create()
                    .creatingParentsIfNeeded()
                    .withMode(CreateMode.EPHEMERAL)
                    .forPath(path, produce.getBytes());
        }
    }
    
    @SneakyThrows
    public static CollectorStatusDTO get(CuratorFramework zkClient,String path) {
        try {
            byte[] data = zkClient.getData().forPath(path);
    
            return JSONObject.parseObject(new String(data)).toJavaObject(CollectorStatusDTO.class);
        } catch (KeeperException.NoNodeException e) {
            return null;
        }
    }
    
    @SneakyThrows
    public static List<CollectorStatusDTO> list(CuratorFramework zkClient) {
        List<String> children = zkClient.getChildren().forPath(CommonConsts.ZK_PATH_COLLECTOR_REPORT);
        return children.stream()
                .map(child -> get(zkClient,CommonConsts.ZK_PATH_COLLECTOR_REPORT + "/" + child))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
