package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.LgInterfaceInfo;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.sensor.lg.LGDimVo;
import com.gtg56.idas.common.entity.sensor.lg.LGOverLimitHandling;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

@Slf4j
public class LGCollector extends BaseCollector {

    private final WarehouseSensorClient openTSDBClient;//时序数据库客户端
    private final IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;//增量同步数据处理
    private final IWarehouseSensorService warehouseSensorService;//传感器维度配置
    private final MQProducer sensorRecordProducer;  //实时数据处理kafka
    private final MQProducer overLimitHandlingProducer;//异常处理数据kafka

    private final String jdbcUrl;
    private final String jdbcUser;
    private final String jdbcPassword;
    private final String interfaceUrl;
    private final String interfaceUser;
    private final String interfacePassword;
    private final String strWarehouseCode;//仓库编号
    private final String strWarehouseName;//仓库名字
    private final Integer intRound;//多少个采集周期采集一次维度
    private final Boolean blincr;//是否增量同步
    private final Boolean blFirst;//是否增第一次运行，第一次运行先采集维度信息
    private List<LGDimVo> listdim;
    private Map<String, LGDimVo> dimMap;
    private Integer overLimitRound;
    private Date dtoverLimitRount_last = new Date();
    private Date dtoverLimitRount_Now;
    private Date ovelimitS_Date;

    public LGCollector(Properties properties) throws ParseException {
        super(properties);

        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);

        jdbcUrl = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.url");
        jdbcUser = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.user");
        jdbcPassword = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.password");

        interfaceUrl = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.interfaceurl");
        interfaceUser = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.interfaceuser");
        interfacePassword = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.interfacepassword");

        strWarehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        strWarehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");

        //  intCycle = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.cycle"));
        intRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));

        blincr = Boolean.parseBoolean(activeProperties().getProperty("com.gtg56.idas.service.collect.incr"));
        blFirst = Boolean.parseBoolean(activeProperties().getProperty("com.gtg56.idas.service.collect.firstRun"));

        overLimitRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.overlimitRound"));

    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + strWarehouseCode;
    }


    private static final String interFace_SQL =
            "SELECT  id , warehousecode, sn, address, devtime, " +
                    "pctime, channelport, value, upper, lower,opertime" +
                    " FROM t_lg_interface_tem_hum " +
                    " WHERE id >?  and warehousecode = ?  order by id LIMIT  100  ";
    private static final String dim_SQL = "SELECT\n" +
            "\twh.Id as regionCode,\n" +
            "\twh.Name as regionName ,\n" +
            "\tdev.SN as sensorCodeOrigion ,\n" +
            "\tdev.Name as sensorName ,\n" +
            "\ttc.Upper as temperatureHighLimit,\n" +
            "\ttc.Lower as temperatureLowLimit,\n" +
            "\thc.Upper as humidityHighLimit,\n" +
            "\thc.Lower as humidityLowLimit\n" +
            "FROM\n" +
            "\tWareHouse_Info wh\n" +
            "LEFT JOIN Warehouse_Dev whd ON\n" +
            "\twh.Id = whd.WareHouseId\n" +
            "LEFT JOIN Dev_Info dev ON\n" +
            "\twhd.DevId = dev.Id\n" +
            "LEFT JOIN Dev_Channel tc ON\n" +
            "\tdev.SN = tc.SN\n" +
            "\tand tc.[Index] = 1\n" +
            "LEFT JOIN Dev_Channel hc ON\n" +
            "\tdev.SN = hc.SN\n" +
            "\tand hc.[Index] = 2";

    private static final String overLimit_SQL = "SELECT rd.Id  as recordId ,\n" +
            "\t wh.Id as orgCode,\n" +
            "\t wh.Name as orgName ,\n" +
            "\t dev.SN as nodeCode ,--仪器SN号\n" +
            "\t dev.Name as nodeName ,\n" +
            "\t rct.Value as temperature ,--温度\n" +
            "\t rch.Value as humidity ,--湿度\n" +
            "\t tc.Upper as temperatureHighLimit,--温度上限\n" +
            "\t tc.Lower as temperatureLowLimit,--温度下限\n" +
            "\t hc.Upper as humidityHighLimit,--湿度上限\n" +
            "\t hc.Lower as humidityLowLimit ,--湿度下限\n" +
            "\t rd.DevTime as recordDateTime,\n" +
            "\t rd.OverReason as overReason \n" +
            "FROM\n" +
            "\t Record_Data rd\n" +
            "LEFT JOIN Record_Channel rct ON\n" +
            "\t rd.Id = rct.DataId\n" +
            "\t and rct.ChannelPort = 1\n" +
            "LEFT JOIN Record_Channel rch ON\n" +
            "\t rd.Id = rch.DataId\n" +
            "\t and rch.ChannelPort = 2\n" +
            "LEFT JOIN Dev_Info dev ON\n" +
            "\t rd.SN = dev.SN\n" +
            "LEFT JOIN Dev_Channel tc ON\n" +
            "\t dev.SN = tc.SN\n" +
            "\t and tc.[Index] = 1\n" +
            "LEFT JOIN Dev_Channel hc ON\n" +
            "\t dev.SN = hc.SN\n" +
            "\t and hc.[Index] = 2\n" +
            "LEFT JOIN Warehouse_Dev whd ON\n" +
            "\t whd.DevId = dev.Id " +
            "LEFT JOIN WareHouse_Info wh ON\n" +
            "\t wh.Id = whd.WareHouseId\n" +
            "where rd.devtime  > ? \n" +
            " and rd.OverReason  !=''\n" +
            "\t order by rd.id  ";

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {

        CollectResult ret = new CollectResult(0L, 0L);
        try (Connection conn = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword);
             Connection interfaceConn = DriverManager.getConnection(interfaceUrl, interfaceUser, interfacePassword)) {


                //采集维度数据；

                long currentcount = currentCycle();

                //第一次同步时先采集维度信息
            if (currentcount % intRound == 1) {
                CollectResult dimResult = syncDim(conn);
                ret.incr(dimResult);
            }
                //采集实时数据；

                CollectResult recordResult = syncSensorRecord(interfaceConn);
                ret.incr(recordResult);

                /*记录错误处理的数据
                 * #1.每天记录一次
                 * #2.每次查询 N 天数据进行处理
                 * */
                ovelimitS_Date = new Date();//初始化 SQL的日期
                dtoverLimitRount_Now = new Date();//初始化当前日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

                /* 格式化日期，去除时分秒 */
                String stroverLimitHandlingNow = sdf.format(dtoverLimitRount_Now);
                String stroverLimitHandlingLast = sdf.format(dtoverLimitRount_last);
                String stroverLimitS_Date = sdf.format(ovelimitS_Date);

                /*格式话后的日期重新赋值*/
                dtoverLimitRount_Now = sdf.parse(stroverLimitHandlingNow);
                dtoverLimitRount_last = sdf.parse(stroverLimitHandlingLast);
                ovelimitS_Date = sdf.parse(stroverLimitS_Date);


                /*判断最后执行记录错误记录的时间是否与当前时间为同一天，（每天只记录一次）*/
                if (dtoverLimitRount_Now.after(dtoverLimitRount_last)){
                    ovelimitS_Date.setTime(ovelimitS_Date.getTime() - Duration.ofDays(overLimitRound).toMillis());
                    CollectResult croverLimitHandling = syncOverlimitHandling(conn);
                    ret.incr(croverLimitHandling);

                    /*记录最后的执行时间*/
                    dtoverLimitRount_last.setTime(dtoverLimitRount_Now.getTime());
                }

            }catch (SQLException e) {

                log.warn(e.getMessage() + "  失败！！！", e);
            }
           catch (ParseException e) {
                e.printStackTrace();
            }
        return ret;
    }

    /*采集实时数据*/
    private CollectResult syncSensorRecord(Connection interfaceConn) throws SQLException {


        /* #1. 获取接口数据
         *  #2. 获取维度数据
         *  #3. 根据设备编号，仓库信息拼接温湿度数据
         *  #4. 处理温湿度数据
         *  #5. 记录更新数据最后的id
         * */
        //判断是否增量同步，如果是则选择增量同步SQL；

        String strSQL = interFace_SQL;
        PreparedStatement ps = interfaceConn.prepareStatement(strSQL);

        //获取插入表的最后插入的Id,
        RdbIncrSyncOffset rdbIncrSyncOffset = rdbIncrSyncOffsetService.get(interfaceUrl, "lg_infterface_tem_hum");  // 关系型数据库数据

        Long LastID = rdbIncrSyncOffset.getLastId();
        ps.setLong(1, LastID);//传SQL参数；
        ps.setString(2, strWarehouseCode);

        ResultSet rs = ps.executeQuery();//获取数据结果集

        List<LgInterfaceInfo> resoults = JDBCUtil.resultSetToBeanList(rs, LgInterfaceInfo.class);
        List<LgInterfaceInfo> listProcessed = resoults.parallelStream()
                /* 踢出 无效的数据 */
                .filter(record -> record.getDevTime() != null && record.getPcTime() != null &&
                        record.getSN() != null
                )
                .peek(record -> record.setSN(strWarehouseCode + "-" + record.getSN()))//设备ID
                .collect(Collectors.toList());
        ps.close();

        /* #2 获取维度数据
         * #3 组装实体
         * */
        listProcessed.parallelStream()
                .forEach(record -> {
                            /*Goto row 525 */
                            String sn = record.getSN();
                            LGDimVo lgDimVo = dimMap.get(sn); //通过仓库编号+设备编号

                            String channenlPort = record.getChannelPort();
                            BigDecimal value;
                            BigDecimal upper; //上限
                            BigDecimal lower;   //下限
                            String Status;
                            long timestamp = record.getDevTime().getTime() / 1000;


                            WarehouseSensorRecord sensorRecord = new WarehouseSensorRecord();
                            //如果是1，那么值存的就是温度，2就是湿度
                            if ("1".equals(channenlPort)) {

                                /*温度*/
                                value = record.getValue();//温度
                                upper = BigDecimal.valueOf((lgDimVo.getTemperatureHighLimit())); //上限
                                lower = BigDecimal.valueOf(lgDimVo.getTemperatureLowLimit());   //下限
                                Status = SensorConsts.toStatus(value, upper, lower);

                            } else {

                                value = record.getValue();//湿度
                                upper = BigDecimal.valueOf(lgDimVo.getHumidityHighLimit());//上限
                                lower = BigDecimal.valueOf(lgDimVo.getHumidityLowLimit());//下限
                                Status = SensorConsts.toStatus(value, upper, lower);
                            }
                            WarehouseSensorRecord whsr = new WarehouseSensorRecord();
                            whsr.setValue(value)
                                    .setTimestamp(timestamp)
                                    .setTags(new WarehouseSensorRecord.Tags()
                                            .setWarehouseCode(strWarehouseCode)
                                            .setRegionCode(lgDimVo.getRegionCode())
                                            .setSensorCode(lgDimVo.getSensorCodeOrigion())
                                            .setStatus(Status)
                                            /*1.是取值温度，2是取值湿度
                                             *  这里判断是温度还是时段，传不同的类型*/
                                            .setType("1".equals(channenlPort) ? SensorConsts.TYPE_TEMPERATURE : SensorConsts.TYPE_HUMIDITY)
                                            .setNormal(SensorConsts.STATUS_NORMAL.equals(Status) ? CommonConsts.TRUE : CommonConsts.FALSE)
                                    );
                            openTSDBClient.put(whsr);//时序数据写入
                            sensorRecordProducer.produce(strWarehouseCode, whsr.toPutBody());
                        }
                );
        CollectResult realTimeResult = new CollectResult((long) listProcessed.size(), (long) listProcessed.size());
        //判断是否增量更新，记录最后更新的最大Id；

        resoults.parallelStream()
                .mapToLong(LgInterfaceInfo::getId)
                .max()
                .ifPresent(currMaxId -> rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(currMaxId)));

        return realTimeResult;
    }

    //维度数据采集；
    private CollectResult syncDim(Connection conn) throws SQLException {

        PreparedStatement psDim = conn.prepareStatement(dim_SQL);
        ResultSet rs = psDim.executeQuery();
        listdim = JDBCUtil.resultSetToBeanList(rs, LGDimVo.class);
        long l_count = listdim.parallelStream()
                .map(mpDim -> {

                    WarehouseSensor ws = new WarehouseSensor();
                    ws.setCorpCode(SensorConsts.CORP_LG);//设备厂商编号
                    ws.setWarehouseCode(strWarehouseCode);
                    ws.setWarehouseName(strWarehouseName);
                    ws.setRegionCode(mpDim.getRegionCode());//区域编号
                    ws.setRegionName(mpDim.getRegionName());//区域名称
                    ws.setSensorCode(strWarehouseCode + '-' + mpDim.getSensorCodeOrigion());//设备编号
                    ws.setSensorCodeOrigin(mpDim.getSensorCodeOrigion());//原始设备编码
                    ws.setSensorName(mpDim.getSensorName());//设备名称

                    ws.setSensorType(" ");//设备类型【暂无】
                    ws.setSensorFunction(SensorConsts.FUNC_SENSOR);


                    ws.setTemperatureHighLimit(BigDecimal.valueOf(mpDim.getTemperatureHighLimit()));//温度上限
                    ws.setTemperatureLowLimit(BigDecimal.valueOf(mpDim.getTemperatureLowLimit()));//温度下限
                    ws.setHumidityHighLimit(BigDecimal.valueOf(mpDim.getHumidityHighLimit()));//湿度上限
                    ws.setHumidityLowLimit(BigDecimal.valueOf(mpDim.getHumidityLowLimit()));//湿度下限
                    return ws;
                })
                .filter(ws -> warehouseSensorService.createOrUpdate(ws))
                .count();

        psDim.close();

        dimMap = listdim.stream()
                .peek(record -> record.setSensorCodeOrigion(strWarehouseCode + "-" + record.getSensorCodeOrigion()))
                .collect(Collectors.toMap(LGDimVo::getSensorCodeOrigion, vo -> vo));

        return new CollectResult((long) listdim.size(), l_count);


    }

    //错误日志记录
    private CollectResult syncOverlimitHandling(Connection conn) throws SQLException {


        java.sql.Date sqlDate = new java.sql.Date(ovelimitS_Date.getTime());

        PreparedStatement ps = conn.prepareStatement(overLimit_SQL);
        ps.setDate(1, sqlDate);//传SQL参数；
        ResultSet rs = ps.executeQuery();

        List<LGOverLimitHandling> lgOverLimitHandlingList = JDBCUtil.resultSetToBeanList(rs, LGOverLimitHandling.class);


        lgOverLimitHandlingList.parallelStream()
                .forEach(overlimithandling -> {

                    OverLimitHandlingEvent overLimitHandlingEvent = new OverLimitHandlingEvent();
                    overLimitHandlingEvent.setCorpCode(SensorConsts.CORP_LG);//设备厂家编码
                    overLimitHandlingEvent.setWarehouseCode(strWarehouseCode);//仓库编码
                    overLimitHandlingEvent.setRegionCode(overlimithandling.getOrgCode());//区域编码
                    overLimitHandlingEvent.setSensorCode(strWarehouseCode+ "-"+ overlimithandling.getNodeCode());//传感器编码
                    overLimitHandlingEvent.setTemperatureHighLimit(BigDecimal.valueOf(overlimithandling.getTemperatureHighLimit()));//温度上限
                    overLimitHandlingEvent.setTemperatureLowLimit(BigDecimal.valueOf(overlimithandling.getTemperatureLowLimit()));//温度下限
                    overLimitHandlingEvent.setHumidityHighLimit(BigDecimal.valueOf(overlimithandling.getHumidityHighLimit()));//湿度上限
                    overLimitHandlingEvent.setHumidityLowLimit(BigDecimal.valueOf(overlimithandling.getHumidityLowLimit()));//湿度下限
                    overLimitHandlingEvent.setStartTime(overlimithandling.getRecordDateTime());//开始时间

                    String overReason = overlimithandling.getOverReason();

                    JSONObject json;
                    //处理Json数据，去掉最后一个‘逗号’ 兼容多个JSON。以JSON Array解析数据
                    if (overReason.endsWith(",")) {
                        JSONArray array = JSON.parseArray("[" + StringUtils.left(overReason, overReason.length() - 1) + "]");
                        if (array.size() == 0) return;
                        //如果Json 数组只有一个，那么取当前，如果不是，则取最后一个
                        json = array.getJSONObject(array.size() == 1 ? 0 : (array.size() - (array.size() - 1)));
                    } else {

                        json = JSONObject.parseObject(overReason);
                    }

                    //拼接处理时间
                    String date = json.getString("adddate");
                    String time = json.getString("addtime");
                    String ymdhms = date + " " + time + ":00";
                    try {
                        Date handleTime = ymdhms.contains("/") ? DateUtil.tsdb().parse(ymdhms) : DateUtil.ymdhms().parse(ymdhms);
                        overLimitHandlingEvent.setHandleTime(handleTime);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                    overLimitHandlingEvent.setRange(false);//无是否时间范围，读取开始时间
                    overLimitHandlingEvent.setPrincipal(json.getString("userrealname"));//处理人
                    overLimitHandlingEvent.setSuggestion(json.getString("overreason"));//处理意见


                    overLimitHandlingProducer.produce(strWarehouseCode, JSON.toJSONString(overLimitHandlingEvent));
                });

        return new CollectResult((long) listdim.size(), (long) lgOverLimitHandlingList.size());


    }


}