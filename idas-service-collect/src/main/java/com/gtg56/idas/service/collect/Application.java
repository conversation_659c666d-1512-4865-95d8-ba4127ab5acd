package com.gtg56.idas.service.collect;

import com.gtg56.idas.common.tool.CollectArgsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.List;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.gtg56.idas.common.service",
                "com.gtg56.idas.common.util",
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@DubboComponentScan
@MapperScan("com.gtg56.idas.common.mapper")
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(value = {"classpath:/collectServiceBeans.xml"})
public class Application {
    
    public static void main(String[] args) {
        List<String> configFilePaths =
                CollectArgsHelper.getConfigFilePaths(args);
        if(configFilePaths.isEmpty()) {
            System.out.println("no config file");
        } else {
            ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
            CollectorManager collectorManager = new CollectorManager();
            configFilePaths.forEach(collectorManager::startCollector);
        }
    }
}
