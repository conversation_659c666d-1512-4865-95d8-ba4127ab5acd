package com.gtg56.idas.service.collect;

import com.gtg56.idas.api.facade.controller.platform.ManualCollectController.ManualCollectResult;
import com.gtg56.idas.service.collect.worker.BaseCollector;
import com.gtg56.idas.service.collect.worker.ZDV2Collector;
import com.gtg56.idas.service.define.IManualCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

@Slf4j
@Service
@DubboService(interfaceClass = IManualCollectService.class, protocol = "dubbo", registry = "zookeeper", filter = "-exception")
public class ManualCollectService implements IManualCollectService {

    @Override
    public ManualCollectResult supplementData(String warehouseCode, String date, boolean includeAlarm) {
        try {
            // 根据仓库编码查找对应的配置文件
            Properties properties = findCollectorProperties(warehouseCode);
            if (properties == null) {
                return new ManualCollectResult(warehouseCode, date, includeAlarm, 0L, 0L, 
                    "FAILED", "未找到仓库编码对应的采集器配置");
            }

            // 检查是否为ZDV2Collector
            String collectorType = properties.getProperty("com.gtg56.idas.service.collect.collectorType");
            if (!"ZDV2Collector".equals(collectorType)) {
                return new ManualCollectResult(warehouseCode, date, includeAlarm, 0L, 0L, 
                    "FAILED", "该仓库不支持手动数据补充功能，仅支持ZDV2Collector类型");
            }

            // 创建ZDV2Collector实例
            ZDV2Collector collector = new ZDV2Collector(properties);
            
            // 执行手动数据采集
            BaseCollector.CollectResult result = collector.manualCollectData(date, includeAlarm);
            
            return new ManualCollectResult(warehouseCode, date, includeAlarm, 
                result.getTotal(), result.getSuccess(), "SUCCESS", 
                String.format("成功补充数据，总记录数: %d, 成功处理: %d", result.getTotal(), result.getSuccess()));
                
        } catch (Exception e) {
            log.error("Manual collect data failed for warehouse: " + warehouseCode + ", date: " + date, e);
            return new ManualCollectResult(warehouseCode, date, includeAlarm, 0L, 0L, 
                "FAILED", "数据补充失败: " + e.getMessage());
        }
    }

    /**
     * 根据仓库编码查找对应的配置文件
     */
    private Properties findCollectorProperties(String warehouseCode) {
        // 配置文件目录
        String configDir = "config";
        File configDirFile = new File(configDir);
        
        if (!configDirFile.exists() || !configDirFile.isDirectory()) {
            log.warn("Config directory not found: " + configDir);
            return null;
        }

        // 递归查找配置文件
        return findPropertiesInDirectory(configDirFile, warehouseCode);
    }

    /**
     * 在目录中递归查找包含指定仓库编码的配置文件
     */
    private Properties findPropertiesInDirectory(File directory, String warehouseCode) {
        File[] files = directory.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归查找子目录
                Properties result = findPropertiesInDirectory(file, warehouseCode);
                if (result != null) {
                    return result;
                }
            } else if (file.getName().endsWith(".properties")) {
                // 检查properties文件
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(file)) {
                    props.load(fis);
                    String configWarehouseCode = props.getProperty("com.gtg56.idas.service.collect.warehouseCode");
                    if (warehouseCode.equals(configWarehouseCode)) {
                        log.info("Found matching config file: " + file.getAbsolutePath());
                        return props;
                    }
                } catch (IOException e) {
                    log.warn("Failed to load properties file: " + file.getAbsolutePath(), e);
                }
            }
        }
        
        return null;
    }
}
