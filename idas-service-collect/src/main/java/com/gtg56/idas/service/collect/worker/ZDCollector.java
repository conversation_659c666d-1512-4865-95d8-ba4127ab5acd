package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.entity.sensor.zd.ZDAlarm;
import com.gtg56.idas.common.entity.sensor.zd.ZDCorpDevice;
import com.gtg56.idas.common.entity.sensor.zd.ZDSensorRecord;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.tool.http.wsdl.ZDClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ZDCollector extends BaseCollector {

    private WarehouseSensorClient openTSDBClient; //时序数据库客户端
    private String warehouseCode; //仓库编码
    private String warehouseName;//仓库名称
    private IWarehouseSensorService warehouseSensorService;//维表配置信息
    private MQProducer sensorRecordProducer;//kafka生产者对象
    private ZDClient zdClient;//webservice客户端
    private Boolean blincr;//是否同步历史数据
    private String startDate_his;//历史数据抽数开始时间
    private String corpid_his;
    private MQProducer overLimitHandlingProducer;
    private IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;
    private String wsdlAddress;
    private String wsdlUsername;
    private String wsdlPassword;
    private int recycle;
    private  Map<String,Long>  devicesTs; //记录每个设备的当前时间戳




    public ZDCollector(Properties properties) {
        super(properties);
        //构造器初始化配置信息
        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);


        warehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        wsdlAddress = activeProperties().getProperty("com.gtg56.idas.service.collect.wsdl.address");
        wsdlUsername = activeProperties().getProperty("com.gtg56.idas.service.collect.wsdl.username");
        wsdlPassword = activeProperties().getProperty("com.gtg56.idas.service.collect.wsdl.password");
        startDate_his = activeProperties().getProperty("com.gtg56.idas.service.collect.startDate_his");
        blincr = Boolean.parseBoolean(activeProperties().getProperty("com.gtg56.idas.service.collect.incr"));
        corpid_his = activeProperties().getProperty("com.gtg56.idas.service.collect.corpid_his");
        zdClient = new ZDClient(wsdlAddress, wsdlUsername, wsdlPassword);
        recycle = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.recycle")) ;
        devicesTs = new HashMap<>();

    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {

        CollectResult ret = new CollectResult(0L,0L);
        CollectResult produce = null;
        List<ZDSensorRecord> results = null;

        //采集温湿度实时及历史数据
        //实时数据同步
        if (!blincr) {
            //webservice获取实时数据，以Json格式返回
            results = zdClient.getCurrent();

            produce = getWarehouseSensorRecords(results);
            ret.incr(produce);

        } else//历史数据同步,配置文件配好开始时间即可,系统自动计算开始时间到当前天数
        {
            String dt = startDate_his;
            //int days = Integer.parseInt(startDays_his);
            Date parse = null;
            try {
                parse = DateUtil.ymdhms().parse(dt);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //计算结束时间
            String endDate = DateUtil.ymd().format(new Date());
            int days = 0;
            try {
                //计算开始抽数时间和当前时间的天数
                days = DateUtil.dayDiff(DateUtil.ymd().parse(dt), DateUtil.ymd().parse(endDate));

            } catch (ParseException e) {
                e.printStackTrace();
            }

            Calendar calendar = DateUtil.dateToCalendar(parse);
            for (int i = 0; i < days; i++) {

                //读取当前仓库所有的设备代码信息
                for (ZDCorpDevice zdCorpDevice : zdClient.getDevice(corpid_his)) {
                    //每次取一天的数据
                    results = zdClient.getHistory(zdCorpDevice.getSbdm(), calendar.getTime(), 48);
                    produce = getWarehouseSensorRecords(results);

                    ret.incr(produce);
                }


                calendar.add(Calendar.DATE, 1);

            }

        }


        //超标处理措施数据抽取
        CollectResult result = getOverlimitHandlings();

        ret.incr(result);


        return ret;

    }

    /**
     * 由于该接口抽的异常记录数据，是采集时间来过滤，异常记录生成后，异常处理是滞后的，可能今天早上异常，下午或者第二天才处理
     * 因此需要每天回溯前几天的数据，才能把异常处理措施，人员同步过来，和兰芝确认暂时定的回溯前4天数据，回溯多少天数据可根据配置文件来调整
     * 配置参数 com.gtg56.idas.service.collect.recycle=4
     * 根据配置表rdb_incr_sync_offset中的Last_Id跑确定是按指定时间来跑，还是按当天时间往前推4天重跑,如果Last_Id=0
     * 表示按当前时间回溯前4天的数
     * @return
     */
    private CollectResult getOverlimitHandlings() {
        RdbIncrSyncOffset offset = rdbIncrSyncOffsetService.get(wsdlAddress, "ZDOverlimitHandRecords");
        Long ts =  offset.getLastId();
        Long lastts = 0L;//当前周期内该设备的最大处理时间
        Long lastts_min = 0L;//当前周期内所有设备的最后处理时间最小时间
        Date lastTtime =null;//起始时间加一分钟，接口是按分钟过滤的

        //回溯前4天的数据
        if(0 == ts)
        {

             devicesTs.put("incrFlag",0L);
            //判断是否当天首次抽数，拿集合中的日期时间戳和当天的日期时间戳比较，不同表示是跨天了，首次抽取
            Long flag = devicesTs.get("recycleFlag");
            long flag1 = DateUtils.truncate(new Date(), Calendar.DATE).getTime(); //当天的时间戳
            if(flag == null || flag != flag1)//跨天了，清空设备时间戳，日期标志，回溯前4天数据
            {
                devicesTs.clear();
                devicesTs.put("recycleFlag",DateUtils.truncate(new Date(),Calendar.DATE).getTime());
                Date addDays = DateUtils.addDays(DateUtils.truncate(new Date(),Calendar.DATE), -recycle);
                lastTtime = new Date(addDays.getTime() + 1000 * 60L);

            }
            else
            {
                Date addDays = DateUtils.addDays(DateUtils.truncate(new Date(),Calendar.DATE), -recycle);
                lastTtime = new Date(addDays.getTime() + 1000 * 60L);
            }

        }
        else {
            lastTtime = new Date(ts+1000*60L);//起始时间加一分钟，接口是按分钟过滤的
            //判断是否首次按时间戳跑数，首次需清空时间map
            Long flag = devicesTs.getOrDefault("incrFlag",0L);
            if(flag == 0 ) {
                devicesTs.clear();
            }else
            {
                devicesTs.put("incrFlag",1L);
            }
        }




        long count = 0L;

        List<ZDCorpDevice> devices = zdClient.getDevice(corpid_his);
        List<String> deviceTimes = new ArrayList<>();

        for (ZDCorpDevice device : devices) {

            Long aLong = devicesTs.get(device.getSbdm());

            try {

                List<ZDAlarm> zdAlarms = zdClient.getDeviceAlarm(device.getSbdm(), aLong != null ? new Date(aLong + 1000 * 60L) : lastTtime, 50);

                log.info("====" + device.getSbdm() + "\t" + DateUtil.ymdhms().format(aLong != null ? new Date(aLong + 1000 * 60L) : lastTtime) + "=====");

                //获取周期内该设备的最大处理时间
                String s = zdAlarms.parallelStream()
                        //因为异常记录源系统是提前生成的，而接口的时间条件是采集时间，而这时候记录还没有处理把数据过滤掉，防止kafka中太多垃圾数据
                        .filter(r -> StringUtils.isNotBlank(r.getCjsj()) && !"-".equals(r.getCjsj()))
                        .filter(r -> StringUtils.isNotBlank(r.getClsj()) && !"-".equals(r.getClsj()))
                        .filter(r -> !"ERR".equals(r.getSdz()) && !"ERR".equals(r.getWdz()))
                        .filter(Objects::nonNull)
                        .map(r -> r.getCjsj()).max(String::compareTo).orElse(null);
                if (null == s) continue;

                try {
                    lastts = DateUtil.ymdhms().parse(s).getTime();
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                //保留每个设备的最大时间
                deviceTimes.add(s);

                devicesTs.put(device.getSbdm(), lastts);

                log.info(zdAlarms.toString());
                count += zdAlarms.parallelStream()
                        .filter(r -> StringUtils.isNotBlank(r.getCjsj()) && !"-".equals(r.getCjsj()))
                        .filter(r -> StringUtils.isNotBlank(r.getClsj()) && !"-".equals(r.getClsj()))
                        .filter(r -> !"ERR".equals(r.getSdz()) && !"ERR".equals(r.getWdz()))
                        .filter(Objects::nonNull)
                        .map(r -> {
                                    String sensorCode = warehouseCode + "-" + device.getSbdm();

                                    WarehouseSensor warehouseSensor =
                                            warehouseSensorService.findBy(
                                                    new WarehouseSensorQuery()
                                                            .setWarehouseCode(warehouseCode)
                                                            .setSensorCode(sensorCode)
                                            )
                                                    .stream()
                                                    .findFirst()
                                                    .orElse(null);

                                    if (warehouseSensor != null) {
                                        Date handTime = null;
                                        try {
                                            handTime = DateUtil.ymdhms().parse(r.getClsj());
                                        } catch (ParseException e) {
                                            handTime = null;
                                        }

                                        Date startTime = null;
                                        try {
                                            startTime = DateUtil.ymdhms().parse(r.getCjsj());
                                        } catch (ParseException e) {
                                            startTime = null;
                                        }

                                        OverLimitHandlingEvent overLimitHandlingEvent = new OverLimitHandlingEvent();
                                        overLimitHandlingEvent.setCorpCode(SensorConsts.CORP_ZD);
                                        overLimitHandlingEvent.setWarehouseCode(warehouseCode);
                                        overLimitHandlingEvent.setRegionCode(warehouseSensor.getRegionCode());
                                        overLimitHandlingEvent.setSensorCode(warehouseSensor.getSensorCode());
                                        overLimitHandlingEvent.setHumidityHighLimit(warehouseSensor.getHumidityHighLimit());
                                        overLimitHandlingEvent.setHumidityLowLimit(warehouseSensor.getHumidityLowLimit());
                                        overLimitHandlingEvent.setTemperatureHighLimit(warehouseSensor.getTemperatureHighLimit());
                                        overLimitHandlingEvent.setTemperatureLowLimit(warehouseSensor.getTemperatureLowLimit());
                                        overLimitHandlingEvent.setHandleTime(handTime);
                                        /*泽大的异常数据是精确到时间点的，不需要用endTime，但是需要 startTime，同时设置Range等于false*/
                                        overLimitHandlingEvent.setStartTime(startTime);
                                        //                                    overLimitHandlingEvent.setEndTime(startTime);
                                        overLimitHandlingEvent.setRange(false);
                                        overLimitHandlingEvent.setPrincipal(r.getClry());
                                        overLimitHandlingEvent.setSuggestion(r.getClcs());

                                        return overLimitHandlingEvent;
                                    } else return null;
                                }
                        ).filter(Objects::nonNull).peek(event -> {
                            //数据写入Kafka
                            log.info("{}", JSON.toJSONString(event));
                            overLimitHandlingProducer.produce(warehouseCode, JSON.toJSONString(event));
                        }).count();

            } catch (Exception e) {
                log.warn("", e);
            }
        }
        //如果按指定时间戳跑数，把最后跑数时间写入数据库存
        if(0 != ts) {
        //保存时间戳，作为下次抽数的起始时间，取每个设备最后抽取时间最小的值
        String ss = deviceTimes.stream().min(String::compareTo).orElse(null);
        //没取到数据 直接返回
        if(ss == null)  return new CollectResult(count, count);

        try {
            lastts_min = DateUtil.ymdhms().parse(ss).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }



            offset.setLastId(lastts_min);
            rdbIncrSyncOffsetService.set(offset);
        }


        return new CollectResult(count, count);
    }



    //抽取数据，并拆分成温度和湿度数据
    private CollectResult  getWarehouseSensorRecords(List<ZDSensorRecord> results) {
        List<WarehouseSensorRecord> produce = results.parallelStream()
                .filter(record -> StringUtils.isNotBlank(record.getCjsj()) && !"-".equals(record.getCjsj())) //这里根据采集时间是否等于 "-"判断是否有空数据 ，如{"cjsj":"-","sbdm":"07","sdz":"-","wdz":"-"}
                .filter(record -> !"ERR".equals(record.getWdz()) && !"ERR".equals(record.getSdz()))//剔除脏数据{"cjsj":"2020-08-30 14:30:00","sbdm":"7E4800384","sdz":"ERR","wdz":"ERR"}
                .flatMap(record ->
                {
                    /*sbdm 设备代码,wdz 温度值,sdz 湿度值,cjsj 采集时间*/
                    String sbdm = record.getSbdm();
                    String wdz = record.getWdz();
                    String sdz = record.getSdz();
                    String cjsj = record.getCjsj();
                    long time = 0l;
                    try {
                        time = DateUtil.ymdhms().parse(cjsj).getTime();
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }


                    String sensorCode = warehouseCode + "-" + sbdm;
                    WarehouseSensor warehouseSensor =
                            warehouseSensorService.findBy(
                                    new WarehouseSensorQuery()
                                            .setWarehouseCode(warehouseCode)
                                            .setSensorCode(sensorCode)
                            )
                                    .stream()
                                    .findFirst()
                                    .orElse(null);

                    WarehouseSensorRecord tem = null;
                    WarehouseSensorRecord hum = null;

                    //处理条件：当设备编码不为空，且不存在空数据时
                    if (warehouseSensor != null) {
                        BigDecimal temperature =  null;
                        BigDecimal humidity = null;

                        //异常值默认为 0
                        if (wdz == null ||  "".equals(wdz) ||  "null".equals(wdz) || "-".equals(wdz)) {
                            wdz = "0";
                        }
                        if (sdz == null ||  "".equals(sdz) ||  "null".equals(sdz) || "-".equals(sdz)) {
                            sdz = "0";
                        }

                        temperature = new BigDecimal(wdz);
                        humidity = new BigDecimal(sdz);

                        String temperatureStatus = SensorConsts.toStatus(temperature, warehouseSensor.getTemperatureHighLimit(), warehouseSensor.getTemperatureLowLimit());
                        String humidityStatus = SensorConsts.toStatus(humidity, warehouseSensor.getHumidityHighLimit(), warehouseSensor.getHumidityLowLimit());
                        String temperatureNormal = SensorConsts.STATUS_NORMAL.equals(temperatureStatus) ? CommonConsts.TRUE : CommonConsts.FALSE;
                        String humidityNormal = SensorConsts.STATUS_NORMAL.equals(humidityStatus) ? CommonConsts.TRUE : CommonConsts.FALSE;
                        //封装温度数据
                        tem = new WarehouseSensorRecord();
                        tem.setTimestamp(time / 1000)
                                .setValue(temperature)
                                .setTags(new WarehouseSensorRecord.Tags()
                                        .setWarehouseCode(warehouseCode)
                                        .setRegionCode(warehouseSensor.getRegionCode())
                                        .setSensorCode(warehouseSensor.getSensorCode())
                                        .setType(SensorConsts.TYPE_TEMPERATURE)
                                        .setStatus(temperatureStatus)
                                        .setNormal(temperatureNormal));

                        //泽大保温箱无湿度数据
                        if(!"694d3fa82954062299a357e7a8cc3b5a-5a337071644770306247493d".equals(warehouseSensor.getRegionCode())){
                            //封装湿度数据
                            hum = new WarehouseSensorRecord();
                            hum.setTimestamp(time / 1000)
                                    .setValue(humidity)
                                    .setTags(new WarehouseSensorRecord.Tags()
                                            .setWarehouseCode(warehouseCode)
                                            .setRegionCode(warehouseSensor.getRegionCode())
                                            .setSensorCode(warehouseSensor.getSensorCode())
                                            .setType(SensorConsts.TYPE_HUMIDITY)
                                            .setStatus(humidityStatus)
                                            .setNormal(humidityNormal));
                        }
                    }

                    return Stream.of(tem, hum);

                })
                .filter(Objects::nonNull)
                .filter(record -> record.getTags() != null)
                .collect(Collectors.toList());

        produce.forEach(record -> {
            //设置日志中打印出结果，类开头要加入@Slf4j注解
            log.info("{}", record.toPutBody());
            openTSDBClient.put(record);
            sensorRecordProducer.produce(warehouseCode, record.toPutBody());
        });
        return new CollectResult((long)results.size(),(long)produce.size());
    }
}
