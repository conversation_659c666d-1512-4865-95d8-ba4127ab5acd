package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.entity.sensor.zdbx.ZDBXDimVO;
import com.gtg56.idas.common.entity.sensor.zdbx.ZDBXOverLimitHandling;
import com.gtg56.idas.common.entity.sensor.zdbx.ZDBXSensorRecordVO;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ZDBXCollector extends BaseCollector {

    private WarehouseSensorClient openTSDBClient;
    private String warehouseCode;
    private String warehouseName;
    private IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;
    private IWarehouseSensorService warehouseSensorService;
    private MQProducer sensorRecordProducer;
    private MQProducer overLimitHandlingProducer;
    private String url;
    private String user;
    private String password;
    private Integer dimRound;
    private String table;
    private boolean incr;



    public ZDBXCollector(Properties properties) {
        super(properties);

        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);
        
        warehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        url = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.url");
        user = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.user");
        password = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.password");
        dimRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));
        table = activeProperties().getProperty("com.gtg56.idas.service.collect.table");
        incr = Boolean.parseBoolean(activeProperties().getProperty("com.gtg56.idas.service.collect.incr"));

    }
    
    @Override
    protected boolean requireElection() {
        return true;
    }
    
    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }
    
    private static final String INCR_SQL = "SELECT TOP 100 cr.Id as recordId,\n" +
            "       O.Code as orgCode,O.Name as orgName,cr.NodeId as nodeCode,sn.Name as nodeName,\n" +
            "       cr.Temperature as temperature ,cr.Humidity as humidity ,\n" +
            "       sn.TemperatureHighLimit as temperatureHighLimit,sn.TemperatureLowLimit as temperatureLowLimit,\n" +
            "       sn.HumidityHighLimit as humidityHighLimit,sn.HumidityLowLimit as humidityLowLimit ,\n" +
            "       cr.RecordDateTime as recordDateTime\n" +
            "FROM ${table} cr \n" +
            "    JOIN SlaveNode sn ON cr.NodeId = sn.NodeId\n" +
            "    JOIN Organization O on sn.OrganizationId = O.Id\n" +
            "    WHERE cr.Id > ? " +
            "    ORDER BY cr.Id ";
    
    private static final String ALL_SQL = "SELECT cr.Id as recordId,\n" +
            "       O.Code as orgCode,O.Name as orgName,cr.NodeId as nodeCode,sn.Name as nodeName,\n" +
            "       cr.Temperature as temperature ,cr.Humidity as humidity ,\n" +
            "       sn.TemperatureHighLimit as temperatureHighLimit,sn.TemperatureLowLimit as temperatureLowLimit,\n" +
            "       sn.HumidityHighLimit as humidityHighLimit,sn.HumidityLowLimit as humidityLowLimit ,\n" +
            "       cr.RecordDateTime as recordDateTime\n" +
            "FROM ${table} cr \n" +
            "    JOIN SlaveNode sn ON cr.NodeId = sn.NodeId\n" +
            "    JOIN Organization O on sn.OrganizationId = O.Id\n" +
            "    ORDER BY cr.Id ";
    
    @Override
    protected CollectResult collectAction() {
        CollectResult ret = new CollectResult(0L,0L);

        try(Connection conn = DriverManager.getConnection(url, user, password)) {

            // 采集实时数据
            CollectResult recordResult = syncSensorRecord(conn);
            ret.incr(recordResult);

            // 采集超标处理数据
            CollectResult handlingResult = syncOverlimitHandling(conn);
            ret.incr(handlingResult);

            // 采集维度数据
            if(currentCycle() % dimRound == 1) {
                CollectResult dimResult = syncDim(conn);
                ret.incr(dimResult);
            }
        } catch (SQLException e) {
            log.error("query from zxbx database fail \n{}", ExceptionUtils.getStackTrace(e));
        }
        return ret;
    }
    
    private CollectResult syncSensorRecord(Connection conn) throws SQLException {

        //incr 增量同步等于true 就执行Incr_SQL 将表明替换成 参数Table，否则就查询所有;
        String sql = incr ? INCR_SQL.replace("${table}", table) : ALL_SQL.replace("${table}", table);
        PreparedStatement ps = conn.prepareStatement(sql);
        
        RdbIncrSyncOffset rdbIncrSyncOffset = rdbIncrSyncOffsetService.get(url, table);
        if(incr) {
            long lastId = rdbIncrSyncOffset.getLastId();
            ps.setLong(1,lastId);
        }
        
        ResultSet rs = ps.executeQuery();
        List<ZDBXSensorRecordVO> results = JDBCUtil.resultSetToBeanList(rs, ZDBXSensorRecordVO.class);
        List<WarehouseSensorRecord> produce = results.parallelStream()
                .peek(record -> record.setNodeCode(warehouseCode + "-" + record.getNodeCode()))
                .peek(record -> record.setOrgCode(record.getOrgCode().replaceAll("－", "-").replace("- ", "-")))
                .flatMap(record -> {
                    BigDecimal temperature = record.getTemperature();
                    BigDecimal humidity = record.getHumidity();
                
                    String temStatus = SensorConsts.toStatus(temperature, record.getTemperatureHighLimit(), record.getTemperatureLowLimit());
                    String humStatus = SensorConsts.toStatus(humidity, record.getHumidityHighLimit(), record.getHumidityLowLimit());
                    long timestamp = record.getRecordDateTime().getTime() / 1000;
                    WarehouseSensorRecord tem = new WarehouseSensorRecord();
                    tem.setTimestamp(timestamp)
                            .setValue(temperature)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(record.getOrgCode())
                                    .setSensorCode(record.getNodeCode())
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(temStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));
                
                    WarehouseSensorRecord hum = new WarehouseSensorRecord();
                    hum.setTimestamp(timestamp)
                            .setValue(humidity)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(record.getOrgCode())
                                    .setSensorCode(record.getNodeCode())
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(humStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));
                
                    return Stream.of(tem, hum);
                })
                .filter(record -> record.getTags() != null)
                .collect(Collectors.toList());
                
        produce
                .forEach(record -> {
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(warehouseCode,record.toPutBody());
                });
    
        if(incr && rdbIncrSyncOffset != null) {
            if (!results.isEmpty()) {
                results.parallelStream()
                        .mapToLong(ZDBXSensorRecordVO::getRecordId)
                        .max()
                        .ifPresent(currMaxId -> rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(currMaxId)));

            } else { // 数据库自增id回滚至0
                String minMaxId = JDBCUtil.getTableMinMaxId(url, user, password, table, "Id");
                String[] split = minMaxId.split(",");
                if (split.length == 2) {
                    long dbMax = Long.parseLong(split[1]);
                    if (rdbIncrSyncOffset.getLastId() > dbMax) {
                        rdbIncrSyncOffsetService.set(rdbIncrSyncOffset.setLastId(0L));
                    }
                }
            }
        }
        ps.close();
        
        return new CollectResult((long)results.size(),(long)produce.size());
    }
    
    private static final String DIM_SQL =
            "SELECT o.Code as regionCode, o.Name as regionName, \n" +
            "\tsn.NodeId as sensorCodeOrigin , sn.Name as sensorName,\n" +
            "\tsn.SensorType as sensorType,\n" +
            "\tsn.TemperatureHighLimit as temperatureHighLimit,sn.TemperatureLowLimit as temperatureLowLimit,\n" +
            "\tsn.HumidityHighLimit as humidityHighLimit,sn.HumidityLowLimit as humidityLowLimit\n" +
            "FROM SlaveNode sn \n" +
            "JOIN Organization o on sn.OrganizationId = o.Id";
    
    private CollectResult syncDim(Connection conn) throws SQLException {


        PreparedStatement ps = conn.prepareStatement(DIM_SQL);
        ResultSet rs = ps.executeQuery();

        List<ZDBXDimVO> dims = JDBCUtil.resultSetToBeanList(rs, ZDBXDimVO.class);
        long create = dims.parallelStream()
                .map(dim -> {
                    WarehouseSensor ws = new WarehouseSensor();
                    BeanUtils.copyProperties(dim, ws);
                    ws.setSensorType(dim.getSensorType().toString());
                    ws.setWarehouseCode(warehouseCode);
                    ws.setWarehouseName(warehouseName);
                    ws.setCorpCode(SensorConsts.CORP_ZDBX);
                    ws.setSensorCode(warehouseCode + "-" + ws.getSensorCodeOrigin());
                    ws.setRegionCode(ws.getRegionCode().replaceAll("－", "-").replace("- ", "-"));
                    if(StringUtils.isBlank(ws.getSensorName())) {
                        ws.setSensorName(ws.getSensorCodeOrigin());
                    }
                
                    return ws;
                })
                .filter(ws -> warehouseSensorService.createOrUpdate(ws))
                .count();
        ps.close();
        
        return new CollectResult((long)dims.size(),create);
    }

    private static final String overlimitHandlingSQL =
            "SELECT TOP 10 Id as id , RecordStartTime as startTime , RecordEndTime as endTime , " +
                    "CreateDate as handleTime, Principal as principal , Suggestion as suggestion , " +
                    "NodeIdArray as nodeIds " +
                    "FROM OverLimitHandling " +
                    "WHERE Id > ?";



    private CollectResult syncOverlimitHandling(Connection conn) throws SQLException {
        RdbIncrSyncOffset offset = rdbIncrSyncOffsetService.get(url, "OverLimitHandling");
        PreparedStatement ps = conn.prepareStatement(overlimitHandlingSQL);
        ps.setLong(1,offset.getLastId());
        ResultSet rs = ps.executeQuery();
        List<ZDBXOverLimitHandling> zdbxOverLimitHandlings = JDBCUtil.resultSetToBeanList(rs, ZDBXOverLimitHandling.class);
    
        long count = zdbxOverLimitHandlings.stream().flatMap(h -> {
            String[] ids = h.getNodeIds().split(",");
            return Stream.of(ids).map(nodeId -> {
            
                String sensorCode = warehouseCode + "-" + nodeId;
                WarehouseSensor sensor = warehouseSensorService.findBy(new WarehouseSensorQuery().setWarehouseCode(warehouseCode).setSensorCode(sensorCode)).stream().findFirst().orElse(null);
                if (sensor != null) {
                    OverLimitHandlingEvent event = new OverLimitHandlingEvent();
                    BeanUtils.copyProperties(h, event);
                    BeanUtils.copyProperties(sensor, event);
                    event.setRange(true);
                    event.setCorpCode(SensorConsts.CORP_ZDBX);
                    return event;
                }
                return null;
            }).filter(Objects::nonNull);
        }).peek(event -> {
            overLimitHandlingProducer.produce(warehouseCode, JSON.toJSONString(event));
        }).count();

        if (!zdbxOverLimitHandlings.isEmpty()) {
            long max = zdbxOverLimitHandlings.parallelStream().mapToLong(ZDBXOverLimitHandling::getId).max().orElse(0);

            offset.setLastId(max);
            rdbIncrSyncOffsetService.set(offset);
        }

        return new CollectResult((long) zdbxOverLimitHandlings.size(), count);
    }
}
