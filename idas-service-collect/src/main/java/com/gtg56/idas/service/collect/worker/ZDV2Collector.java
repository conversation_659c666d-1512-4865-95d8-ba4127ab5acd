package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SpringUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ZDV2Collector extends BaseCollector {

    private WarehouseSensorClient openTSDBClient; // 时序数据库客户端
    private String warehouseCode; // 仓库编码
    private String warehouseName; // 仓库名称
    private IWarehouseSensorService warehouseSensorService; // 维表配置信息
    private MQProducer sensorRecordProducer; // kafka生产者对象
    private MQProducer overLimitHandlingProducer; // 异常处理kafka生产者对象
    private String apiUrl;
    private String apiUsername;
    private String apiPassword;
    private String sessid;
    private int recycle; // 异常记录回溯天数，默认3天

    public ZDV2Collector(Properties properties) {
        super(properties);
        // 构造器初始化配置信息
        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer =
                SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);

        warehouseCode =
                activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName =
                activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        apiUrl = activeProperties().getProperty("com.gtg56.idas.service.collect.api.url");
        apiUsername = activeProperties().getProperty("com.gtg56.idas.service.collect.api.username");
        apiPassword = activeProperties().getProperty("com.gtg56.idas.service.collect.api.password");
        recycle =
                Integer.parseInt(
                        activeProperties()
                                .getProperty("com.gtg56.idas.service.collect.recycle", "3"));
    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {
        CollectResult ret = new CollectResult(0L, 0L);

        // 登录获取 sessid
        if (StringUtils.isBlank(sessid)) {
            login();
        }

        if (StringUtils.isBlank(sessid)) {
            log.error("Login failed, cannot get sessid.");
            return new CollectResult(0L, 0L);
        }

        // 获取设备实时信息
        List<JSONObject> devices = getDeviceBrief();
        if (devices != null && !devices.isEmpty()) {
            // 处理实时数据
            CollectResult produce = getWarehouseSensorRecords(devices);
            ret.incr(produce);
        }

        // 处理历史异常数据
        CollectResult alarmResult = getHistoryAlarmData();
        ret.incr(alarmResult);

        return ret;
    }

    private void login() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/login");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("username", Base64.getEncoder().encodeToString(apiUsername.getBytes()));
            json.put("pwd", Base64.getEncoder().encodeToString(apiPassword.getBytes()));

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);
                if (responseJson.getBoolean("success")) {
                    this.sessid = responseJson.getString("sessid");
                    log.info("Login successful, sessid: {}", this.sessid);
                } else {
                    log.error("Login failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error during login", e);
        }
    }

    private List<JSONObject> getDeviceBrief() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/device/brief");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("sessid", this.sessid);

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);

                if (responseJson.getBoolean("success")) {
                    JSONArray devlist = responseJson.getJSONArray("devlist");
                    return devlist.stream()
                            .map(obj -> (JSONObject) obj)
                            .collect(Collectors.toList());
                } else {
                    log.error("Get device brief failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error getting device brief", e);
            return null;
        }
    }

    private CollectResult getWarehouseSensorRecords(List<JSONObject> results) {
        List<WarehouseSensorRecord> produce =
                results.parallelStream()
                        .filter(record -> StringUtils.isNotBlank(record.getString("tm")))
                        .flatMap(this::transformRecord)
                        .filter(Objects::nonNull)
                        .filter(record -> record.getTags() != null)
                        .collect(Collectors.toList());

        produce.forEach(
                record -> {
                    log.info("{}", record.toPutBody());
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(warehouseCode, record.toPutBody());
                });
        return new CollectResult((long) results.size(), (long) produce.size());
    }

    private Stream<WarehouseSensorRecord> transformRecord(JSONObject record) {
        String sbdm = record.getString("tag");
        String cjsj = record.getString("tm");
        JSONArray channels = record.getJSONArray("channel");

        String wdz = null;
        String sdz = null;

        for (Object channelObj : channels) {
            JSONObject channel = (JSONObject) channelObj;
            int id = channel.getIntValue("id");
            if (id == 1) { // 温度
                wdz = channel.getString("data");
            } else if (id == 2) { // 湿度
                sdz = channel.getString("data");
            }
        }

        if ("ERR".equals(wdz) || "ERR".equals(sdz)) {
            return Stream.empty();
        }

        long time = 0l;
        try {
            time = DateUtil.ymdhms().parse(cjsj).getTime();
        } catch (ParseException e) {
            log.error("Parse time error", e);
            return Stream.empty();
        }

        String sensorCode = warehouseCode + "-" + sbdm;
        WarehouseSensor warehouseSensor =
                warehouseSensorService
                        .findBy(
                                new WarehouseSensorQuery()
                                        .setWarehouseCode(warehouseCode)
                                        .setSensorCode(sensorCode))
                        .stream()
                        .findFirst()
                        .orElse(null);

        if (warehouseSensor == null) {
            return Stream.empty();
        }

        WarehouseSensorRecord tem = null;
        WarehouseSensorRecord hum = null;

        BigDecimal temperature = null;
        BigDecimal humidity = null;

        if (wdz != null && !wdz.isEmpty() && !"null".equalsIgnoreCase(wdz) && !"-".equals(wdz)) {
            temperature = new BigDecimal(wdz);
        }

        if (sdz != null && !sdz.isEmpty() && !"null".equalsIgnoreCase(sdz) && !"-".equals(sdz)) {
            humidity = new BigDecimal(sdz);
        }

        if (temperature != null) {
            String temperatureStatus =
                    SensorConsts.toStatus(
                            temperature,
                            warehouseSensor.getTemperatureHighLimit(),
                            warehouseSensor.getTemperatureLowLimit());
            String temperatureNormal =
                    SensorConsts.STATUS_NORMAL.equals(temperatureStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            tem = new WarehouseSensorRecord();
            tem.setTimestamp(time / 1000)
                    .setValue(temperature)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temperatureStatus)
                                    .setNormal(temperatureNormal));
        }

        if (humidity != null) {
            String humidityStatus =
                    SensorConsts.toStatus(
                            humidity,
                            warehouseSensor.getHumidityHighLimit(),
                            warehouseSensor.getHumidityLowLimit());
            String humidityNormal =
                    SensorConsts.STATUS_NORMAL.equals(humidityStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            hum = new WarehouseSensorRecord();
            hum.setTimestamp(time / 1000)
                    .setValue(humidity)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humidityStatus)
                                    .setNormal(humidityNormal));
        }

        return Stream.of(tem, hum).filter(Objects::nonNull);
    }

    /** 获取历史异常数据，保存最近3天的异常报警信息，注意去重 统一按当前时间回溯最近N天的数据 */
    private CollectResult getHistoryAlarmData() {
        long count = 0L;

        // 统一按当前时间回溯前N天的数据
        Date endTime = new Date(); // 当前时间
        Date startTime =
                DateUtils.addDays(
                        DateUtils.truncate(endTime, Calendar.DATE), -recycle); // 回溯N天前的开始时间

        log.info(
                "开始处理异常数据，时间范围: {} 到 {}",
                DateUtil.ymdhms().format(startTime),
                DateUtil.ymdhms().format(endTime));

        // 获取设备列表
        List<JSONObject> devices = getDeviceBrief();
        if (devices == null || devices.isEmpty()) {
            return new CollectResult(count, count);
        }

        for (JSONObject device : devices) {
            String deviceTag = device.getString("tag");

            try {
                // 获取设备历史异常数据
                List<JSONObject> alarmData = getDeviceHistoryAlarm(deviceTag, startTime, endTime);

                if (alarmData == null || alarmData.isEmpty()) {
                    continue;
                }

                log.info("处理设备 {} 的异常数据，共 {} 条记录", deviceTag, alarmData.size());
                count += processAlarmData(alarmData, deviceTag);

            } catch (Exception e) {
                log.warn("Process alarm data error for device: " + deviceTag, e);
            }
        }

        log.info("异常数据处理完成，共处理 {} 条记录", count);
        return new CollectResult(count, count);
    }

    /** 获取设备历史异常数据 */
    private List<JSONObject> getDeviceHistoryAlarm(String deviceTag, Date fromTime, Date toTime) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/history/device");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("sessid", this.sessid);
            json.put("tag", deviceTag);
            json.put("from", DateUtil.ymdhm().format(fromTime));
            json.put("to", DateUtil.ymdhm().format(toTime));
            json.put("coll", 1); // 只获取报警数据

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);

                if (responseJson.getBoolean("success")) {
                    JSONArray results = responseJson.getJSONArray("results");
                    return results.stream()
                            .map(obj -> (JSONObject) obj)
                            .collect(Collectors.toList());
                } else {
                    log.error("Get device history alarm failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error getting device history alarm for device: " + deviceTag, e);
            return null;
        }
    }

    /** 处理异常数据，转换为OverLimitHandlingEvent并发送到Kafka */
    private long processAlarmData(List<JSONObject> alarmData, String deviceTag) {
        return alarmData.stream()
                .filter(r -> StringUtils.isNotBlank(r.getString("time")))
                .filter(r -> StringUtils.isNotBlank(r.getString("solve_time")))
                .filter(r -> StringUtils.isNotBlank(r.getString("solve")))
                .filter(r -> !"".equals(r.getString("solve")))
                .filter(Objects::nonNull)
                .map(
                        r -> {
                            String sensorCode = warehouseCode + "-" + deviceTag;

                            WarehouseSensor warehouseSensor =
                                    warehouseSensorService
                                            .findBy(
                                                    new WarehouseSensorQuery()
                                                            .setWarehouseCode(warehouseCode)
                                                            .setSensorCode(sensorCode))
                                            .stream()
                                            .findFirst()
                                            .orElse(null);

                            if (warehouseSensor != null) {
                                OverLimitHandlingEvent overLimitHandlingEvent =
                                        new OverLimitHandlingEvent();
                                overLimitHandlingEvent.setWarehouseCode(warehouseCode);
                                overLimitHandlingEvent.setRegionCode(
                                        warehouseSensor.getRegionCode());
                                overLimitHandlingEvent.setSensorCode(
                                        warehouseSensor.getSensorCode());

                                Date startTime;
                                try {
                                    startTime = DateUtil.ymdhm().parse(r.getString("time"));
                                } catch (ParseException e) {
                                    log.error("Parse alarm time error: {}", r.getString("time"), e);
                                    return null;
                                }

                                /*新版API的异常数据是精确到时间点的，不需要用endTime，但是需要 startTime，同时设置Range等于false*/
                                overLimitHandlingEvent.setStartTime(startTime);
                                overLimitHandlingEvent.setRange(false);
                                overLimitHandlingEvent.setPrincipal(r.getString("solve_user"));
                                overLimitHandlingEvent.setSuggestion(r.getString("solve"));

                                return overLimitHandlingEvent;
                            } else {
                                return null;
                            }
                        })
                .filter(Objects::nonNull)
                .peek(
                        event -> {
                            // 数据写入Kafka
                            log.info("{}", JSON.toJSONString(event));
                            overLimitHandlingProducer.produce(
                                    warehouseCode, JSON.toJSONString(event));
                        })
                .count();
    }
}
