package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SpringUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ZDV2Collector extends BaseCollector {

    private WarehouseSensorClient openTSDBClient; // 时序数据库客户端
    private String warehouseCode; // 仓库编码
    private String warehouseName; // 仓库名称
    private IWarehouseSensorService warehouseSensorService; // 维表配置信息
    private MQProducer sensorRecordProducer; // kafka生产者对象
    private MQProducer overLimitHandlingProducer; // 异常处理kafka生产者对象
    private IRdbIncrSyncOffsetService rdbIncrSyncOffsetService; // 增量同步偏移量服务
    private String apiUrl;
    private String apiUsername;
    private String apiPassword;
    private String sessid;
    private int recycle; // 异常记录回溯天数，默认3天
    private Map<String, Long> devicesTs; // 记录每个设备的当前时间戳

    public ZDV2Collector(Properties properties) {
        super(properties);
        // 构造器初始化配置信息
        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer =
                SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);

        warehouseCode =
                activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName =
                activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        apiUrl = activeProperties().getProperty("com.gtg56.idas.service.collect.api.url");
        apiUsername = activeProperties().getProperty("com.gtg56.idas.service.collect.api.username");
        apiPassword = activeProperties().getProperty("com.gtg56.idas.service.collect.api.password");
        recycle =
                Integer.parseInt(
                        activeProperties()
                                .getProperty("com.gtg56.idas.service.collect.recycle", "3"));
        devicesTs = new HashMap<>();
    }

    @Override
    public String collectorName() {
        return super.collectorName() + "-" + warehouseCode;
    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {
        CollectResult ret = new CollectResult(0L, 0L);

        // 登录获取 sessid
        if (StringUtils.isBlank(sessid)) {
            login();
        }

        if (StringUtils.isBlank(sessid)) {
            log.error("Login failed, cannot get sessid.");
            return new CollectResult(0L, 0L);
        }

        // 获取设备实时信息
        List<JSONObject> devices = getDeviceBrief();
        if (devices != null && !devices.isEmpty()) {
            // 处理实时数据
            CollectResult produce = getWarehouseSensorRecords(devices);
            ret.incr(produce);
        }

        // 处理历史异常数据
        CollectResult alarmResult = getHistoryAlarmData();
        ret.incr(alarmResult);

        return ret;
    }

    private void login() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/login");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("username", Base64.getEncoder().encodeToString(apiUsername.getBytes()));
            json.put("pwd", Base64.getEncoder().encodeToString(apiPassword.getBytes()));

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);
                if (responseJson.getBoolean("success")) {
                    this.sessid = responseJson.getString("sessid");
                    log.info("Login successful, sessid: {}", this.sessid);
                } else {
                    log.error("Login failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error during login", e);
        }
    }

    private List<JSONObject> getDeviceBrief() {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/device/brief");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("sessid", this.sessid);

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);

                if (responseJson.getBoolean("success")) {
                    JSONArray devlist = responseJson.getJSONArray("devlist");
                    return devlist.stream()
                            .map(obj -> (JSONObject) obj)
                            .collect(Collectors.toList());
                } else {
                    log.error("Get device brief failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error getting device brief", e);
            return null;
        }
    }

    private CollectResult getWarehouseSensorRecords(List<JSONObject> results) {
        List<WarehouseSensorRecord> produce =
                results.parallelStream()
                        .filter(record -> StringUtils.isNotBlank(record.getString("tm")))
                        .flatMap(this::transformRecord)
                        .filter(Objects::nonNull)
                        .filter(record -> record.getTags() != null)
                        .collect(Collectors.toList());

        produce.forEach(
                record -> {
                    log.info("{}", record.toPutBody());
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(warehouseCode, record.toPutBody());
                });
        return new CollectResult((long) results.size(), (long) produce.size());
    }

    private Stream<WarehouseSensorRecord> transformRecord(JSONObject record) {
        String sbdm = record.getString("tag");
        String cjsj = record.getString("tm");
        JSONArray channels = record.getJSONArray("channel");

        String wdz = null;
        String sdz = null;

        for (Object channelObj : channels) {
            JSONObject channel = (JSONObject) channelObj;
            int id = channel.getIntValue("id");
            if (id == 1) { // 温度
                wdz = channel.getString("data");
            } else if (id == 2) { // 湿度
                sdz = channel.getString("data");
            }
        }

        if ("ERR".equals(wdz) || "ERR".equals(sdz)) {
            return Stream.empty();
        }

        long time = 0l;
        try {
            time = DateUtil.ymdhms().parse(cjsj).getTime();
        } catch (ParseException e) {
            log.error("Parse time error", e);
            return Stream.empty();
        }

        String sensorCode = warehouseCode + "-" + sbdm;
        WarehouseSensor warehouseSensor =
                warehouseSensorService
                        .findBy(
                                new WarehouseSensorQuery()
                                        .setWarehouseCode(warehouseCode)
                                        .setSensorCode(sensorCode))
                        .stream()
                        .findFirst()
                        .orElse(null);

        if (warehouseSensor == null) {
            return Stream.empty();
        }

        WarehouseSensorRecord tem = null;
        WarehouseSensorRecord hum = null;

        BigDecimal temperature = null;
        BigDecimal humidity = null;

        if (wdz != null && !wdz.isEmpty() && !"null".equalsIgnoreCase(wdz) && !"-".equals(wdz)) {
            temperature = new BigDecimal(wdz);
        }

        if (sdz != null && !sdz.isEmpty() && !"null".equalsIgnoreCase(sdz) && !"-".equals(sdz)) {
            humidity = new BigDecimal(sdz);
        }

        if (temperature != null) {
            String temperatureStatus =
                    SensorConsts.toStatus(
                            temperature,
                            warehouseSensor.getTemperatureHighLimit(),
                            warehouseSensor.getTemperatureLowLimit());
            String temperatureNormal =
                    SensorConsts.STATUS_NORMAL.equals(temperatureStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            tem = new WarehouseSensorRecord();
            tem.setTimestamp(time / 1000)
                    .setValue(temperature)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temperatureStatus)
                                    .setNormal(temperatureNormal));
        }

        if (humidity != null) {
            String humidityStatus =
                    SensorConsts.toStatus(
                            humidity,
                            warehouseSensor.getHumidityHighLimit(),
                            warehouseSensor.getHumidityLowLimit());
            String humidityNormal =
                    SensorConsts.STATUS_NORMAL.equals(humidityStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            hum = new WarehouseSensorRecord();
            hum.setTimestamp(time / 1000)
                    .setValue(humidity)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humidityStatus)
                                    .setNormal(humidityNormal));
        }

        return Stream.of(tem, hum).filter(Objects::nonNull);
    }

    /**
     * 获取历史异常数据，保存最近3天的异常报警信息，注意去重 根据配置表rdb_incr_sync_offset中的Last_Id来确定是按指定时间来跑，还是按当天时间往前推N天重跑
     * 如果Last_Id=0表示按当前时间回溯前N天的数据
     */
    private CollectResult getHistoryAlarmData() {
        RdbIncrSyncOffset offset = rdbIncrSyncOffsetService.get(apiUrl, "ZDV2AlarmRecords");
        Long ts = offset.getLastId();
        Long lastts_min = 0L; // 当前周期内所有设备的最后处理时间最小时间
        Date startTime = null; // 起始时间

        long count = 0L;

        // 回溯前N天的数据
        if (0 == ts) {
            devicesTs.put("incrFlag", 0L);
            // 判断是否当天首次抽数，拿集合中的日期时间戳和当天的日期时间戳比较，不同表示是跨天了，首次抽取
            Long flag = devicesTs.get("recycleFlag");
            long flag1 = DateUtils.truncate(new Date(), Calendar.DATE).getTime(); // 当天的时间戳
            if (flag == null || flag != flag1) { // 跨天了，清空设备时间戳，日期标志，回溯前N天数据
                devicesTs.clear();
                devicesTs.put(
                        "recycleFlag", DateUtils.truncate(new Date(), Calendar.DATE).getTime());
                Date addDays =
                        DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), -recycle);
                startTime = addDays;
            } else {
                Date addDays =
                        DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), -recycle);
                startTime = addDays;
            }
        } else {
            startTime = new Date(ts); // 起始时间
            // 判断是否首次按时间戳跑数，首次需清空时间map
            Long flag = devicesTs.getOrDefault("incrFlag", 0L);
            if (flag == 0) {
                devicesTs.clear();
            } else {
                devicesTs.put("incrFlag", 1L);
            }
        }

        // 获取设备列表
        List<JSONObject> devices = getDeviceBrief();
        if (devices == null || devices.isEmpty()) {
            return new CollectResult(count, count);
        }

        List<String> deviceTimes = new ArrayList<>();

        for (JSONObject device : devices) {
            String deviceTag = device.getString("tag");
            Long aLong = devicesTs.get(deviceTag);

            try {
                // 获取设备历史异常数据
                List<JSONObject> alarmData =
                        getDeviceHistoryAlarm(
                                deviceTag, aLong != null ? new Date(aLong) : startTime, new Date());

                if (alarmData == null || alarmData.isEmpty()) {
                    continue;
                }

                log.info(
                        "===="
                                + deviceTag
                                + "\t"
                                + DateUtil.ymdhms()
                                        .format(aLong != null ? new Date(aLong) : startTime)
                                + "=====");

                // 获取周期内该设备的最大处理时间
                String maxTime =
                        alarmData.stream()
                                .filter(r -> StringUtils.isNotBlank(r.getString("time")))
                                .filter(r -> StringUtils.isNotBlank(r.getString("solve_time")))
                                .filter(r -> !"".equals(r.getString("solve")))
                                .filter(Objects::nonNull)
                                .map(r -> r.getString("time"))
                                .max(String::compareTo)
                                .orElse(null);

                if (null == maxTime) continue;

                Long lastts = 0L;
                try {
                    lastts = DateUtil.ymdhm().parse(maxTime).getTime();
                } catch (ParseException e) {
                    log.error("Parse time error: " + maxTime, e);
                    continue;
                }

                // 保留每个设备的最大时间
                deviceTimes.add(maxTime);
                devicesTs.put(deviceTag, lastts);

                log.info("Device {} alarm data: {}", deviceTag, alarmData.toString());

                count += processAlarmData(alarmData, deviceTag);

            } catch (Exception e) {
                log.warn("Process alarm data error for device: " + deviceTag, e);
            }
        }

        // 如果按指定时间戳跑数，把最后跑数时间写入数据库
        if (0 != ts) {
            // 保存时间戳，作为下次抽数的起始时间，取每个设备最后抽取时间最小的值
            String ss = deviceTimes.stream().min(String::compareTo).orElse(null);
            // 没取到数据 直接返回
            if (ss == null) return new CollectResult(count, count);

            try {
                lastts_min = DateUtil.ymdhm().parse(ss).getTime();
            } catch (ParseException e) {
                log.error("Parse min time error: " + ss, e);
            }

            offset.setLastId(lastts_min);
            rdbIncrSyncOffsetService.set(offset);
        }

        return new CollectResult(count, count);
    }

    /** 获取设备历史异常数据 */
    private List<JSONObject> getDeviceHistoryAlarm(String deviceTag, Date fromTime, Date toTime) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/history/device");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("sessid", this.sessid);
            json.put("tag", deviceTag);
            json.put("from", DateUtil.ymdhm().format(fromTime));
            json.put("to", DateUtil.ymdhm().format(toTime));
            json.put("coll", 1); // 只获取报警数据

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);

                if (responseJson.getBoolean("success")) {
                    JSONArray results = responseJson.getJSONArray("results");
                    return results.stream()
                            .map(obj -> (JSONObject) obj)
                            .collect(Collectors.toList());
                } else {
                    log.error("Get device history alarm failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error getting device history alarm for device: " + deviceTag, e);
            return null;
        }
    }

    /** 处理异常数据，转换为OverLimitHandlingEvent并发送到Kafka */
    private long processAlarmData(List<JSONObject> alarmData, String deviceTag) {
        return alarmData.stream()
                .filter(r -> StringUtils.isNotBlank(r.getString("time")))
                .filter(r -> StringUtils.isNotBlank(r.getString("solve_time")))
                .filter(r -> StringUtils.isNotBlank(r.getString("solve")))
                .filter(r -> !"".equals(r.getString("solve")))
                .filter(Objects::nonNull)
                .map(
                        r -> {
                            String sensorCode = warehouseCode + "-" + deviceTag;

                            WarehouseSensor warehouseSensor =
                                    warehouseSensorService
                                            .findBy(
                                                    new WarehouseSensorQuery()
                                                            .setWarehouseCode(warehouseCode)
                                                            .setSensorCode(sensorCode))
                                            .stream()
                                            .findFirst()
                                            .orElse(null);

                            if (warehouseSensor != null) {
                                OverLimitHandlingEvent overLimitHandlingEvent =
                                        new OverLimitHandlingEvent();
                                overLimitHandlingEvent.setWarehouseCode(warehouseCode);
                                overLimitHandlingEvent.setRegionCode(
                                        warehouseSensor.getRegionCode());
                                overLimitHandlingEvent.setSensorCode(
                                        warehouseSensor.getSensorCode());

                                Date startTime;
                                try {
                                    startTime = DateUtil.ymdhm().parse(r.getString("time"));
                                } catch (ParseException e) {
                                    log.error("Parse alarm time error: {}", r.getString("time"), e);
                                    return null;
                                }

                                /*新版API的异常数据是精确到时间点的，不需要用endTime，但是需要 startTime，同时设置Range等于false*/
                                overLimitHandlingEvent.setStartTime(startTime);
                                overLimitHandlingEvent.setRange(false);
                                overLimitHandlingEvent.setPrincipal(r.getString("solve_user"));
                                overLimitHandlingEvent.setSuggestion(r.getString("solve"));

                                return overLimitHandlingEvent;
                            } else {
                                return null;
                            }
                        })
                .filter(Objects::nonNull)
                .peek(
                        event -> {
                            // 数据写入Kafka
                            log.info("{}", JSON.toJSONString(event));
                            overLimitHandlingProducer.produce(
                                    warehouseCode, JSON.toJSONString(event));
                        })
                .count();
    }

    /** 手动补充指定日期的数据（包括正常和异常报警数据） */
    public CollectResult manualCollectData(String date, boolean includeAlarm) {
        CollectResult ret = new CollectResult(0L, 0L);

        // 登录获取 sessid
        if (StringUtils.isBlank(sessid)) {
            login();
        }

        if (StringUtils.isBlank(sessid)) {
            log.error("Login failed, cannot get sessid.");
            return new CollectResult(0L, 0L);
        }

        try {
            Date targetDate = DateUtil.ymd().parse(date);
            Date startTime = DateUtils.truncate(targetDate, Calendar.DATE);
            Date endTime = DateUtils.addDays(startTime, 1);

            // 获取设备列表
            List<JSONObject> devices = getDeviceBrief();
            if (devices == null || devices.isEmpty()) {
                return new CollectResult(0L, 0L);
            }

            for (JSONObject device : devices) {
                String deviceTag = device.getString("tag");

                // 获取正常数据
                List<JSONObject> normalData =
                        getDeviceHistoryData(deviceTag, startTime, endTime, 0);
                if (normalData != null && !normalData.isEmpty()) {
                    CollectResult normalResult = processHistoryData(normalData, deviceTag);
                    ret.incr(normalResult);
                }

                // 获取异常数据
                if (includeAlarm) {
                    List<JSONObject> alarmData =
                            getDeviceHistoryData(deviceTag, startTime, endTime, 1);
                    if (alarmData != null && !alarmData.isEmpty()) {
                        long alarmCount = processAlarmData(alarmData, deviceTag);
                        ret.incr(new CollectResult(alarmCount, alarmCount));
                    }
                }
            }

        } catch (ParseException e) {
            log.error("Parse date error: " + date, e);
        }

        return ret;
    }

    /** 获取设备历史数据（通用方法） */
    private List<JSONObject> getDeviceHistoryData(
            String deviceTag, Date fromTime, Date toTime, int coll) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl + "/history/device");
            httpPost.setHeader("Content-Type", "application/json");

            JSONObject json = new JSONObject();
            json.put("sessid", this.sessid);
            json.put("tag", deviceTag);
            json.put("from", DateUtil.ymdhm().format(fromTime));
            json.put("to", DateUtil.ymdhm().format(toTime));
            json.put("coll", coll); // 0：正常数据；1：报警数据

            StringEntity entity = new StringEntity(json.toString());
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity);
                JSONObject responseJson = JSON.parseObject(responseString);

                if (responseJson.getBoolean("success")) {
                    JSONArray results = responseJson.getJSONArray("results");
                    return results.stream()
                            .map(obj -> (JSONObject) obj)
                            .collect(Collectors.toList());
                } else {
                    log.error("Get device history data failed: {}", responseString);
                    // 如果认证失败，可能是sessid过期，清空sessid以便下次重试
                    if (responseJson.getJSONObject("error") != null
                            && responseJson.getJSONObject("error").getIntValue("code") == 101) {
                        this.sessid = null;
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("Error getting device history data for device: " + deviceTag, e);
            return null;
        }
    }

    /** 处理历史数据，转换为WarehouseSensorRecord */
    private CollectResult processHistoryData(List<JSONObject> historyData, String deviceTag) {
        List<WarehouseSensorRecord> produce =
                historyData.stream()
                        .filter(record -> StringUtils.isNotBlank(record.getString("time")))
                        .flatMap(record -> transformHistoryRecord(record, deviceTag))
                        .filter(Objects::nonNull)
                        .filter(record -> record.getTags() != null)
                        .collect(Collectors.toList());

        produce.forEach(
                record -> {
                    log.info("{}", record.toPutBody());
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(warehouseCode, record.toPutBody());
                });

        return new CollectResult((long) historyData.size(), (long) produce.size());
    }

    /** 转换历史记录数据 */
    private Stream<WarehouseSensorRecord> transformHistoryRecord(
            JSONObject record, String deviceTag) {
        String cjsj = record.getString("time");
        JSONObject dataMap = record.getJSONObject("data");

        String wdz = null;
        String sdz = null;

        if (dataMap != null) {
            wdz = dataMap.getString("1"); // 温度
            sdz = dataMap.getString("2"); // 湿度
        }

        if ("ERR".equals(wdz) || "ERR".equals(sdz)) {
            return Stream.empty();
        }

        long time = 0l;
        try {
            time = DateUtil.ymdhm().parse(cjsj).getTime();
        } catch (ParseException e) {
            log.error("Parse time error", e);
            return Stream.empty();
        }

        String sensorCode = warehouseCode + "-" + deviceTag;
        WarehouseSensor warehouseSensor =
                warehouseSensorService
                        .findBy(
                                new WarehouseSensorQuery()
                                        .setWarehouseCode(warehouseCode)
                                        .setSensorCode(sensorCode))
                        .stream()
                        .findFirst()
                        .orElse(null);

        if (warehouseSensor == null) {
            return Stream.empty();
        }

        WarehouseSensorRecord tem = null;
        WarehouseSensorRecord hum = null;

        BigDecimal temperature = null;
        BigDecimal humidity = null;

        if (wdz != null && !wdz.isEmpty() && !"null".equalsIgnoreCase(wdz) && !"-".equals(wdz)) {
            temperature = new BigDecimal(wdz);
        }

        if (sdz != null && !sdz.isEmpty() && !"null".equalsIgnoreCase(sdz) && !"-".equals(sdz)) {
            humidity = new BigDecimal(sdz);
        }

        if (temperature != null) {
            String temperatureStatus =
                    SensorConsts.toStatus(
                            temperature,
                            warehouseSensor.getTemperatureHighLimit(),
                            warehouseSensor.getTemperatureLowLimit());
            String temperatureNormal =
                    SensorConsts.STATUS_NORMAL.equals(temperatureStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            tem = new WarehouseSensorRecord();
            tem.setTimestamp(time / 1000)
                    .setValue(temperature)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temperatureStatus)
                                    .setNormal(temperatureNormal));
        }

        if (humidity != null) {
            String humidityStatus =
                    SensorConsts.toStatus(
                            humidity,
                            warehouseSensor.getHumidityHighLimit(),
                            warehouseSensor.getHumidityLowLimit());
            String humidityNormal =
                    SensorConsts.STATUS_NORMAL.equals(humidityStatus)
                            ? CommonConsts.TRUE
                            : CommonConsts.FALSE;
            hum = new WarehouseSensorRecord();
            hum.setTimestamp(time / 1000)
                    .setValue(humidity)
                    .setTags(
                            new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humidityStatus)
                                    .setNormal(humidityNormal));
        }

        return Stream.of(tem, hum).filter(Objects::nonNull);
    }
}
