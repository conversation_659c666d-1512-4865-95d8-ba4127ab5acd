package com.gtg56.idas.service.collect.worker;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.core.kafka.MQProducer;
import com.gtg56.idas.common.entity.jdbc.RdbIncrSyncOffset;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.common.entity.sensor.kbs.KBSDimVO;
import com.gtg56.idas.common.entity.sensor.kbs.KBSOverLimitHandling;
import com.gtg56.idas.common.entity.sensor.kbs.KBSSensorRecordVO;
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord;
import com.gtg56.idas.common.service.IRdbIncrSyncOffsetService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class KBSCollector extends BaseCollector {

    private WarehouseSensorClient openTSDBClient;
    private String warehouseCode;
    private String warehouseName;
    private IRdbIncrSyncOffsetService rdbIncrSyncOffsetService;
    private IWarehouseSensorService warehouseSensorService;
    private MQProducer sensorRecordProducer;
    private MQProducer overLimitHandlingProducer;
    private String url;
    private String user;
    private String password;
    private Integer dimRound;
    private String table;
    private boolean incr;


    public KBSCollector(Properties properties) {
        super(properties);

        openTSDBClient = SpringUtil.getBean(WarehouseSensorClient.class);
        rdbIncrSyncOffsetService = SpringUtil.getBean(IRdbIncrSyncOffsetService.class);
        warehouseSensorService = SpringUtil.getBean(IWarehouseSensorService.class);
        sensorRecordProducer = SpringUtil.getBean("sensorRecordProducer", MQProducer.class);
        overLimitHandlingProducer = SpringUtil.getBean("overLimitHandlingProducer", MQProducer.class);


        warehouseCode = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseCode");
        warehouseName = activeProperties().getProperty("com.gtg56.idas.service.collect.warehouseName");
        url = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.url");
        user = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.user");
        password = activeProperties().getProperty("com.gtg56.idas.service.collect.jdbc.password");
        dimRound = Integer.parseInt(activeProperties().getProperty("com.gtg56.idas.service.collect.dim.round"));

    }

    @Override
    protected boolean requireElection() {
        return true;
    }

    @Override
    protected CollectResult collectAction() {

        CollectResult ret = new CollectResult(0L,0L);

        //由于汕头仓是开仓时就可以开始抽数，因此不用抽历史数据，只需要抽取维表，实时表，超标处理措施表
        try(Connection conn = DriverManager.getConnection(url, user, password)) {

            // 采集维度数据
            if(currentCycle() % dimRound == 1) {
                CollectResult dimResult = syncDim(conn);
                ret.incr(dimResult);
            }

            // 采集实时数据
            CollectResult recordResult = syncSensorRecord(conn);
            ret.incr(recordResult);

            //采集超标处理措施数据
            CollectResult handlingResult = syncOverlimitHandling(conn);
            ret.incr(handlingResult);

        } catch (SQLException e) {
            log.error("query from KingBos database fail \n{}", ExceptionUtils.getStackTrace(e));
        }

        return ret;
    }


    private  static  String overlimitHandlingSQL ="    SELECT\n" +
                                        "    TOP 10 T.ID,\n" +
                                        "    T.ErrSource sensorCode,\n" +
                                        "    T.ErrMsg,\n" +
                                        "    T.CollectTime startTime,\n" +
                                        "    T.AffirmTime handleTime, \n" +
                                        "    T.armResult suggestion,\n" +
                                        "    T.armEditor principal\n" +
                                        "    FROM  ErrLog T\n" +
                                        "    WHERE ID > ?\n" +
                                        "    AND T.ErrSource IN (SELECT MONCODE FROM  Info_Mon)\n" +
                                        "   ORDER BY  ID  " ;

    private CollectResult syncOverlimitHandling(Connection conn) throws SQLException {

        RdbIncrSyncOffset offset = rdbIncrSyncOffsetService.get(url, "ErrLog");
        PreparedStatement ps = conn.prepareStatement(overlimitHandlingSQL);
        ps.setLong(1,offset.getLastId());
        ResultSet rs = ps.executeQuery();
        List<KBSOverLimitHandling> kbsOverLimitHandlings = JDBCUtil.resultSetToBeanList(rs, KBSOverLimitHandling.class);
        long count = kbsOverLimitHandlings.parallelStream().map(record -> {

            //查找设备维表是否有该设备
            String sensorCode = warehouseCode + "-" + record.getSensorCode();
            WarehouseSensor sensor = warehouseSensorService.findBy(new WarehouseSensorQuery().setWarehouseCode(warehouseCode).setSensorCode(sensorCode)).stream().findFirst().orElse(null);

            if (sensor != null) {
                OverLimitHandlingEvent event = new OverLimitHandlingEvent();
                BeanUtils.copyProperties(record, event);
                BeanUtils.copyProperties(sensor, event);
                event.setRange(false);
                return event;
            } else return null;
        }).filter(Objects::nonNull).peek(event -> {
            //数据写入Kafka
            overLimitHandlingProducer.produce(warehouseCode, JSON.toJSONString(event));
        }).count();

        if(count >0 ) {
            //记录本次处理的最大ID,并写回数据库
            long max = kbsOverLimitHandlings.parallelStream().mapToLong(KBSOverLimitHandling::getId).max().orElse(0);

            offset.setLastId(max);
            rdbIncrSyncOffsetService.set(offset);
        }
        ps.close();

        return new CollectResult((long) kbsOverLimitHandlings.size(),count);


    }


    //金博温控系统实时数据表每分钟会刷新一次，保留最新的温湿度数据，历史数据归档，每次全量拿最新的温湿度数据即可
    private static  final String ALL_SQL ="SELECT\n" +
                                    "\t'${orgCode}' orgCode,\n" +
                                    "\t'${orgName}' orgName,\n" +
                                    "\tMonCode nodeCode,\n" +
                                    "\tMonName nodeName,\n" +
                                    "\tReadTp temperature,\n" +
                                    "\tReadWp humidity,\n" +
                                    "\tTULimit temperatureHighLimit,\n" +
                                    "\tTLLimit temperatureLowLimit,\n" +
                                    "\tWULimit humidityHighLimit,\n" +
                                    "\tWLLimit humidityLowLimit,\n" +
                                    "\tLastTime recordDateTime\n" +
                                    "FROM\n" +
                                    "\tInfo_Mon";

    private CollectResult syncSensorRecord(Connection conn) throws SQLException {

       String sql = ALL_SQL.replace("${orgCode}", warehouseCode).replace("${orgName}", warehouseName);
        PreparedStatement ps = conn.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        List<KBSSensorRecordVO> records = JDBCUtil.resultSetToBeanList(rs, KBSSensorRecordVO.class);

        List<WarehouseSensorRecord> produce = records.parallelStream()
                .flatMap(record -> {
                    BigDecimal temperature = record.getTemperature();
                    BigDecimal humidity = record.getHumidity();
                    String temStatus = SensorConsts.toStatus(temperature, record.getTemperatureHighLimit(), record.getTemperatureLowLimit());
                    String humStatus = SensorConsts.toStatus(humidity, record.getHumidityHighLimit(), record.getHumidityLowLimit());
                    long timestamp = record.getRecordDateTime().getTime() / 1000;

                    //读取维表信息
                    String sensorCode = warehouseCode + "-" + record.getNodeCode();
                    WarehouseSensor warehouseSensor =
                            warehouseSensorService.findBy(
                                    new WarehouseSensorQuery()
                                            .setWarehouseCode(warehouseCode)
                                            .setSensorCode(sensorCode)
                            )
                                    .stream()
                                    .findFirst()
                                    .orElse(null);

                    //构建温度对象
                    WarehouseSensorRecord tem = new WarehouseSensorRecord();
                    tem.setTimestamp(timestamp)
                            .setValue(temperature)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_TEMPERATURE)
                                    .setStatus(temStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(temStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));


                    //构建湿度数据
                    WarehouseSensorRecord hum = new WarehouseSensorRecord();
                    hum.setTimestamp(timestamp)
                            .setValue(humidity)
                            .setTags(new WarehouseSensorRecord.Tags()
                                    .setWarehouseCode(warehouseCode)
                                    .setRegionCode(warehouseSensor.getRegionCode())
                                    .setSensorCode(warehouseSensor.getSensorCode())
                                    .setType(SensorConsts.TYPE_HUMIDITY)
                                    .setStatus(humStatus)
                                    .setNormal(SensorConsts.STATUS_NORMAL.equals(humStatus) ? CommonConsts.TRUE : CommonConsts.FALSE));
                    return Stream.of(tem, hum);
                }).filter(record -> record.getTags() != null)
                .collect(Collectors.toList());

        produce.forEach(record -> {
                    openTSDBClient.put(record);
                    sensorRecordProducer.produce(warehouseCode,record.toPutBody());
                });

        ps.close();

        return new CollectResult((long)records.size(),(long)produce.size());


    }

    private static final String DIM_SQL ="SELECT\n" +
                        "\tb.LineCode regionCode,\n" +
                        "\tb.LineName regionName,\n" +
                        "\ta.MonCode sensorCodeOrigin,\n" +
                        "\ta.MonName sensorName,\n" +
                        "\t3 sensorType,\n" +
                        "\ta.TULimit temperatureHighLimit,\n" +
                        "\ta.TLLimit temperatureLowLimit,\n" +
                        "\ta.WULimit humidityHighLimit,\n" +
                        "\ta.WLLimit humidityLowLimit\n" +
                        "FROM\n" +
                        "\tInfo_Mon a,\n" +
                        "\tInfo_Line b\n" +
                        "WHERE\n" +
                        "\ta.LineCode = b.LineCode";

    private CollectResult syncDim(Connection conn) throws SQLException {

        PreparedStatement ps = conn.prepareStatement(DIM_SQL);
        ResultSet rs = ps.executeQuery();
        List<KBSDimVO> dims = JDBCUtil.resultSetToBeanList(rs, KBSDimVO.class);

        long create = dims.parallelStream()
                .map(dim -> {
                            WarehouseSensor ws = new WarehouseSensor();
                            BeanUtils.copyProperties(dim, ws);
                            ws.setSensorType(dim.getSensorType().toString());
                            ws.setWarehouseCode(warehouseCode);
                            ws.setWarehouseName(warehouseName);
                            ws.setCorpCode(SensorConsts.CORP_KBS);
                            ws.setSensorCode(warehouseCode + "-" + ws.getSensorCodeOrigin());
                            ws.setRegionCode(ws.getRegionCode().replaceAll("－", "-").replace("- ", "-"));
                            if (StringUtils.isBlank(ws.getSensorName())) {
                                ws.setSensorName(ws.getSensorCodeOrigin());
                            }

                            return ws;
                        }
               //设备维度信息写入数据库
                ).filter(ws -> warehouseSensorService.createOrUpdate(ws))
                .count();

        ps.close();

        //统计处理的记录数
        return new CollectResult((long)dims.size(),create);


    }
}
