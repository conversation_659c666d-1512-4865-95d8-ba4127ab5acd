<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="idas-service-collect" version="1.0" architecture="query"/>
    <import resource="classpath:/spring/dubbo.xml"/>
    <import resource="classpath:/spring/redis.xml"/>
    <import resource="classpath:/spring/kafka.xml"/>

    <bean id="sensorRecordProducer" class="com.gtg56.idas.common.core.kafka.MQProducer">
        <constructor-arg name="prop" ref="kafkaProducerProperties"/>
        <constructor-arg name="topic" value="warehouse_sensor_record"/>
    </bean>

    <bean id="overLimitHandlingProducer" class="com.gtg56.idas.common.core.kafka.MQProducer">
        <constructor-arg name="prop" ref="kafkaProducerProperties"/>
        <constructor-arg name="topic" value="overlimit_handling"/>
    </bean>

    <bean id="tsdbClient" class="com.gtg56.idas.common.tool.http.WarehouseSensorClient">
        <constructor-arg name="baseAddress" value="${tsdb.address}"/>
    </bean>

    <bean id="zkClient" class="org.apache.curator.framework.CuratorFrameworkFactory" factory-method="newClient"
          init-method="start" destroy-method="close">
        <constructor-arg name="connectString" value="${zookeeper.address}"/>
        <constructor-arg name="connectionTimeoutMs" value="60000"/>
        <constructor-arg name="sessionTimeoutMs" value="60000"/>
        <constructor-arg name="retryPolicy">
            <bean class="org.apache.curator.retry.RetryNTimes">
                <constructor-arg name="n" value="10"/>
                <constructor-arg name="sleepMsBetweenRetries" value="5000"/>
            </bean>
        </constructor-arg>
    </bean>
</beans>