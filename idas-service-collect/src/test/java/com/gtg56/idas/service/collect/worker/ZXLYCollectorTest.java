package com.gtg56.idas.service.collect.worker;

import com.gtg56.idas.common.entity.sensor.zxly.ZXLYRealTempHum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ZXLYCollectorTest {

    @InjectMocks
    private ZXLYCollector collector;

    private Method isValidRecordMethod;
    private Method safeConvertToBigDecimalMethod;
    private Method isValidValueMethod;
    private Method validateTimestampMethod;
    private Method cleanTagValueMethod;
    private Method isEmptyOrWhitespaceMethod;

    @BeforeEach
    void setUp() throws Exception {
        // 使用反射获取私有方法进行测试
        isValidRecordMethod = ZXLYCollector.class.getDeclaredMethod("isValidRecord", ZXLYRealTempHum.class);
        isValidRecordMethod.setAccessible(true);

        safeConvertToBigDecimalMethod = ZXLYCollector.class.getDeclaredMethod("safeConvertToBigDecimal", Float.class);
        safeConvertToBigDecimalMethod.setAccessible(true);

        isValidValueMethod = ZXLYCollector.class.getDeclaredMethod("isValidValue", BigDecimal.class);
        isValidValueMethod.setAccessible(true);

        validateTimestampMethod = ZXLYCollector.class.getDeclaredMethod("validateTimestamp", Date.class);
        validateTimestampMethod.setAccessible(true);

        cleanTagValueMethod = ZXLYCollector.class.getDeclaredMethod("cleanTagValue", String.class);
        cleanTagValueMethod.setAccessible(true);

        isEmptyOrWhitespaceMethod = ZXLYCollector.class.getDeclaredMethod("isEmptyOrWhitespace", String.class);
        isEmptyOrWhitespaceMethod.setAccessible(true);
    }

    @Test
    void testIsValidRecord() throws Exception {
        // 测试null记录
        assertFalse((Boolean) isValidRecordMethod.invoke(collector, (ZXLYRealTempHum) null));

        // 测试缺少ID的记录
        ZXLYRealTempHum recordWithoutId = new ZXLYRealTempHum();
        recordWithoutId.setDt(new Date());
        recordWithoutId.setAreaCode("area1");
        recordWithoutId.setDeviceCode("device1");
        assertFalse((Boolean) isValidRecordMethod.invoke(collector, recordWithoutId));

        // 测试缺少日期的记录
        ZXLYRealTempHum recordWithoutDate = new ZXLYRealTempHum();
        recordWithoutDate.setId(1L);
        recordWithoutDate.setAreaCode("area1");
        recordWithoutDate.setDeviceCode("device1");
        assertFalse((Boolean) isValidRecordMethod.invoke(collector, recordWithoutDate));

        // 测试有效记录
        ZXLYRealTempHum validRecord = new ZXLYRealTempHum();
        validRecord.setId(1L);
        validRecord.setDt(new Date());
        validRecord.setAreaCode("area1");
        validRecord.setDeviceCode("device1");
        assertTrue((Boolean) isValidRecordMethod.invoke(collector, validRecord));
    }

    @Test
    void testSafeConvertToBigDecimal() throws Exception {
        // 测试null值
        assertNull(safeConvertToBigDecimalMethod.invoke(collector, (Float) null));

        // 测试NaN值
        assertNull(safeConvertToBigDecimalMethod.invoke(collector, Float.NaN));

        // 测试无穷大值
        assertNull(safeConvertToBigDecimalMethod.invoke(collector, Float.POSITIVE_INFINITY));
        assertNull(safeConvertToBigDecimalMethod.invoke(collector, Float.NEGATIVE_INFINITY));

        // 测试正常值
        BigDecimal result = (BigDecimal) safeConvertToBigDecimalMethod.invoke(collector, 25.5f);
        assertNotNull(result);
        assertEquals(25.5, result.doubleValue(), 0.001);
    }

    @Test
    void testIsValidValue() throws Exception {
        // 测试null值
        assertFalse((Boolean) isValidValueMethod.invoke(collector, (BigDecimal) null));

        // 测试超出范围的值
        assertFalse((Boolean) isValidValueMethod.invoke(collector, BigDecimal.valueOf(-100.0)));
        assertFalse((Boolean) isValidValueMethod.invoke(collector, BigDecimal.valueOf(200.0)));

        // 测试有效值
        assertTrue((Boolean) isValidValueMethod.invoke(collector, BigDecimal.valueOf(25.0)));
        assertTrue((Boolean) isValidValueMethod.invoke(collector, BigDecimal.valueOf(-10.0)));
        assertTrue((Boolean) isValidValueMethod.invoke(collector, BigDecimal.valueOf(100.0)));
    }

    @Test
    void testValidateTimestamp() throws Exception {
        // 测试null日期
        assertEquals(-1L, validateTimestampMethod.invoke(collector, (Date) null));

        // 测试当前时间
        Date now = new Date();
        long result = (Long) validateTimestampMethod.invoke(collector, now);
        assertTrue(result > 0);

        // 测试过去的时间
        Date pastDate = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 1天前
        result = (Long) validateTimestampMethod.invoke(collector, pastDate);
        assertTrue(result > 0);

        // 测试太久远的时间
        Date veryOldDate = new Date(System.currentTimeMillis() - 20L * 365 * 24 * 60 * 60 * 1000); // 20年前
        result = (Long) validateTimestampMethod.invoke(collector, veryOldDate);
        assertEquals(-1L, result);
    }

    @Test
    void testCleanTagValue() throws Exception {
        // 测试null值
        assertNull(cleanTagValueMethod.invoke(collector, (String) null));

        // 测试空字符串
        assertNull(cleanTagValueMethod.invoke(collector, ""));
        assertNull(cleanTagValueMethod.invoke(collector, "   "));

        // 测试包含空格的字符串
        assertEquals("test", cleanTagValueMethod.invoke(collector, " test "));
        assertEquals("testvalue", cleanTagValueMethod.invoke(collector, "test value"));

        // 测试正常字符串
        assertEquals("normalvalue", cleanTagValueMethod.invoke(collector, "normalvalue"));
    }

    @Test
    void testIsEmptyOrWhitespace() throws Exception {
        // 测试null值
        assertTrue((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, (String) null));

        // 测试空字符串
        assertTrue((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, ""));

        // 测试只包含空白字符的字符串
        assertTrue((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, "   "));
        assertTrue((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, "\t\n"));

        // 测试包含内容的字符串
        assertFalse((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, "test"));
        assertFalse((Boolean) isEmptyOrWhitespaceMethod.invoke(collector, " test "));
    }
}
