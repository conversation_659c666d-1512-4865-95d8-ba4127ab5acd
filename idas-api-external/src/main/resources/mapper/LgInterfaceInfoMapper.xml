<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtg56.idas.api.external.mapper.LgInterfaceInfoMapper">
    <resultMap id="BaseResultMap" type="com.gtg56.idas.common.entity.jdbc.LgInterfaceInfo">
        <id column="id" property="id"/>
        <result column="devTime" property="devTime"/>
    </resultMap>

    <select id="resCount" resultType="java.lang.Integer">
        SELECT  count(1) cnt
        FROM t_lg_interface_tem_hum;
    </select>



    <delete id = "clear_his">

         delete from t_lg_interface_tem_hum where
         devTime &lt; #{devTime}

    </delete>
</mapper>