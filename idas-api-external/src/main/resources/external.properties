spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
##端口号
server.port=8083
# 上传文件总的最大值
spring.servlet.multipart.max-request-size=100MB
# 单个文件的最大值
spring.servlet.multipart.max-file-size=100MB

swagger.base-path=/**
swagger.base-package=com.gtg56.idas

# 定时任务,每十分钟处理一次，每次清理三天前数据
 sue.spring.task.cron = 0 0/10 * * * ?

logging.level.com.gtg56.idas.api.external.mapper=debug

# 错误处理时间周期，每天定时执行一次,单位：天
com.gtg56.idas.api.external.service.impl.clearDay =3

