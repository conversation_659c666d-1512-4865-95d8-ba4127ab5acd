package com.gtg56.idas.api.external.service.impl;

import com.gtg56.idas.common.entity.jdbc.LgInterfaceInfo;
import com.gtg56.idas.api.external.mapper.LgInterfaceInfoMapper;
import com.gtg56.idas.api.external.service.ILgInterfaceInfoService;
import com.gtg56.idas.common.core.jdbc.AbstractService;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.time.Duration;
import java.util.Date;


@Slf4j
@Mapper
@Service("lgInterfaceInfoService")
public class LgInterfaceInfoServiceImpl
        extends AbstractService<LgInterfaceInfo, LgInterfaceInfoMapper>
        implements ILgInterfaceInfoService {

    @Value("${com.gtg56.idas.api.external.service.impl.clearDay}")
    private Integer clearDay;

    /*定时任务->每三天定时清理一次三天前的中间接口表数据*/
    @Scheduled(cron = "${sue.spring.task.cron}")
    public void clearhistoryInfo() throws ParseException {

        Date devTime = new Date();
        devTime.setTime(devTime.getTime() - Duration.ofDays(clearDay).toMillis());

        /*记录删除条目数
        * 1.得到当前条目数
        * 2.执行删除语句
        * 3.得到删除后的条目数
        * 4.记录删除条目数
        * */
        Integer beforeCount =   mapper.resCount();

        /* ------- 删除3天前的历史数据 ------*/
        mapper.clear_his(devTime);
        Integer afterCount = mapper.resCount();

        /*<----------记录删除条目日志-------->*/
        log.info("接口中间表数据删除,已执行，删除行：{}",(beforeCount - afterCount));

    }
}
