package com.gtg56.idas.api.external.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.api.external.service.ILgInterfaceInfoService;
import com.gtg56.idas.common.util.DateUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.gtg56.idas.common.entity.jdbc.LgInterfaceInfo;

import java.text.ParseException;
import java.util.Date;


@Slf4j
@Api(tags = "Platform:CollectController", description = "采集器")
@RestController
@RequestMapping(value = "/api/Temp", produces = {MediaType.APPLICATION_JSON_VALUE})
public class LuGeController {



    @Resource(name = "lgInterfaceInfoService")
    private ILgInterfaceInfoService lgInterfaceInfoService;


    @PostMapping("/post")
    public void post(@RequestParam String realtime, @RequestParam String warehouseCode,
                     HttpServletRequest request, HttpServletResponse response) {




        JSONObject json = JSONObject.parseObject(realtime);
        JSONArray lstRealTime = json.getJSONArray("LstRealTime");
        JSONArray listchannel = null;


        for (int i = 0; i < lstRealTime.size(); i++) {
            LgInterfaceInfo lginterface = new LgInterfaceInfo();
            JSONObject rowJs = lstRealTime.getJSONObject(i);

            lginterface.setWarehousecode(warehouseCode);//仓库
            lginterface.setAddress(rowJs.getString("Address"));
            lginterface.setSN(rowJs.getString("SN"));//设备

            String  strdevTime = rowJs.getString("DevTime");
            String  strpcTime = rowJs.getString("PCTime");

            if(strdevTime==null || strpcTime==null) continue;

            try {

            lginterface.setDevTime(strdevTime.contains("/")?DateUtil.tsdb().parse(strdevTime) : DateUtil.ymdhms().parse(strdevTime));//

            }catch (ParseException e)
            {
                e.printStackTrace();

            }

            try {

                lginterface.setPcTime(strpcTime.contains("/")?DateUtil.tsdb().parse(strpcTime) : DateUtil.ymdhms().parse(strpcTime));//

            }catch (ParseException e)
            {
                e.printStackTrace();

            }

            listchannel = rowJs.getJSONArray("ListChannel");
            lginterface.setOperTime(new Date());//操作时间

            for (int j = 0; j < listchannel.size(); j++) {

                JSONObject channlJs = listchannel.getJSONObject(j);
                lginterface.setChannelPort(channlJs.getString("ChannelPort"));
                lginterface.setValue(channlJs.getBigDecimal("Value"));
                lginterface.setUpper(channlJs.getBigDecimal("Upper"));
                lginterface.setLower(channlJs.getBigDecimal("Lower"));

                lginterface.setId(null);
                lgInterfaceInfoService.save(lginterface);
            }

        }

        log.info("{}: {} : {}","时间：" + new Date(), "仓库: "+ warehouseCode,"下载行数：" + (lstRealTime.size()*listchannel.size()));
 }

}
