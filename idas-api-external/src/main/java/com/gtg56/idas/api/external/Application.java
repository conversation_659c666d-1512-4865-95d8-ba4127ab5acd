package com.gtg56.idas.api.external;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import tk.mybatis.spring.annotation.MapperScan;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.gtg56.idas.api.external"
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@EnableScheduling
@MapperScan("com.gtg56.idas.api.external.mapper")
@PropertySource(value = {"classpath:/application.properties", "classpath:/external.properties"})
@ImportResource(locations = {"classpath:/externalBeans.xml"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class,args);
    }
}
