<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>idas-parent</artifactId>
        <groupId>com.gtg56</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../idas-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>idas-service-define</artifactId>
    <packaging>jar</packaging>

    <description>
        <![CDATA[
            这里是RPC方法接口集中定义pom
        ]]>
    </description>

    <dependencies>
        <dependency>
            <groupId>com.gtg56</groupId>
            <artifactId>idas-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


</project>