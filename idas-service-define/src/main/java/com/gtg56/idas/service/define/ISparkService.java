package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.entity.dto.SparkWorkloadNodeInstanceStatusDTO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/12/30.
 * Spark工作流节点驱动服务
 */
public interface ISparkService {
    
    /**
     * 启动工作流节点
     *
     * @param nodeName 工作流节点名称
     * @param nodeArgs 工作流节点参数
     * @param role     启动者角色（保留）
     * @return 工作流节点实例ID
     */
    String startNode(String nodeName, NodeArgsDTO nodeArgs, String role);
    
    /**
     * 启动工作流节点（cli）
     *
     * @param nodeName 工作流节点名称
     * @param args     工作流节点参数
     * @param role     启动者角色（保留）
     * @return 工作流节点实例ID
     */
    String startNodeByCli(String nodeName, String[] args, String role);
    
    /**
     * 查询工作流节点实例状态
     *
     * @param instanceId 工作流节点实例ID
     * @return 工作流节点实例状态
     */
    SparkWorkloadNodeInstanceStatusDTO getStatus(String instanceId);
}
