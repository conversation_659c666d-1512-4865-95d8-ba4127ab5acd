package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.entity.query.UserAuthQuery;

import java.util.List;
import java.util.Map;

public interface IAuthService {
    UserAuth create(<PERSON><PERSON><PERSON><PERSON> userAuth, UserDTO creator);

    UserAuth edit(UserAuth userAuth, UserDTO editor);

    List<UserAuth> find(UserAuthQuery query);

    boolean checkGNCApiInvoke(Map<String, String> headers);
}
