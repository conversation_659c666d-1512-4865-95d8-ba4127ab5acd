package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据完整性检测配置明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface ICheckConfigDetailService {

    /**
     *
     * @param dataCheckConfigDetail
     * @return
     */
    public DataCheckConfigDetail saveData(DataCheckConfigDetail dataCheckConfigDetail);

    /**
     *
     * @param dataCheckConfigDetail
     */
    public DataCheckConfigDetail updateData(DataCheckConfigDetail dataCheckConfigDetail);

    /**
    *
    * @param
    */
    public DataCheckConfigDetail getById(Long id);


    /**
    *
    * @param
    */
    public DataCheckConfigDetail removeById(Long id);

    /**
     *
     * @param
     */
    public PageObject<DataCheckConfigDetail> pageByMap(Map<String, Object> var1, PageObject var2);

    /**
     *
     * @param
     */
    public PageObject<DataCheckConfigDetail> pageByQuery(BaseQuery var1, PageObject var2);

    /**
     *
     * @param
     */
    public List<DataCheckConfigDetail> listByIds(String ids);

    /**
     * 通过 配置表ID 查询明细 dataCheckConfigId
     * @param dataCheckConfigId
     */
    public List<DataCheckConfigDetailDTO> findByConfigId(Long dataCheckConfigId);


    }
