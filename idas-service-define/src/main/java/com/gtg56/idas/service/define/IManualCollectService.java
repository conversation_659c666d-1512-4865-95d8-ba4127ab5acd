package com.gtg56.idas.service.define;

import com.gtg56.idas.api.facade.controller.platform.ManualCollectController.ManualCollectResult;

/**
 * 手动数据采集服务接口
 */
public interface IManualCollectService {
    
    /**
     * 手动补充指定日期的数据
     * 
     * @param warehouseCode 仓库编码
     * @param date 日期(格式: yyyy-MM-dd)
     * @param includeAlarm 是否包含异常报警数据
     * @return 采集结果
     */
    ManualCollectResult supplementData(String warehouseCode, String date, boolean includeAlarm);
}
