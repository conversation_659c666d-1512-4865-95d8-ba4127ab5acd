package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据完整性检测结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface ICheckResultService {

    /**
     *
     * @param dataCheckResult
     * @return
     */
    public DataCheckResult saveData(DataCheckResult dataCheckResult);

    /**
     *
     * @param dataCheckResult
     */
    public DataCheckResult updateData(DataCheckResult dataCheckResult);

    /**
    *
    * @param
    */
    public DataCheckResult getById(Long id);


    /**
    *
    * @param 
    */
    public DataCheckResult removeById(Long id);


    /**
     *
     * @param
     */
    public PageObject<DataCheckResult> pageByMap(Map<String, Object> var1, PageObject var2);

    /**
     *
     * @param
     */
    public PageObject<DataCheckResult> pageByQuery(BaseQuery var1, PageObject var2);

    /**
     *
     * @param
     */
    public List<DataCheckResult> listByIds(String ids);

    /**
     *  数据检查方法
     * @param dataCheckConfig
     */
    public List<DataCheckResult> doCheckData(DataCheckConfig dataCheckConfig);

    /**
     *  数据检查方法
     * @param dataCheckResult
     */
    public List<DataCheckResult> doCheckData(DataCheckResult dataCheckResult);
    }
