package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据完整性检测结果明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface ICheckResultDetailService {

    /**
     *
     * @param dataCheckResultDetail
     * @return
     */
    public DataCheckResultDetail saveData(DataCheckResultDetail dataCheckResultDetail);

    /**
     *
     * @param
     */
    public DataCheckResultDetail updateData(DataCheckResultDetail dataCheckResultDetail);

    /**
    *
    * @param
    */
    public DataCheckResultDetail getById(Long id);


    /**
    *
    * @param
    */
    public DataCheckResultDetail removeById(Long id);

    /**
     *
     * @param
     */
    public PageObject<DataCheckResultDetail> pageByMap(Map<String, Object> var1, PageObject var2);

    /**
     *
     * @param
     */
    public PageObject<DataCheckResultDetail> pageByQuery(BaseQuery var1, PageObject var2);

    /**
     *
     * @param
     */
    public List<DataCheckResultDetail> listByIds(String ids);

    /**
     * 查找指定的DataCheckResultDetailDTO
     * @param dataCheckConfigDetailId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<DataCheckResultDetail> findDataCheckResultDetailList(Long dataCheckConfigDetailId,String startDate,String endDate);

    }
