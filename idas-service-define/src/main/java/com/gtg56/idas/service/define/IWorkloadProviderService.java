package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.NodeProvideDTO;

import java.util.List;

/**
 * 节点定义提供服务
 */
public interface IWorkloadProviderService {
    
    /**
     * 提供节点定义
     *
     * @param name 节点名
     * @return NodeProvideDTO
     */
    NodeProvideDTO provideNode(String name);
    
    /**
     * 列举提供中的节点定义
     *
     * @return 节点名
     */
    List<String> listProviding();
}
