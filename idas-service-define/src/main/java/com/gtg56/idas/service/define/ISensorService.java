package com.gtg56.idas.service.define;

import com.github.pagehelper.PageInfo;
import com.gtg56.idas.common.entity.dto.FDAReportDTO;
import com.gtg56.idas.common.entity.dto.sensor.*;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.*;

import java.util.List;

/**
 * 温湿度传感器服务
 */
public interface ISensorService {
    /**
     * 列举仓库
     *
     * @return WarehouseDTO
     */
    List<WarehouseDTO> listWarehouse();
    
    /**
     * 列举传感器
     *
     * @param query 查询
     * @return WarehouseSensor
     */
    List<WarehouseSensor> findSensor(WarehouseSensorQuery query);
    
    /**
     * 获取传感器最新数据（2分钟内最新一条）
     *
     * @param query 查询
     * @return SensorResultDTO
     */
    SensorResultDTO listSensorLastRecord(WarehouseSensorLastQuery query);
    
    /**
     * 获取传感器历史数据
     *
     * @param query 查询
     * @return SensorResultDTO
     */
    SensorResultDTO listSensorHistoryRecord(WarehouseSensorHistoryQuery query);
    
    /**
     * 获取超标处理记录
     *
     * @param query 查询
     * @return OverLimitHandlingDTO
     */
    List<OverLimitHandlingDTO> listOverlimitHandling(WarehouseOverlimitHandlingQuery query);
    
    /**
     * 获取原始数据
     *
     * @param query 查询
     * @return SensorRecordDTO
     */
    List<SensorRecordDTO> listRaw(WarehouseSensorRawQuery query);

    SensorStatResultDTO stat(WarehouseStatQuery query);

    PageInfo<FDAReportDTO> listFDAReportPage(FDAReportQuery query);

    WarehouseSensor edit(WarehouseSensor sensor);
}
