package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据完整性检测配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface ICheckConfigService {

    /**
     *
     * @param dataCheckConfig
     * @return
     */
    public DataCheckConfig saveData(DataCheckConfig dataCheckConfig);

    /**
     *
     * @param dataCheckConfig
     */
    public DataCheckConfig updateData(DataCheckConfig dataCheckConfig);

    /**
     *
     * @param
     */
    public DataCheckConfig getById(Long id);

    /**
     *
     * @param
     */

    /**
     *
     * @param
     */
    public DataCheckConfig removeById(Long id);

    /**
     *
     * @param
     */
    public PageObject<DataCheckConfig> pageByMap(Map<String, Object> var1, PageObject var2);

    /**
     *
     * @param
     */
    public PageObject<DataCheckConfig> pageByQuery(BaseQuery var1, PageObject var2);

    /**
     *
     * @param
     */
    public List<DataCheckConfig> listByIds(String ids);

    /**
     *
     * @param
     */
    public List<DataCheckConfig> findAll();

    /**
     * 更新状态
     * @param id
     * @param status
     * @return
     */
    public DataCheckConfig updateStatus(String id,String status);
}
