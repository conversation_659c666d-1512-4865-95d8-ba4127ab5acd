package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户数据角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface IRoleUserService {

    /**
     *
     * @param dataRoleUser
     * @return
     */
    public DataRoleUser saveData(DataRoleUser dataRoleUser);

    /**
     *
     * @param dataRoleUser
     */
    public DataRoleUser updateData(DataRoleUser dataRoleUser);

    /**
     * 通过用户ID查询数据角色列表
     * @param userId
     * @return
     */
    public List<DataRoleUserDTO> listVOByUserId(String userId);

    /**
     * 通过用户ID删除数据角色用户数据
     * @param userId
     * @return
     */
    public Boolean deleteByUserId(String userId);

    PageObject<DataRoleUser> pageByMap(Map<String, Object> var1, PageObject var2);

    public PageObject<DataRoleUser> pageByQuery(BaseQuery var1, PageObject var2);

    public List<DataRoleUser> listByIds(Collection<? extends Serializable> var1);

    public DataRoleUser getById(Long id);

    public Boolean removeById(Long id);
}
