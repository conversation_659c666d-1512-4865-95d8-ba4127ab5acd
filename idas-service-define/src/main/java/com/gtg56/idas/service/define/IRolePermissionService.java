package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据角色权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IRolePermissionService {

    /**
     *
     * @param dataRolePermission
     * @return
     */
    public DataRolePermission saveData(DataRolePermission dataRolePermission);

    /**
     *
     * @param dataRoleId
     * @param dataPermissionmVOList
     * @return
     */
    public Boolean saveData(String dataRoleId, List<DataPermissionDTO> dataPermissionmVOList);

    /**
     * 删除数据角色ID相关的关系
     * @param dataRoleId
     * @return
     */
    public Boolean deleteDataByRoleId(String dataRoleId);

    /**
     *
     * @param dataRolePermission
     */
    public DataRolePermission updateData(DataRolePermission dataRolePermission);

    public PageObject<DataRolePermission> pageByMap(Map<String, Object> var1, PageObject var2);


    public PageObject<DataRolePermission> pageByQuery(BaseQuery var1, PageObject var2);

    public List<DataRolePermission> listByIds(Collection<? extends Serializable> var1);

    public DataRolePermission getById(Long id);

    public Boolean removeById(Long id);
}
