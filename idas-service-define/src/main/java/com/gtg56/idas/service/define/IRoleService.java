package com.gtg56.idas.service.define;

import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据角色 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface IRoleService {

    /**
     *
     * @param dataRole
     * @return
     */
    public DataRole saveData(DataRole dataRole);

    /**
     *
     * @param dataRole
     */
    public DataRole updateData(DataRole dataRole);

    PageObject<DataRole> pageByMap(Map<String, Object> var1, PageObject var2);

    public PageObject<DataRole> pageByQuery(BaseQuery var1, PageObject var2);

    public List<DataRole> listByIds(Collection<? extends Serializable> var1);

    public DataRole getById(Long id);

    public Boolean removeById(Long id);

    /**
     * 根据Code获取数据角色
     * @PARAM CODE
     * @RETURN
     */
    public DataRole findByCode(String code);
}
