package com.gtg56.idas.service.auth;

import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;
import com.gtg56.idas.common.mapper.DataRoleUserMapper;
import com.gtg56.idas.common.service.IDataRoleUserService;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.IRoleUserService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户数据角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@DubboService(
        interfaceClass = IRoleUserService.class, protocol = "dubbo", registry = "zookeeper",
        filter = "-exception"
)
public class RoleUserService  implements IRoleUserService {

    @Resource(name = "dataRoleUserService")
    private IDataRoleUserService dataRoleUserService;

    /**
     * @param dataRoleUser
     * @return
     */
    @Override
    public DataRoleUser saveData(DataRoleUser dataRoleUser) {
        return dataRoleUserService.saveData(dataRoleUser);
    }

    /**
     * @param dataRoleUser
     */
    @Override
    public DataRoleUser updateData(DataRoleUser dataRoleUser) {
        return dataRoleUserService.updateData(dataRoleUser);
    }

    @Override
    public List<DataRoleUserDTO> listVOByUserId(String userId) {
        return dataRoleUserService.listVOByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByUserId(String userId) {

        return dataRoleUserService.deleteByUserId(userId);
    }

    @Override
    public PageObject<DataRoleUser> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataRoleUserService.pageByMap(var1, var2);
    }

    @Override
    public PageObject<DataRoleUser> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataRoleUserService.pageByQuery(var1, var2);
    }

    @Override
    public List<DataRoleUser> listByIds(Collection<? extends Serializable> var1) {
        String ids = var1.stream().map(Object::toString).collect(Collectors.joining(","));
        return dataRoleUserService.findByIds(ids);
    }

    @Override
    public DataRoleUser getById(Long id) {
        return dataRoleUserService.findById(id);
    }

    @Override
    public Boolean removeById(Long id) {
        Boolean flag = false;
        dataRoleUserService.deleteById(id);
        flag = true;
        return flag;
    }
}
