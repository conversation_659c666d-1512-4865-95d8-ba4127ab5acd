package com.gtg56.idas.service.auth;

import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.mapper.DataRolePermissionMapper;
import com.gtg56.idas.common.service.IDataPermissionService;
import com.gtg56.idas.common.service.IDataRolePermissionService;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.IRolePermissionService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据角色权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@DubboService(
        interfaceClass = IRolePermissionService.class, protocol = "dubbo", registry = "zookeeper",
        filter = "-exception"
)
public class RolePermissionService implements IRolePermissionService {

    @Resource(name = "dataRolePermissionService")
    private IDataRolePermissionService dataRolePermissionService;

    /**
     * @param dataRolePermission
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRolePermission saveData(DataRolePermission dataRolePermission) {
        return dataRolePermissionService.saveData(dataRolePermission);
    }

    @Override
    public Boolean saveData(String dataRoleId, List<DataPermissionDTO> dataPermissionDTOList) {

        return dataRolePermissionService.saveData(dataRoleId, dataPermissionDTOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDataByRoleId(String dataRoleId) {

        return dataRolePermissionService.deleteDataByRoleId(dataRoleId);
    }


    /**
     * @param dataRolePermission
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataRolePermission updateData(DataRolePermission dataRolePermission) {
        return dataRolePermissionService.updateData(dataRolePermission);
    }

    @Override
    public PageObject<DataRolePermission> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataRolePermissionService.pageByMap(var1,var2);
    }


    @Override
    public PageObject<DataRolePermission> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataRolePermissionService.pageByQuery(var1, var2);
    }

    @Override
    public List<DataRolePermission> listByIds(Collection<? extends Serializable> var1) {
        String ids = var1.stream().map(Object::toString).collect(Collectors.joining(","));
        return dataRolePermissionService.findByIds(ids);
    }

    @Override
    public DataRolePermission getById(Long id) {
         return dataRolePermissionService.findById(id);
    }

    @Override
    public Boolean removeById(Long id) {
        Boolean flag = false;
        dataRolePermissionService.deleteById(id);
        flag = true;
        return flag;
    }
}
