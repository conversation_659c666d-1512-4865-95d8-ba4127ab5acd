package com.gtg56.idas.service.auth;

import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.service.IDataRoleService;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.IRoleService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@DubboService(
        interfaceClass = IRoleService.class, protocol = "dubbo", registry = "zookeeper",
        filter = "-exception"
)
public class RoleService  implements com.gtg56.idas.service.define.IRoleService {

    @Resource(name = "dataRoleService")
    private IDataRoleService dateRoleService;

    /**
     * @param dataRole
     * @return
     */
    @Override
    public DataRole saveData(DataRole dataRole) {
        return dateRoleService.saveData(dataRole);
    }

    /**
     * @param dataRole
     */
    @Override
    public DataRole updateData(DataRole dataRole) {
        return dateRoleService.updateData(dataRole);
    }

    @Override
    public PageObject<DataRole> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dateRoleService.pageByMap(var1, var2);
    }

    @Override
    public PageObject<DataRole> pageByQuery(BaseQuery var1, PageObject var2) {
        return dateRoleService.pageByQuery(var1, var2);
    }

    @Override
    public List<DataRole> listByIds(Collection<? extends Serializable> var1) {

        String ids = var1.stream().map(Object::toString).collect(Collectors.joining(","));
        return dateRoleService.findByIds(ids);
    }


    @Override
    public DataRole getById(Long id) {
        return dateRoleService.findById((Long) id);
    }

    @Override
    public Boolean removeById(Long id) {
        Boolean flag = false;
        dateRoleService.deleteById(id);
        flag = true;
        return flag;
    }

    @Override
    public DataRole findByCode(String code) {
        return dateRoleService.findByCode(code);
    }


}
