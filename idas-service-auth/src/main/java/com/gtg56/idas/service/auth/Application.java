package com.gtg56.idas.service.auth;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import tk.mybatis.spring.annotation.MapperScan;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.gtg56.idas.common.service"
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@MapperScan("com.gtg56.idas.common.mapper")
@DubboComponentScan
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(locations = {"classpath:/authBeans.xml"})
public class Application {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
    }
}
