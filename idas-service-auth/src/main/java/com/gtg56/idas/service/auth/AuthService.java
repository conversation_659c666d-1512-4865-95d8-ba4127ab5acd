package com.gtg56.idas.service.auth;

import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.entity.query.UserAuthQuery;
import com.gtg56.idas.common.service.IGncAppKeyService;
import com.gtg56.idas.common.service.IUserAuthService;
import com.gtg56.idas.common.util.AssertUtil;
import com.gtg56.idas.service.define.IAuthService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@DubboService(
        interfaceClass = IAuthService.class, protocol = "dubbo", registry = "zookeeper",
        filter = "-exception"
)
public class AuthService implements IAuthService {

    @Resource(name = "userAuthService")
    private IUserAuthService userAuthService;

    @Resource(name = "gncAppKeyService")
    private IGncAppKeyService gncAppKeyService;

    @Override
    public UserAuth create(UserAuth userAuth, UserDTO creator) {
        userAuth.baseSet(creator);

        userAuthService.save(userAuth);

        return userAuth;
    }
    
    @Override
    public UserAuth edit(UserAuth userAuth, UserDTO editor) {
        userAuth.baseSet(editor);
        
        userAuthService.update(userAuth);

        return userAuth;
    }

    @Override
    public List<UserAuth> find(UserAuthQuery query) {
        AssertUtil.isNotBlank(query.getAuthRealm(), "授权域不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(query.getUserId()) || StringUtils.isNotBlank(query.getUsername()), "未指定用户");

        return userAuthService.find(query);
    }

    @Override
    public boolean checkGNCApiInvoke(Map<String, String> headers) {
        String key = headers.get("app-key");
        String projectName = headers.get("project-name");

        if (!"idas".equals(projectName)) return false;
        return gncAppKeyService.hasKey(key);
    }
}
