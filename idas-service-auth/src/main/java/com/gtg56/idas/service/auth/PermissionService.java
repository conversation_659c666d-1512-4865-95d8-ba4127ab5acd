package com.gtg56.idas.service.auth;

import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.mapper.DataPermissionMapper;
import com.gtg56.idas.common.service.IDataPermissionService;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.IPermissionService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@DubboService(
        interfaceClass = IPermissionService.class, protocol = "dubbo", registry = "zookeeper",
        filter = "-exception"
)
public class PermissionService implements IPermissionService {

    @Resource(name = "dataPermissionService")
    private IDataPermissionService dataPermissionService;

    @Override
    public DataPermission saveData(DataPermission dataPermission) {
        return dataPermissionService.saveData(dataPermission);
    }

    @Override
    public DataPermission updateData(DataPermission dataPermission) {
        return dataPermissionService.updateData(dataPermission);
    }

    @Override
    public List<DataPermissionDTO> listVOByRoleId(String roleId) {
        return dataPermissionService.listVOByRoleId(roleId);
    }

    @Override
    public List<DataPermissionDTO> listVOByUserId(String userId) {

        return dataPermissionService.listVOByUserId(userId);
    }

    @Override
    public List<DataPermissionDTO> listVOByType(String type) {
        return dataPermissionService.listVOByType(type);
    }

    @Override
    public Boolean refreshAndCreate() {
        return dataPermissionService.refreshAndCreate();
    }

    @Override
    public PageObject<DataPermission> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataPermissionService.pageByMap(var1, var2);
    }

    @Override
    public PageObject<DataPermission> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataPermissionService.pageByQuery(var1, var2);
    }

    @Override
    public List<DataPermission> listByIds(Collection<? extends Serializable> var1) {
        String ids = var1.stream().map(Object::toString).collect(Collectors.joining(","));
        return dataPermissionService.findByIds(ids);
    }

    @Override
    public DataPermission getById(Long id) {
        return dataPermissionService.findById(id);
    }

    @Override
    public Boolean removeById(Long id) {
        Boolean flag = false;
        dataPermissionService.deleteById(id);
        flag = true;
        return flag;
    }

    @Override
    public List<DataPermissionDTO> listVOByCodeList(List<DataPermissionDTO> codeList) {
        return dataPermissionService.listVOByCodeList(codeList);
    }
}
