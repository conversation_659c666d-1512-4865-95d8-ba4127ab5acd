# 数据监测系统接口文档

(版本：V1.0)

# 目录

[1、 概述 3](#_Toc204001861)

[1.1目的范围 3](#_Toc204001862)

[2、 接口规范 3](#_Toc204001863)

[2.1 协议约定 3](#_Toc204001864)

[2.2 接口地址 3](#_Toc204001865)

[2.3 接口说明 3](#_Toc204001866)

[2.3.1 获取授权码 3](#_Toc204001867)

[2.3.2 获取设备实时信息 4](#_Toc204001868)

[2.3.3 获取设备历史数据 5](#_Toc204001869)

[3、 接口响应示例 6](#_Toc204001870)

[3.1 失败响应格式 6](#_Toc204001871)

[3.2 示例 7](#_Toc204001872)

[4、 错误代码 8](#_Toc204001873)

[5、 数据字典 8](#_Toc204001874)

[5.1 通道类型 8](#_Toc204001875)

# 概述

## 1.1目的范围

本接口文档为三方平台从数据监测系统获取数据时使用到的接口文档。

# 接口规范

## 协议约定

|     |     |     |
| --- | --- | --- |
| 接口方式 | http POST | Content-Type: application/json |
| 编码格式 | UTF-8 |     |
| 传输格式 | JSON |     |

## 接口地址

http://服务地址/路由地址

## 接口说明

### 获取授权码

路由地址：/login

描述：根据用户登录信息获取授权码，其他所有功能接口都需要使用该授权码方可正常使用对应功能。授权码保持使用可长期有效。

请求参数：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数项 | 数据类型 | 是否必填 | 参数说明 |
| username | String | 是   | 用户名(需base64加密) |
| pwd | String | 是   | 密码(需base64加密) |

返回字段：

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| success | boolean | 响应结果：true-成功，false-失败 |
| sessid | String | 授权码 |

### 获取设备实时信息

路由地址：/device/brief

请求参数：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数项 | 数据类型 | 是否必填 | 参数说明 |
| sessid | String | 是   | 授权码（由2.3.1接口获取） |

返回字段：

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| success | boolean | 响应结果：true-成功，false-失败 |
| devlist | Json\[\] | **数据集合** |

其中数据集中的每项数据格式如下

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| tag | String | 设备标识符 |
| name | String | 设备名称 |
| tm  | String | 实时数据时间，无数据时为空<br><br>格式：yyyy-MM-dd hh:mm:ss |
| channel | Json\[\] | **通道数据数组** |

通道数据数组的每项数据格式如下

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| id  | int | 通道id |
| custom | String | 通道名称 |
| data | String | 通道数据字符串<br><br>“ERR”表示通讯故障 |
| warn | int | 报警状态<br><br>0：未报警；1：报警；2：预报警 |

### 获取设备历史数据

路由地址：/history/device

请求参数：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数项 | 数据类型 |     | 是否必填 | 参数说明 |
| sessid | String |     | 是   | 授权码（由2.3.1接口获取） |
| tag | String |     | 是   | 设备标识符 |
| from | String | 否   |     | 开始时间，默认获取最近两小时数据<br><br>格式：yyyy-MM-dd hh:mm |
| to  | String | 否   |     | 结束时间<br><br>格式：yyyy-MM-dd hh:mm |
| coll | Int |     | 否   | 数据状态<br><br>默认：全部；0：正常数据；1：报警数据 |

**PS：** \* from和to同时传参才有效，否则返回最近两小时的数据

\* 考虑到大数据量的情况，建议请求参数from、to的时间跨度十天左右

返回字段：

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| success | boolean | 响应结果：true-成功，false-失败 |
| results | Json\[\] | **历史数据集合** |

其中数据集中的每项数据格式如下

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| time | String | 数据时间<br><br>格式：yyyy-MM-dd hh:mm |
| data | Json | 通道数据map - 通道id:数据展示字符串 |
| solve | String | 报警处理详情 |
| solve_user | String | 报警处理人 |
| solve_time | String | 报警处理时间<br><br>格式：yyyy-MM-dd hh:mm:ss |
| reason | String | 数据状态描述 |

# 接口响应示例

## 失败响应格式

|     |     |     |
| --- | --- | --- |
| 消息项 | 数据类型 | 消息说明 |
| success | Boolean | false |
| error | Json | **错误详细**（仅响应结果为失败时有效） |

其中错误详细格式如下

|     |     |     |
| --- | --- | --- |
| 参数项 | 数据类型 | 参数说明 |
| msg | String | 错误描述 |
| key | String | 错误对象关键字 |
| code | Number | 错误代码 |

## 示例

**失败**示例：

{

"success": false,

"error": {

"code": 5

"msg": "未授权"

}

}

**成功**示例：

\* /device/brief

{

"success": true,

"devlist": \[

{

"tag": "9F6560001",

"name": "送风机组1",

"tm": "2025-07-21 13:46:59",

"channel": \[

{ "id":1, "custom":"温度", "data":"25.9","warn":0},

{ "id":2, "custom":"湿度", "data":"73.7","warn":0}

\]

},

{

"tag": "9F6560002",

"name": "送风机组2",

"tm": "2025-07-21 13:46:59",

"channel": \[

{ "id":1, "custom":"温度", "data":"25.9","warn":0},

{ "id":2, "custom":"湿度", "data":"73.7","warn":0}

\]

}

\]

}

\* /history/device

{

"success": true,

"results": \[

{

"time": "2025-07-21 13:46",

"data": \[

{ "1":"25.9" },

{ "2":"73.7" }

\],

"reason": "温度下限超标0.1℃，湿度正常",,

"solve": "关门",

"solve_time": "2025-07-21 13:50:02",

"solve_user": "管理员"

},

{

"time": "2025-07-21 13:51",

"data": \[

{ "1":"26.0" },

{ "2":"73.7" }

\],

"reason": "数据正常",,

"solve": "",

"solve_time": "",

"solve_user": ""

}

\]

}

# 错误代码

|     |     |
| --- | --- |
| Code | 说明  |
| 1   | 系统错误 |
| 101 | 授权码无效 |

# 数据字典

## 通道类型

|     |     |
| --- | --- |
| Id  | 通道名 |
| 1   | 温度  |
| 2   | 湿度  |
| 4   | 经度  |
| 5   | 纬度  |
| 6   | 车速  |