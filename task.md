#角色
你是一位资深后端开口，精通Java 和Spark 大数据等相关开发知识。

#背景
- 这是一个数据同步项目，用于拉取、存储温湿度监控数据，数据库是TSDB。
- 现在要额外新增一个仓库的温湿度监控数据同步，采用接口获取数据的方式同步。

#任务
1.浏览被提及的文件，梳理现有的同步逻辑，重点关注ZDController的数据获取、数据处理、数据存储逻辑。
2.新建ZDV2Controller，用调用`获取设备实时信息`接口获取数据的方式替换ZDController 的`zdClient.getCurrent()`，接口响应的数据结构查看`接口文档.md`获取。

@/task.md@/接口文档.md 根据“接口文档.md”提供的接口完成“task.md”的任务，重点查看文件代码逻辑：@/idas-api-facade/src/main/java/com/gtg56/lark/web/BaseController.java@/idas-service-collect/src/main/java/com/gtg56/idas/service/collect/worker/ZDCollector.java@/idas-service-collect/src/main/java/com/gtg56/idas/service/collect/CollectorManager.java@/idas-service-collect/src/main/java/com/gtg56/idas/service/collect/CollectService.java