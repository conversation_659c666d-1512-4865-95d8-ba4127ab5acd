<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="idas-service-nodeProvider" version="1.0" architecture="base"/>
    <import resource="classpath:/spring/dubbo.xml"/>

    <bean id="classPool" class="javassist.ClassPool" factory-method="getDefault"/>
    <!--  workload node  -->
    <bean id="providingNodes" class="com.gtg56.idas.service.nodeProvider.NodeProviderService.NodeFactory" factory-method="providingNodes"/>
</beans>