package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.DataFrameWriter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class RunSQL extends WorkloadNode {
    private static final long serialVersionUID = -8612043278443771918L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("sql",true,null),
                NodeArg.of("outputHDFSPath",false,null),
                NodeArg.of("format",false,"json")
                );
    }
    
    @Override
    public String describe() {
        return "run sql segment and save as hdfs file";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String sql = args.get("sql");
        String outputHDFSPath = args.get("outputHDFSPath");
        String format = args.get("format");
        JSONObject json = new JSONObject();
        Dataset<Row> result = getSpark().sql(sql).limit(100).cache();
        long count = result.count();
        json.put("numRecords",count);
        
        if(StringUtils.isNotBlank(outputHDFSPath)) {
            json.put("outputHDFSPath", outputHDFSPath);
    
            DataFrameWriter<Row> writer = result.repartition(1).write().mode(SaveMode.Overwrite).format(format);
            if("csv".equalsIgnoreCase(format)) {
                writer.option("header","true");
            }
            writer.save(outputHDFSPath);
        } else {
            List<JSONObject> res = result.toJSON().collectAsList().stream().map(JSONObject::parseObject).collect(Collectors.toList());
            json.put("result",res);
        }
    
        result.unpersist(true);
        return dataReturn(json);
    }
    
    
}
