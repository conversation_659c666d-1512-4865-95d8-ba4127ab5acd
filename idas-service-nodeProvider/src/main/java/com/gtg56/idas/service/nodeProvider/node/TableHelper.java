package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class TableHelper extends WorkloadNode {
    private static final long serialVersionUID = -6926232289681187107L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("command",true,null),
                NodeArg.of("subject",true,null)
        );
    }
    
    @Override
    public String describe() {
        return "table utils for spark / hive / carbon";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String cmd = args.get("command");
        String subject = args.get("subject");
        
        Object ret = null;
        switch (cmd) {
            case "showCreate" : {
                ret = runSQL("SHOW CREATE TABLE " + subject);
                break;
            }
            case "describe" : {
                ret = runSQL("DESC " + subject);
                break;
            }
            case "refreshMV" : {
                ret = runSQL("REFRESH MATERIALIZED VIEW " + subject);
                break;
            }
            case "minorCompact" : {
                justRun("ALTER TABLE " + subject + " COMPACT 'MINOR'");
                justRun("CLEAN FILES FOR TABLE " + subject);
                break;
            }
            case "majorCompact" : {
                justRun("ALTER TABLE " + subject + " COMPACT 'MAJOR'");
                justRun("CLEAN FILES FOR TABLE " + subject);
                break;
            }
        }
        
        return dataReturn(ret);
    }
    
    private List<JSONObject> runSQL(String sql) {
        return getSpark().sql(sql).toJSON()
                .collectAsList()
                .stream()
                .map(JSONObject::parseObject)
                .collect(Collectors.toList());
    }
    
    private void justRun(String sql) {
        getSpark().sql(sql);
    }
}
