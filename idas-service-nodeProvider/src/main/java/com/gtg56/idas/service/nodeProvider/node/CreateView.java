package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeException;

import java.util.Arrays;
import java.util.List;

public class CreateView extends WorkloadNode {
    private static final long serialVersionUID = 6707941353793689247L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("hiveDatabase",true,null),
                NodeArg.of("viewName",true,null),
                NodeArg.of("materialized",false,"false"),
                NodeArg.of("sql",true,null)
        );
    }
    
    @Override
    public String describe() {
        return "create carbonData materialized view";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String hiveDatabase = args.get("hiveDatabase");
        String viewName = args.get("viewName");
        String sql = args.get("sql");
        Boolean materialized = args.getAsBoolean("materialized");
    
        boolean tableExists = getSpark().catalog().tableExists(hiveDatabase, viewName);
        
        if(tableExists)
            throw new WorkloadNodeException(this,"view " + viewName + " already exists in " + hiveDatabase);
            
        String ddl = "CREATE " + (materialized ? "materialized" : "") + " VIEW " +
                hiveDatabase + "." + viewName +
                " AS " + sql;
        getSpark().sql(ddl);
    
        JSONObject json = new JSONObject();
        json.put("defineSQL",sql);
        json.put("createDDL",ddl);
        return dataReturn(json);
    }
}
