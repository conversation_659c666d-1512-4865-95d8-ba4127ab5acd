package com.gtg56.idas.service.nodeProvider.node;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;

import java.util.Arrays;
import java.util.List;

public class MergeDeltaWithBase extends WorkloadNode {
    private static final long serialVersionUID = -1133679031677546539L;

    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("deltaHiveDatabase", true, null),
                NodeArg.of("deltaHiveTable", true, null),
                NodeArg.of("baseHiveDatabase", true, null),
                NodeArg.of("baseHiveTable", true, null),
                NodeArg.of("bizDate", true, null),
                NodeArg.of("baseBizDate", true, null),
                NodeArg.of("idCol", false, "id"),
                NodeArg.of("targetHiveDatabase", true, null),
                NodeArg.of("targetHiveTable", true, null),
                NodeArg.of("targetHiveTableBucket", false, "200")
        );
    }

    @Override
    public String describe() {
        return "merge delta data with base data";
    }

    @Override
    public boolean requireSpark() {
        return true;
    }

    @Override
    public String execution(NodeArgsDTO args) {
        String deltaHiveDatabase = args.get("deltaHiveDatabase");
        String deltaHiveTable = args.get("deltaHiveTable");
        String deltaTableFull = deltaHiveDatabase + "." + deltaHiveTable;
        String baseHiveDatabase = args.get("baseHiveDatabase");
        String baseHiveTable = args.get("baseHiveTable");
        String baseTableFull = baseHiveDatabase + "." + baseHiveTable;

        String bizDate = args.get("bizDate");
        String baseBizDate = args.get("baseBizDate");
        String idCol = args.get("idCol");

        String targetHiveDatabase = args.get("targetHiveDatabase");
        String targetHiveTable = args.get("targetHiveTable");
        Integer targetHiveTableBucket = args.getAsInteger("targetHiveTableBucket");
        String targetFull = targetHiveDatabase + "." + targetHiveTable;

        getSpark().conf().set("spark.sql.shuffle.partitions", targetHiveTableBucket);

        Dataset<Row> delta = getSpark().table(deltaTableFull).where("ds = '" + bizDate + "'").drop("ds");
        Dataset<Row> base = getSpark().table(baseTableFull).where("ds = '" + baseBizDate + "'").drop("ds");

        String deltaTmpView = "tmp_" + deltaHiveTable + "_delta_" + bizDate;
        String baseTmpView = "tmp_" + baseHiveTable + "_base_" + bizDate;
        delta.createOrReplaceTempView(deltaTmpView);
        base.createOrReplaceTempView(baseTmpView);

        String dropDeltaSQL = String.format(
                "SELECT b.* " +
                        "FROM %s b " +
                        "LEFT JOIN (SELECT d.%s FROM %s d GROUP BY d.%s) x " +
                        "ON b.%s = x.%s " +
                        "WHERE x.%s is null",
                baseTmpView,
                idCol,
                deltaTmpView,
                idCol,
                idCol,
                idCol,
                idCol
        );

        Dataset<Row> droppedBase = getSpark().sql(dropDeltaSQL);

        Dataset<Row> merged = droppedBase.unionAll(delta).sort(idCol);

        boolean tableExists = getSpark().catalog().tableExists(targetFull);

        if (tableExists) {
            String mergedTmpView = "tmp.tmp_" + targetHiveTable + "_merge_" + bizDate;
            merged.write()
                    .format("parquet")
                    .saveAsTable(mergedTmpView);

            String insertOverwriteSQL = "INSERT OVERWRITE TABLE " + targetFull + " " +
                    "PARTITION (ds='" + bizDate + "') " +
                    "SELECT * FROM " + mergedTmpView;

            getSpark().sql(insertOverwriteSQL);
            getSpark().sql("DROP TABLE " + mergedTmpView + " PURGE ");
        } else {
            merged.selectExpr("*", "'" + bizDate + "' as ds")
                    .write()
                    .format("parquet")
                    .partitionBy("ds")
                    .saveAsTable(targetFull);
        }

        getSpark().catalog().dropTempView(deltaTmpView);
        getSpark().catalog().dropTempView(baseTmpView);

        return noDataReturn();
    }
}
