package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.types.StructType;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("all")
public class JDBC2Carbon extends WorkloadNode {
    private static final long serialVersionUID = -1794950626354053913L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("jdbcUrl",true,null),
                NodeArg.of("jdbcUser",true,null),
                NodeArg.of("jdbcPasswd",true,null),
                NodeArg.of("jdbcTable",true,null),
                NodeArg.of("hiveDatabase",true,null),
                NodeArg.of("hiveTable",true,null),
                NodeArg.of("bizDate",true,null),
                NodeArg.of("createHiveTableIfNotExists",false,"true"),
                NodeArg.of("partitionColumns",false,null),
                NodeArg.of("sortColumns",false,null),
                NodeArg.of("sortScope",false,"LOCAL_SORT"),
                NodeArg.of("invIndexColumns",false,null),
                NodeArg.of("dictionaryColumns",false,null),
                NodeArg.of("dictionaryExcludeColumns",false,null),
                NodeArg.of("bucketColumns",false,null),
                NodeArg.of("bucketNum",false,"32"),
                NodeArg.of("compressor",false,null),
                NodeArg.of("streaming",false,"false"),
                NodeArg.of("createOnly",false,"false"),
                NodeArg.of("where",false,null)
        );
    }
    
    @Override
    public String describe() {
        return "import jdbc table to hive by carbon";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String  jdbcUrl = args.get("jdbcUrl");
        String  jdbcUser = args.get("jdbcUser");
        String  jdbcPasswd = args.get("jdbcPasswd");
        String  jdbcTable = args.get("jdbcTable");
        String  hiveDatabase = args.get("hiveDatabase");
        String  hiveTable = args.get("hiveTable");
        String  bizDate = args.get("bizDate");
        String  partitionColumns = args.get("partitionColumns");
        Boolean createHiveTableIfNotExists = args.getAsBoolean("createHiveTableIfNotExists");
        String  where = args.get("where");
    
        String hiveTableFull = hiveDatabase + "." + hiveTable;
    
        boolean tableExists = getSpark().catalog().tableExists(hiveTableFull);
        boolean createTable = false;
        if (!tableExists) {
            if (!createHiveTableIfNotExists) {
                throw new WorkloadNodeException(this, "hive table " + hiveTableFull + " not exists and createHiveTableIfNotExists = false");
            } else {
                createTable = true;
            }
        }
        
        Properties properties = new Properties();
        properties.setProperty("user",jdbcUser);
        properties.setProperty("password",jdbcPasswd);
        Dataset<Row> result = (
                StringUtils.isBlank(where) ?
                        getSpark().read().jdbc(jdbcUrl, jdbcTable, properties)
                        :
                        getSpark().read().jdbc(jdbcUrl, jdbcTable, properties).where(where)
        ).cache();
    
        long count = result.count();
        String tmpTableName = "tmp_pre_import_" + hiveTable;
        result.createOrReplaceTempView(tmpTableName);
    
        JSONObject json = new JSONObject();
        json.put("hiveDatabase",hiveDatabase);
        json.put("hiveTable",hiveTable);
        json.put("numRecords",count);
        json.put("createTable",createTable);
        
        if(createTable) {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("CREATE TABLE ").append(hiveTableFull);
    
            StructType schema = result.schema();
            List<String> partitionColDDLs = new ArrayList<>();
            partitionColDDLs.add("ds STRING COMMENT '业务日期'");
            LinkedHashMap<String, String> columnsDDLs = ddlColumnsSegment(schema);
            if(StringUtils.isNotBlank(partitionColumns)) {
                List<String> partitionCol = (List<String>) Arrays.asList(partitionColumns.split(","));
                partitionColDDLs.addAll(getPartitionColumnAndRemove(columnsDDLs, partitionCol));
            }
            
            sqlBuilder.append(" ( \n\t").append(StringUtils.join(columnsDDLs.values(),",\n\t")).append(") \n");
            sqlBuilder.append("PARTITIONED BY (").append(StringUtils.join(partitionColDDLs,",")).append(") \n");
    
            sqlBuilder.append("STORED AS carbondata \n");
            
            Map<String,String> tableProps = defaultTableProps();
            processSortColumns(tableProps,args);
            processInvertedIndexColumn(tableProps,args);
            processDictColumns(tableProps,args);
            processBucketColumn(tableProps,args);
            if(args.getAsBoolean("streaming")) {
                tableProps.put("streaming","true");
            }
            if(StringUtils.isNotBlank(args.get("compressor"))) {
                tableProps.put("carbon.column.compressor",args.get("compressor"));
            }
            sqlBuilder.append("TBLPROPERTIES (").append(map2TableProp(tableProps)).append(") ");
            
            String ddlSQL = sqlBuilder.toString();
            log.info("creating hive table by dll : \n {}",ddlSQL);
            getSpark().sql(ddlSQL);
            json.put("createTableSQL",ddlSQL);
        }
        
        if(!args.getAsBoolean("createOnly")) {
            // insert overwrite
            boolean hasExtPartition = StringUtils.isNotBlank(partitionColumns) && partitionColumns.split(",").length > 0;
            List<String> partitions = hasExtPartition ? Arrays.asList(partitionColumns.split(",")) : Collections.emptyList();
    
            String partitionSegment = "ds='" + bizDate + "'";
            String select = "*";
            if (hasExtPartition) {
                partitionSegment += "," + partitionColumns;
        
                List<String> selectNames = Arrays.stream(result.schema().fieldNames())
                        .filter(name -> !partitions.contains(name)).collect(Collectors.toList());
                selectNames.addAll(partitions);
                select = StringUtils.join(selectNames, " , ");
            }
            String sql = "insert overwrite table " + hiveTableFull +
                    " partition(" + partitionSegment + ") " +
                    "SELECT " + select + " FROM " + tmpTableName;
    
            json.put("insertOverwriteSQL", sql);
            getSpark().sql(sql);
    
            getSpark().catalog().dropTempView(tmpTableName);
        }
        
        return dataReturn(json);
    }
    
    private Map<String,String> defaultTableProps() {
        Map<String,String> tableProps = new LinkedHashMap<>();
        tableProps.put("flat_floder","true");// support hive style partition folder
        return tableProps;
    }
    
    /**
     * 处理排序列
     * @param tableProps
     * @param args
     */
    private void processSortColumns(Map<String,String> tableProps,NodeArgsDTO args) {
        if(StringUtils.isNotBlank(args.get("sortColumns"))) {
            tableProps.put("SORT_COLUMNS",args.get("sortColumns"));
            if(StringUtils.isNotBlank(args.get("sortScope")))
                tableProps.put("SORT_SCOPE",args.get("sortScope"));
        }
    }
    
    /**
     * 处理字典
     * @param tableProps
     * @param args
     */
    private void processDictColumns(Map<String,String> tableProps,NodeArgsDTO args) {
        boolean set = false;
        if(StringUtils.isNotBlank(args.get("dictionaryColumns"))) {
            tableProps.put("LOCAL_DICTIONARY_INCLUDE",args.get("dictionaryColumns"));
            set = true;
        } else if(StringUtils.isNotBlank(args.get("dictionaryExcludeColumns"))) {
            tableProps.put("LOCAL_DICTIONARY_EXCLUDE",args.get("dictionaryExcludeColumns"));
            set = true;
        }
        
        tableProps.put("LOCAL_DICTIONARY_ENABLE", String.valueOf(set));
    }
    
    /**
     * 处理倒排索引
     * @param tableProps
     * @param args
     */
    private void processInvertedIndexColumn(Map<String,String> tableProps,NodeArgsDTO args) {
        String invIndexColumns = args.get("invIndexColumns");
        if(StringUtils.isNotBlank(invIndexColumns)) {
            
            String sort_columns = tableProps.get("SORT_COLUMNS");
            if(StringUtils.isNotBlank(sort_columns)) {
                Set<String> sortColumns = new LinkedHashSet<>();
                sortColumns.addAll(Arrays.asList(sort_columns.split(",")));
                sortColumns.addAll(Arrays.asList(invIndexColumns.split(",")));
                
                tableProps.put("SORT_COLUMNS",StringUtils.join(sortColumns,","));
            } else {
                tableProps.put("SORT_COLUMNS",invIndexColumns);
            }
            tableProps.put("INVERTED_INDEX", invIndexColumns);
        }
    }
    
    /**
     * 处理分桶
     * @param tableProps
     * @param args
     */
    private void processBucketColumn(Map<String,String> tableProps,NodeArgsDTO args) {
        if(StringUtils.isNotBlank(args.get("bucketColumns"))) {
            tableProps.put("BUCKET_COLUMNS",args.get("bucketColumns"));
            tableProps.put("BUCKET_NUMBER",args.get("bucketNum"));
        }
    }
    
    private String map2TableProp(Map<String,String> tableProps) {
        return tableProps.entrySet().stream()
                .map(entry -> "'" + entry.getKey() + "'='" + entry.getValue() + "'")
                .collect(Collectors.joining(",\n"));
    }
    
    private LinkedHashMap<String,String> ddlColumnsSegment(StructType schema) {
        LinkedHashMap<String, String> ddls = new LinkedHashMap<>();
        Arrays.stream(schema.fields()).forEach(sf -> ddls.put(sf.name(),sf.toDDL()));
        
        return ddls;
    }
    
    private List<String> getPartitionColumnAndRemove(LinkedHashMap<String,String> ddls,List<String> partitionColumns) {
        List<String> partitionDDLs = new ArrayList<>();
        for(String partitionColumn : partitionColumns) {
            if(ddls.containsKey(partitionColumn)) {
                String ddl = ddls.remove(partitionColumn);
                partitionDDLs.add(ddl);
            } else {
                throw new WorkloadNodeException(this,"no column name " + partitionColumn + " in jdbc table");
            }
        }
        
        return partitionDDLs;
    }
}
