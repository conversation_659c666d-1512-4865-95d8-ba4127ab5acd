package com.gtg56.idas.service.nodeProvider.node;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.util.DateUtil;
import lombok.SneakyThrows;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import scala.Tuple2;

import java.util.*;
import java.util.stream.Collectors;

public class DropDateSplitPartition extends WorkloadNode {
    
    private static final long serialVersionUID = 4405444892911378826L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("table",true,null),
                NodeArg.of("ds",true,null),
                NodeArg.of("diff",false,"30")
        );
    }
    
    @Override
    public String describe() {
        return "drop sacrifice table date split partitions before [ds] - [diff]";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @SneakyThrows
    @Override
    public String execution(NodeArgsDTO args) {
        String table = args.get("table");
        Integer diff = args.getAsInteger("diff");
        String ds = args.get("ds");
        
        Date dsDate = DateUtil.parseDS(ds);
    
        Dataset<Row> partitions = getSpark().sql("show partitions " + table);
        
        Map<String,Boolean> isStr = new HashMap<>();
        
        getSpark().catalog().listColumns(table).collectAsList()
                .forEach(column -> {
                    boolean str =
                            "STRING".equalsIgnoreCase(column.dataType()) ||
                                    "VARCHAR".equalsIgnoreCase(column.dataType()) ||
                                    "CHAR".equalsIgnoreCase(column.dataType());
                    isStr.put(column.name(),str);
                });
        
        List<Map<String, String>> dsToDrop = partitions.collectAsList()
                .stream()
                .map(r -> r.getString(0))
                .map(s -> {
                    Date date = null;
                    Map<String,String> partition = new LinkedHashMap<>();
                    
                    if(s.contains("/")) {
                        String[] parts = s.split("/");
                        for(String part : parts) {
                            String[] split = part.split("=");
                            if(split.length != 2) continue;
                            partition.put(split[0],split[1]);
                            if("ds".equals(split[0]))
                                date = DateUtil.parseDS(split[1]);
                        }
                    } else {
                        String[] split = s.split("=");
                        if (split.length == 2 && "ds".equals(split[0])) {
                            date = DateUtil.parseDS(split[1]);
                            partition.put(split[0], split[1]);
                        }
                    }
                    
                    if(date!=null) {
                        return Tuple2.apply(date,partition);
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(tuple -> DateUtil.dayDiff(tuple._1(),dsDate) >= diff)
                .map(Tuple2::_2)
                .collect(Collectors.toList());
        
        List<String> dropSql = dsToDrop.stream()
                .map(dropDS -> {
                    String partitionStr = dropDS.entrySet().stream()
                            .map(entry -> {
                                String column = entry.getKey();
                                String value = isStr.getOrDefault(column, true) ?
                                        "'" + entry.getValue() + "'" :
                                        entry.getValue();
                                
                                return column + "=" + value;
                            })
                            .collect(Collectors.joining(","));
                    return "ALTER TABLE " + table + " DROP IF EXISTS PARTITION (" + partitionStr + ")";
                })
                .collect(Collectors.toList());

        dropSql.forEach(sql -> {
            getLog().info("Execute drop partition {}",sql);
            getSpark().sql(sql);
        });
        return dataReturn(dsToDrop);
    }
}
