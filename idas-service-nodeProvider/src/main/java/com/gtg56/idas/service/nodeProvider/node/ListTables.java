package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import lombok.SneakyThrows;
import org.apache.spark.sql.catalog.Database;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ListTables extends WorkloadNode {
    private static final long serialVersionUID = 4761377641454485235L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Collections.singletonList(NodeArg.of("database", false, "all"));
    }
    
    @Override
    public String describe() {
        return "list hive tables";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @SneakyThrows
    @Override
    public String execution(NodeArgsDTO args) {
        String database = args.get("database");
        List<JSONObject> result = new ArrayList<>();
        if("all".equalsIgnoreCase(database)) {
            List<String> databases = getSpark().catalog()
                    .listDatabases().collectAsList()
                    .stream()
                    .map(Database::name)
                    .collect(Collectors.toList());
            
            for(String db : databases) {
                result.addAll(
                        getSpark().catalog()
                                .listTables(db).toJSON().collectAsList()
                                .stream()
                                .map(JSONObject::parseObject)
                                .collect(Collectors.toList())
                );
            }
        } else {
            result.addAll(
                    getSpark().catalog()
                            .listTables(database).toJSON().collectAsList()
                            .stream()
                            .map(JSONObject::parseObject)
                            .collect(Collectors.toList())
            );
        }
        return dataReturn(result);
    }
}
