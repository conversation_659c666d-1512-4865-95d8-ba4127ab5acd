package com.gtg56.idas.service.nodeProvider;

import com.gtg56.idas.common.entity.dto.NodeProvideDTO;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeException;
import com.gtg56.idas.common.util.PackageUtil;
import com.gtg56.idas.service.define.IWorkloadProviderService;
import javassist.ClassPool;
import javassist.CtClass;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService(interfaceClass = IWorkloadProviderService.class, protocol = "dubbo", registry = "zookeeper", filter = "-exception")
public class NodeProviderService implements IWorkloadProviderService {
    
    @Resource
    private ClassPool classPool;
    
    @Resource
    private Map<String, WorkloadNode> providingNodes;
    
    @SneakyThrows
    @Override
    public NodeProvideDTO provideNode(String name) {
        if (providingNodes.containsKey(name)) {
            String timeSuffix =  "_prov" + System.currentTimeMillis();
            
            WorkloadNode workloadNode = providingNodes.get(name);
            
            if(workloadNode.active()) {
    
                Class<? extends WorkloadNode> nodeClass = workloadNode.getClass();
                String nodeClassName = nodeClass.getName();
                String nodeNewName = nodeClassName + timeSuffix;
    
                CtClass nodeCtClass = classPool.getAndRename(nodeClassName, nodeNewName);
    
                byte[] nodeByteCode = nodeCtClass.toBytecode();
                log.info("provide brand new node {} as {}",nodeClassName,nodeNewName);
                return NodeProvideDTO.builder()
                        .nodeClassName(nodeNewName).nodeByteCode(nodeByteCode)
                        .build();
            } else {
                throw WorkloadNodeException.notActive(workloadNode);
            }
        } else {
            throw WorkloadNodeException.noSuchNode(name);
        }
    }
    
    @Override
    public List<String> listProviding() {
        return new ArrayList<>(providingNodes.keySet());
    }
    
    @Slf4j
    public static class NodeFactory {
        
        public static Map<String, WorkloadNode> providingNodes() {
            List<String> nodeClasses = PackageUtil.getClassName("com.gtg56.idas.service.nodeProvider.node");
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            
            return nodeClasses.stream().map(className -> {
                try {
                    Class<?> nodeClass = classLoader.loadClass(className);
                    if(WorkloadNode.class.isAssignableFrom(nodeClass)) {
                        log.info("Loading node class {} as {} ",nodeClass.getName(),nodeClass.getSimpleName());
                        return (WorkloadNode) nodeClass.newInstance();
                    }
                } catch (ReflectiveOperationException e) {
                    log.warn("creating workload node " + className + " fail",e);
                }
                return null;
            })
                    .filter(Objects::nonNull)
                    .filter(WorkloadNode::active)
                    .collect(Collectors.toMap(WorkloadNode::nodeName, node -> node));
        }
    }
}
