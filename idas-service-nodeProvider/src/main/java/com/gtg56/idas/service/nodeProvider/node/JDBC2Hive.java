package com.gtg56.idas.service.nodeProvider.node;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeException;
import com.gtg56.idas.common.util.JDBCUtil;
import com.gtg56.idas.common.util.SpringUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.*;
import org.apache.spark.sql.*;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("all")
public class JDBC2Hive extends WorkloadNode {
    
    private static final long serialVersionUID = -4106138963055926808L;
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("jdbcUrl", true, null),
                NodeArg.of("jdbcUser", true, null),
                NodeArg.of("jdbcPasswd", true, null),
                NodeArg.of("jdbcTable", true, null),
                NodeArg.of("jdbcDriver", false, com.mysql.cj.jdbc.Driver.class.getName()),
                NodeArg.of("hiveDatabase", true, null),
                NodeArg.of("hiveTable", true, null),
                NodeArg.of("bizDate", true, null),
                NodeArg.of("createHiveTableIfNotExists", false, "true"),
                NodeArg.of("format", false, "parquet"),
                NodeArg.of("partitionColumns", false, null),
                NodeArg.of("where", false, null),
                NodeArg.of("bigTable", false, "false"),
                NodeArg.of("idCol", false, "id"),
                NodeArg.of("numParallel", false, "16"),
                NodeArg.of("rangeParallel", false, null)
        );
    }
    
    @Override
    public String describe() {
        return "import jdbc table to hive";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String  hiveDatabase = args.get("hiveDatabase");
        String  hiveTable = args.get("hiveTable");
        String  bizDate = args.get("bizDate");
        String  format = args.get("format");
        String  partitionColumns = args.get("partitionColumns");
        Boolean createHiveTableIfNotExists = args.getAsBoolean("createHiveTableIfNotExists");
    
        String hiveTableFull = hiveDatabase + "." + hiveTable;
    
        boolean tableExists = getSpark().catalog().tableExists(hiveTableFull);
        boolean createHiveTable = false;
        if (!tableExists) {
            if (!createHiveTableIfNotExists) {
                throw new WorkloadNodeException(this, "hive table " + hiveTableFull + " not exists and createHiveTableIfNotExists = false");
            } else {
                createHiveTable = true;
            }
        }

        boolean hasExtPartition = StringUtils.isNotBlank(partitionColumns) && partitionColumns.split(",").length > 0;
        List<String> partitions = hasExtPartition ? Arrays.asList(partitionColumns.split(",")) : Collections.emptyList();

        Dataset<Row> jdbc = readJdbc(args);

        Dataset<Row> result = hasExtPartition ?
                jdbc.repartition(partitions.stream().map(jdbc::col).toArray(Column[]::new)).cache() :
                jdbc.cache();

        long count = result.count();
        int numPartitions = result.rdd().getNumPartitions();
        getLog().info("jdbc fetch {} rows with {} partitions", count, numPartitions);

        JSONObject json = new JSONObject();
        json.put("hiveDatabase", hiveDatabase);
        json.put("hiveTable", hiveTable);
        json.put("numRecords", count);

        if (createHiveTable) {
            List<String> partitionCols = new ArrayList<>();
            partitionCols.add("ds");
            if (hasExtPartition) {
                partitionCols.addAll(partitions);
            }
        
            result.selectExpr("*", "'" + bizDate + "' as ds").write().mode(SaveMode.Overwrite)
                    .format(format)
                    .partitionBy(partitionCols.toArray(new String[0]))
                    .saveAsTable(hiveTableFull);
        
            json.put("createHiveTableSQL",getSpark().sql("show create table " + hiveTableFull).first().getString(0));
        } else {
            String partitionSegment = "ds='" + bizDate + "'";
            String tmpTableName = "tmp_pre_import_" + hiveTable + "_" + RandomStringUtils.random(10,true,true);
            result.createOrReplaceTempView(tmpTableName);
            String select = "*";
            if (hasExtPartition) {
                partitionSegment += "," + partitionColumns;
            
                List<String> selectNames = Arrays.stream(result.schema().fieldNames())
                        .filter(name -> !partitions.contains(name)).collect(Collectors.toList());
                selectNames.addAll(partitions);
                select = StringUtils.join(selectNames," , ");
            }
            String sql = "insert overwrite table " + hiveTableFull +
                    " partition(" + partitionSegment +") " +
                    "SELECT " + select + " FROM " + tmpTableName;
        
            json.put("insertOverwriteSQL",sql);
            getSpark().sql(sql);
        
            getSpark().catalog().dropTempView(tmpTableName);
        }
    
        json.put("createHiveTable",createHiveTable);
        result.unpersist(true);
        return dataReturn(json);
    }

    private Dataset<Row> readJdbc(NodeArgsDTO args) {
        String jdbcUrl = args.get("jdbcUrl");
        String jdbcUser = args.get("jdbcUser");
        String jdbcPasswd = args.get("jdbcPasswd");
        String jdbcTable = args.get("jdbcTable");
        String where = args.get("where");
        String driver = args.get("jdbcDriver");

        Boolean bigTable = args.getAsBoolean("bigTable");

        if (!bigTable) {
            DataFrameReader reader = getSpark().read().format("jdbc");
            reader.option("url", jdbcUrl)
                    .option("user", jdbcUser)
                    .option("password", jdbcPasswd)
                    .option("driver", driver);

            if (StringUtils.isBlank(where)) {
                reader.option("dbtable", jdbcTable);
            } else {
                reader.option("query", "SELECT * FROM " + jdbcTable + " WHERE " + where);
            }
            return reader.load();
        } else {
            String idCol = args.get("idCol");
            Integer numParallel = args.getAsInteger("numParallel");
            Integer rangeParallel = args.getAsInteger("rangeParallel");
            if (rangeParallel == null) rangeParallel = numParallel;

            getLog().info("big table import mode active. idCol {} numParallel {} rangeParallel {}", idCol, numParallel, rangeParallel);

            DataFrameReader reader = getSpark().read().format("jdbc");
            String tableMinMaxId = JDBCUtil.getTableMinMaxId(jdbcUrl, jdbcUser, jdbcPasswd, jdbcTable, idCol, where);
            String[] idBound = tableMinMaxId.split(",");
            String query = "(SELECT " + idCol + " FROM " + jdbcTable + (StringUtils.isNotBlank(where) ? " WHERE " + where : "") + ") t";
            reader.option("url", jdbcUrl)
                    .option("user", jdbcUser)
                    .option("password", jdbcPasswd)
                    .option("driver", driver)
                    .option("dbtable", query)
                    .option("partitionColumn", idCol)
                    .option("lowerBound", idBound[0])
                    .option("upperBound", idBound[1])
                    .option("numPartitions", rangeParallel);

            getLog().info("idCol {} bound are ({})", idCol, tableMinMaxId);

            Dataset<Row> raw = reader.load();
            Dataset<Row> ids = raw.repartitionByRange(numParallel, raw.col(idCol)).sort(idCol);

            String bucketsText = "/user/hdfs/idBucket/" + jdbcTable + "_" + System.currentTimeMillis() + ".csv";

            ids.write().csv(bucketsText);

            getLog().info("{} ranged done. save to bucket file {}", idCol, bucketsText);

            try {
                FileSystem hdfs = SpringUtil.getBean("hdfs", FileSystem.class);
                RemoteIterator<LocatedFileStatus> buckets = hdfs.listFiles(new Path(bucketsText), false);

                List<String> idRanges = new ArrayList<>();

                while (buckets.hasNext()) {
                    LocatedFileStatus file = buckets.next();
                    if (file.getPath().getName().endsWith(".csv")) {
                        getLog().info("reading bucket file {}", file.getPath().getName());
                        FSDataInputStream is = hdfs.open(file.getPath());
                        InputStreamReader isr = new InputStreamReader(is);
                        BufferedReader br = new BufferedReader(isr);

                        Long min = Long.MAX_VALUE;
                        Long max = Long.MIN_VALUE;

                        String line;
                        while ((line = br.readLine()) != null) {
                            long d = Long.parseLong(line);

                            min = d < min ? d : min;
                            max = d > max ? d : max;
                        }
                        getLog().info("range is ({},{})", min, max);
                        idRanges.add(idCol + " >= " + min + " AND " + idCol + " <= " + max);
                    }
                }
                getLog().info("read bucket range done, start import");
                hdfs.delete(new Path(bucketsText), true);

                Properties jdbcProp = new Properties();
                jdbcProp.setProperty("url", jdbcUrl);
                jdbcProp.setProperty("user", jdbcUser);
                jdbcProp.setProperty("password", jdbcPasswd);
                jdbcProp.setProperty("driver", driver);

                Dataset<Row> jdbc = getSpark().read().jdbc(jdbcUrl, jdbcTable, idRanges.toArray(new String[0]), jdbcProp);
                if (StringUtils.isNotBlank(where)) {
                    return jdbc.where(where);
                }
                return jdbc;
            } catch (Exception e) {
                throw new BizException(e);
            }
        }
    }
    
    
}
