package com.gtg56.idas.service.nodeProvider.node;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.util.JDBCUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.functions;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class Hive2JDBC extends WorkloadNode {
    private static final long serialVersionUID = -4413994911539563524L;

    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("jdbcUrl", true, null),
                NodeArg.of("jdbcUser", true, null),
                NodeArg.of("jdbcPasswd", true, null),
                NodeArg.of("jdbcTable", true, null),
                NodeArg.of("jdbcDriver", false, com.mysql.cj.jdbc.Driver.class.getName()),
                NodeArg.of("delete", false, "true"),
                NodeArg.of("deleteCondition", false, ""),
                NodeArg.of("hiveDatabase", true, null),
                NodeArg.of("hiveTable", true, null),
                NodeArg.of("bizDate", true, null)
        );
    }

    @Override
    public String describe() {
        return "export hive table to jdbc";
    }

    @Override
    public boolean requireSpark() {
        return true;
    }

    @Override
    public String execution(NodeArgsDTO args) {
        String hiveDatabase = args.get("hiveDatabase");
        String hiveTable = args.get("hiveTable");
        String bizDate = args.get("bizDate");

        String hiveTableFull = hiveDatabase + "." + hiveTable;

        boolean ds = StringUtils.isNotBlank(bizDate);

        Dataset<Row> table = getSpark().table(hiveTableFull);

        Dataset<Row> data = (ds ? table.where("ds = '" + bizDate + "'") : table).cache();

        long count = data.count();

        getLog().info("prepare to export {} rows from {}", count, hiveTableFull);

        String jdbcUrl = args.get("jdbcUrl");
        String jdbcUser = args.get("jdbcUser");
        String jdbcPasswd = args.get("jdbcPasswd");
        String jdbcTable = args.get("jdbcTable");
        String driver = args.get("jdbcDriver");
        Boolean delete = args.getAsBoolean("delete");
        String deleteCondition = args.get("deleteCondition");

        if (delete) {
            JDBCUtil.clearTable(jdbcUrl, jdbcUser, jdbcPasswd, jdbcTable, deleteCondition);
        }
        String idBound = JDBCUtil.getTableMinMaxId(jdbcUrl, jdbcUser, jdbcPasswd, jdbcTable, "id");
        String[] minmax = idBound.split(",");
        long offset;
        if ("null".equals(minmax[1])) {
            offset = 1L;
        } else {
            offset = Long.parseLong(minmax[1]) + 1L;
        }
        Dataset<Row> out = data.withColumn("id", functions.monotonically_increasing_id().plus(offset));

        Properties jdbcProp = new Properties();
        jdbcProp.setProperty("url", jdbcUrl);
        jdbcProp.setProperty("user", jdbcUser);
        jdbcProp.setProperty("password", jdbcPasswd);
        jdbcProp.setProperty("driver", driver);

        out.write().mode(SaveMode.Append).jdbc(jdbcUrl, jdbcTable, jdbcProp);

        data.unpersist(true);

        return dataReturn(count);
    }
}
