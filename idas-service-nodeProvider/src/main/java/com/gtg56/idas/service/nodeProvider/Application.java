package com.gtg56.idas.service.nodeProvider;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {"com.gtg56.idas.service.nodeProvider"},
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@DubboComponentScan
@ImportResource(locations = {"classpath:/nodeProviderServiceBeans.xml"})
public class Application {
    
    public static void main(String[] args) {
        SpringApplication.run(Application.class,args);
    }
}
