package com.gtg56.idas.service.streaming.application;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.base.ImABean;
import com.gtg56.idas.common.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.PropertySource;

import java.util.Date;
import java.util.Objects;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {"com.gtg56.idas"},
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@PropertySource(value = {"classpath:/application.properties"})
@Data
public class Play implements ImABean {
    private static final long serialVersionUID = -8180061811561024818L;
    
    public static void main(String[] args) throws Exception {
        ConfigurableApplicationContext context = SpringApplication.run(Play.class,args);
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers","10.1.9.62:9092");
        properties.setProperty("group.id","flink_3");
    
        FlinkKafkaConsumer<String> kafkaConsumer
                = new FlinkKafkaConsumer<>("log_idas-service-spark",new SimpleStringSchema(),properties);
        DataStreamSource<String> kafkaSource = env.addSource(kafkaConsumer);
        
        
        Pattern pattern = Pattern.compile("(^\\[]]*) \\[([A-Z]+)] ([a-zA-Z0-9._]+) (.+)");
    
        SingleOutputStreamOperator<Play> logStream = kafkaSource.map(log -> {
            Matcher matcher = pattern.matcher(log);
            if (matcher.find()) {
                Play play = new Play();
            
                play.setLogTime(DateUtil.shortYMDHMS().parse(matcher.group(1)));
                play.setLogLevel(matcher.group(2));
                play.setLoggerClassName(matcher.group(3));
                play.setMessage(matcher.group(4));
                play.setRawLog(log);
            
                return play;
            } else {
                return null;
            }
        }).filter(Objects::nonNull);
        
        logStream.filter(play -> "WARN".equalsIgnoreCase(play.getLogLevel()) || "ERROR".equalsIgnoreCase(play.getLogLevel()))
                .filter(play -> play.getMessage().contains("Exception"))
                .map(new MapFunction<Play, Tuple2<String,Integer>>() {
                    private static final long serialVersionUID = 5671174869249532437L;
    
                    @Override
                    public Tuple2<String, Integer> map(Play play) throws Exception {
                        return new Tuple2<>(play.getLoggerClassName(),1);
                    }
                })
                .keyBy(tuple -> tuple.f0)
                .timeWindow(Time.seconds(5))
                .reduce((ReduceFunction<Tuple2<String, Integer>>) (t1, t2) -> new Tuple2<>(t1.f0,t1.f1 + t2.f1))
                .print();
        
    
        env.execute();
    }
    
    private Date logTime;
    private String logLevel;
    private String threadName;
    private String loggerClassName;
    private String message;
    private String rawLog;
    
    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
