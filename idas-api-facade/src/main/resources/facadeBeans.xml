<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/util
       https://www.springframework.org/schema/util/spring-util.xsd">

    <dubbo:application name="idas-api" version="1.0" architecture="api"/>

    <import resource="classpath:/spring/dubbo.xml"/>

    <dubbo:reference id="sensorService" interface="com.gtg56.idas.service.define.ISensorService" lazy="true"
                     check="false"/>
    <dubbo:reference id="collectService" interface="com.gtg56.idas.service.define.ICollectService" lazy="true"
                     check="false"/>
    <dubbo:reference id="authService" interface="com.gtg56.idas.service.define.IAuthService" lazy="true" check="false"/>

    <dubbo:reference id="roleService" interface="com.gtg56.idas.service.define.IRoleService" lazy="true" check="false"/>
    <dubbo:reference id="permissionService" interface="com.gtg56.idas.service.define.IPermissionService" lazy="true" check="false"/>
    <dubbo:reference id="rolePermissionService" interface="com.gtg56.idas.service.define.IRolePermissionService" lazy="true" check="false"/>
    <dubbo:reference id="roleUserService" interface="com.gtg56.idas.service.define.IRoleUserService" lazy="true" check="false"/>

    <dubbo:reference id="checkConfigService" interface="com.gtg56.idas.service.define.ICheckConfigService" lazy="true" check="false"/>
    <dubbo:reference id="checkConfigDetailService" interface="com.gtg56.idas.service.define.ICheckConfigDetailService" lazy="true" check="false"/>
    <dubbo:reference id="checkResultService" interface="com.gtg56.idas.service.define.ICheckResultService" lazy="true" check="false"/>
    <dubbo:reference id="checkResultDetailService" interface="com.gtg56.idas.service.define.ICheckResultDetailService" lazy="true" check="false"/>

    <util:map id="fda" key-type="java.lang.String" value-type="java.lang.String">
        <entry key="ZJFDA" value="浙江省食品药品监督局"/>
    </util:map>
</beans>