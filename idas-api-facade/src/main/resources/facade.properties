#项目分组和项目名配置
spring.cloud.nacos.discovery.group=idas
spring.application.name=idas-api

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

##端口号
server.port=8082
# 上传文件总的最大值
spring.servlet.multipart.max-request-size=100MB
# 单个文件的最大值
spring.servlet.multipart.max-file-size=100MB

swagger.base-path=/**
swagger.base-package=com.gtg56.idas

#签名密钥
system.jwt.public-key=-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuNDV+xZW7w8XIVBsXVJTzIqaSBntPGpAxyeuILZQrenIMsoF+c3FG34L9vAwnBdA+mSW4zRnfvcCUvYxvpxF9tnuGJQM1M8a23C0gBYS7+aZ9a0EBbq81ls8rvnrx8CB6Fe+S79z+BRa8T+8ZZJEze6+kUd/acxejTyY95ymzVz1PzBOCPFRMCUZ+0EyDvXPRb1KZ5Mb8q8wp9WitVac9eUQWHmX7VJ/6IYXbGFMEuGOsXVzh12GcLRUhKwlcHucIUk64RnSpWhhKGFbQPMKpcZAaEynSTkUABrT6xxaBgsRfKyFOSM+ctvH65kQHMy8PXy5oIO2IDgP77oANsXQjwIDAQAB-----END PUBLIC KEY-----