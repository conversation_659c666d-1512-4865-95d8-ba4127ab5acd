/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: ResourceServerConfiguration
 * @Author: zhangchuyi
 */
package com.gtg56.idas.api.facade.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.vote.AbstractAccessDecisionManager;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.web.cors.CorsUtils;

/**purchasePlans
 * @Description:
 *
 *
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION 
 *
 * @Author: zhangchuyi
 * @CeateDate: 2019-12-09 16:12
 * @Since: Bamboo V00.00.001
 */
@Configuration
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources.accessDeniedHandler(accessDeniedHandler())
                .authenticationEntryPoint(authenticationEntryPoint());
    }

    @Override
    public void configure(HttpSecurity httpSecurity) throws Exception{
        httpSecurity.cors().and().authorizeRequests()
                .requestMatchers(CorsUtils::isPreFlightRequest).permitAll()
                .antMatchers(
                        "/swagger**",
                        "/swagger*/**",
                        "/webjars/**",
                        "/csrf",
                        "/**/api-docs",
                        "/api/collect/**",
                        "/api/heartbeat/").permitAll()
                .anyRequest()
                .permitAll()
                .withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {
                    public <O extends FilterSecurityInterceptor> O postProcess(O fsi) {
                        ((AbstractAccessDecisionManager) fsi.getAccessDecisionManager()).getDecisionVoters().add(new RoleBasedVoter());
                        return fsi;
                    }
                });
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return new CustomAccessDeniedHandler();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new CustomAuthenticationEntryPoint();
    }

}
