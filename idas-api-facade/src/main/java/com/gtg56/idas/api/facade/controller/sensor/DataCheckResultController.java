package com.gtg56.idas.api.facade.controller.sensor;


import com.gtg56.idas.api.facade.service.INoticeService;
import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.convert.DataCheckConfigConvert;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.ICheckResultService;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import com.gtg56.idas.common.convert.DataCheckResultConvert;
import com.gtg56.idas.common.entity.query.DataCheckResultQuery;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RestController;
import com.gtg56.lark.web.BaseController;

/**
 * <p>
 * 数据完整性检测结果表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Api(tags = "Biz:DataCheckResultController",description = "数据完整性检测结果表管理功能")
@RestController
@RequestMapping("/api/dataCheckResults")
public class DataCheckResultController extends BaseController {

    @Autowired
    private ICheckResultService checkResultService;

    @Autowired
    private INoticeService noticeService;

    @ApiOperation(value="通过query对象获取数据完整性检测结果表列表", notes="分页获取数据完整性检测结果表列表。处理/dataCheckResult/的GET请求，用来获取数据完整性检测结果表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataCheckResultQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckResultDTO>> list(DataCheckResultQuery query,
                                    String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export,
                                    HttpServletRequest request, HttpServletResponse response){
        PageObject<DataCheckResult> pageObject = checkResultService.pageByQuery(query, initPageParams());
        PageObject<DataCheckResultDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据完整性检测结果表列表", notes="分页获取数据完整性检测结果表列表。处理/dataCheckResult/的GET请求，用来获取数据完整性检测结果表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckConfigId", value = "数据完整性检测配置表ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String"),
            @ApiImplicitParam(name = "excuteTimeStart", value = "调度时间", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "excuteTimeEnd", value = "调度时间", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckResultDTO>> listByMap(String searchKeyword,
                                    String dataCheckConfigId,
                                    String remark,
                                    String excuteTimeStart,
                                    String excuteTimeEnd,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("dataCheckConfigId", dataCheckConfigId);
        params.put("remark", remark);
        params.put("excuteTimeStart", excuteTimeStart);
        params.put("excuteTimeEnd", excuteTimeEnd);
        PageObject<DataCheckResult> pageObject = checkResultService.pageByMap(params, initPageParams());
        PageObject<DataCheckResultDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value = "查询数据完整性检测结果表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据完整性检测结果表详细信息")
    @ApiImplicitParam(name="ids",value = "数据完整性检测结果表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataCheckResultDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataCheckResultDTO> dataCheckResultDTOList = BeanUtil.transformList((checkResultService.listByIds(ids)), DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dataCheckResultDTOList);
    }

    @ApiOperation(value = "查询数据完整性检测结果表",notes = "根据url的ID查询数据完整性检测结果表详细信息")
    @ApiImplicitParam(name="id",value = "数据完整性检测结果表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataCheckResultDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataCheckResultDTO dataCheckResultDTO = BeanUtil.transform(checkResultService.getById(Long.valueOf(id)), DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dataCheckResultDTO);
    }

    @ApiOperation(value = "创建数据完整性检测结果表",notes = "权限：system:dataCheckResult:add。根据DataCheckResult对象创建数据完整性检测结果表,处理/dataCheckResult/的POST请求，用来创建DataCheckResult。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataCheckResultDTO",value = "数据完整性检测结果表视图对象dataCheckResult",required = true, dataType = "DataCheckResultDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckResultDTO> add(@RequestBody DataCheckResultDTO dataCheckResultDTO){
        DataCheckResult dataCheckResult = BeanUtil.transform(dataCheckResultDTO, DataCheckResult.class, DataCheckResultConvert.toEntity());

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataCheckResult.setCreatorId(Long.valueOf(currentUser.getId()));
            dataCheckResult.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataCheckResult = checkResultService.saveData(dataCheckResult);
        dataCheckResultDTO = BeanUtil.transform(dataCheckResult, DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dataCheckResultDTO);
    }

    @ApiOperation(value = "更新数据完整性检测结果表",notes = "根据url的id来指定更新对象，并根据传过来的dataCheckResult信息来更新数据完整性检测结果表详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckResultDTO",value = "数据完整性检测结果表视图对象dataCheckResultDTO",required = true, dataType = "DataCheckResultDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckResultDTO> edit(@RequestBody DataCheckResultDTO dataCheckResultDTO){
        DataCheckResult dataCheckResult = BeanUtil.transform(dataCheckResultDTO, DataCheckResult.class, DataCheckResultConvert.toEntity());
        checkResultService.updateData(dataCheckResult);
        dataCheckResultDTO = BeanUtil.transform(dataCheckResult, DataCheckResultDTO.class, DataCheckResultConvert.toVO());
        return ResponseData.success(dataCheckResultDTO);
    }

    @ApiOperation(value = "删除数据完整性检测结果表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据完整性检测结果表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        checkResultService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }

    @ApiOperation(value = "开始执行数据完整性检测",notes = "权限：system:dataCheckResult:add。根据DataCheckResult对象创建数据完整性检测结果表,处理/dataCheckResult/doCheckData的POST请求，用来创建DataCheckResult。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataCheckResultDTO",value = "数据完整性检测结果表视图对象dataCheckResult",required = false, dataType = "DataCheckResultDTO")
    @PostMapping(value = "/doCheckData",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckResultDTO> doCheckData(@RequestBody DataCheckConfigDTO dataCheckConfigDTO){

        DataCheckConfig dataCheckConfig = BeanUtil.transform(dataCheckConfigDTO, DataCheckConfig.class, DataCheckConfigConvert.toEntity());

        List<DataCheckResult> dataCheckResultList = checkResultService.doCheckData(dataCheckConfig);
        List<DataCheckResultDTO> dataCheckResultDTOList = BeanUtil.transformList(dataCheckResultList, DataCheckResultDTO.class, DataCheckResultConvert.toVO());

        int totalQuantity = 0;
        int normalQuantity = 0;
        int unnormalQuantity = 0;
        for(DataCheckResultDTO dataCheckResultDTO : dataCheckResultDTOList){
            normalQuantity += dataCheckResultDTO.getNormalQuantity();
            unnormalQuantity += dataCheckResultDTO.getUnnormalQuantity();
        }
        totalQuantity = normalQuantity + unnormalQuantity;


        noticeService.reportDeployMessage("数据完整性检测结果","总检测数量:" + totalQuantity +"\n 正常数量: "+ normalQuantity  +"\n 异常数量: "+  unnormalQuantity ,"","");


        return ResponseData.success(new DataCheckResultDTO());
    }


}
