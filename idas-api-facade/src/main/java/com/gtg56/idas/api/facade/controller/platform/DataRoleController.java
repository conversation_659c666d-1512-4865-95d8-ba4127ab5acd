package com.gtg56.idas.api.facade.controller.platform;


import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.convert.DataRoleConvert;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.dto.auth.DataRoleDTO;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.query.DataRoleQuery;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.IPermissionService;
import com.gtg56.idas.service.define.IRolePermissionService;
import com.gtg56.idas.service.define.IRoleService;
import com.gtg56.lark.web.BaseController;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * 数据角色 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Api(tags = "Platform:DataRoleController",description = "数据角色管理功能")
@RestController
@RequestMapping("/api/dataRoles")
public class DataRoleController extends BaseController {

    @Resource
    private IRoleService roleService;

    @Resource
    private IPermissionService permissionService;

    @Resource
    private IRolePermissionService rolePermissionService;

    @ApiOperation(value="通过query对象获取数据角色列表", notes="分页获取数据角色列表。处理/dataRole/的GET请求，用来获取数据角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataRoleQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRoleDTO>> list(DataRoleQuery query,
                                                      String searchKeyword,
                                                      @RequestParam(defaultValue="false")  boolean export,
                                                      HttpServletRequest request, HttpServletResponse response){
        PageObject<DataRole> pageObject = roleService.pageByQuery(query, initPageParams());
        PageObject<DataRoleDTO> DTOPageObject = BeanUtil.transformPage(pageObject, DataRoleDTO.class, DataRoleConvert.toVO());
        return ResponseData.success(DTOPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据角色列表", notes="分页获取数据角色列表。处理/dataRole/的GET请求，用来获取数据角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "角色编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "角色名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "角色状态", required = false, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "角色类型 dictCode: dataRoleType WAREHOUSE AREA SENSOR", required = false, dataType = "String"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRoleDTO>> listByMap(String searchKeyword,
                                    String code,
                                    String name,
                                    String status,
                                    String type,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("name", name);
        params.put("status", status);
        params.put("type", type);
        PageObject<DataRole> pageObject = roleService.pageByMap(params, initPageParams());
        PageObject<DataRoleDTO> DTOPageObject = BeanUtil.transformPage(pageObject, DataRoleDTO.class, DataRoleConvert.toVO());
        return ResponseData.success(DTOPageObject);
    }

    @ApiOperation(value = "查询数据角色",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据角色详细信息")
    @ApiImplicitParam(name="ids",value = "数据角色ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataRoleDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataRoleDTO> dataRoleDTOList = BeanUtil.transformList(roleService.listByIds(Arrays.asList(ids.split(","))), DataRoleDTO.class, DataRoleConvert.toVO());
        return ResponseData.success(dataRoleDTOList);
    }

    @ApiOperation(value = "查询数据角色",notes = "根据url的ID查询数据角色详细信息")
    @ApiImplicitParam(name="id",value = "数据角色ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataRoleDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataRoleDTO dataRoleDTO = BeanUtil.transform(roleService.getById(Long.valueOf(id)), DataRoleDTO.class, DataRoleConvert.toVO());

        if (dataRoleDTO != null){
            dataRoleDTO.setId(id);
            List<DataPermissionDTO> dataPermissionmList = permissionService.listVOByRoleId(id);
            dataRoleDTO.setDataPermissionVOList(dataPermissionmList);
        }

        return ResponseData.success(dataRoleDTO);
    }

    @ApiOperation(value = "创建数据角色",notes = "权限：system:dataRole:add。根据DataRole对象创建数据角色,处理/dataRole/的POST请求，用来创建DataRole。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataRoleDTO",value = "数据角色视图对象dataRole",required = true, dataType = "DataRoleDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataRoleDTO> add(@RequestBody DataRoleDTO dataRoleDTO){


        DataRole tmpDataRole = roleService.findByCode(dataRoleDTO.getCode());
        if (tmpDataRole != null){
            return ResponseData.error("该数据角色编码已经存在，请输入其他编码");
        }

        //该列表中缺少id,需要查询数据库
        List<DataPermissionDTO> dataPermissionDTOList = dataRoleDTO.getDataPermissionVOList();
        dataPermissionDTOList = permissionService.listVOByCodeList(dataPermissionDTOList);

        DataRole dataRole = BeanUtil.transform(dataRoleDTO, DataRole.class, DataRoleConvert.toEntity());

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataRole.setCreatorId(Long.valueOf(currentUser.getId()));
            dataRole.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataRole = roleService.saveData(dataRole);

        Boolean flag = rolePermissionService.saveData(dataRole.getId().toString(),dataPermissionDTOList);

        //查询，返回已保存的数据
        dataRoleDTO = BeanUtil.transform(dataRole, DataRoleDTO.class, DataRoleConvert.toVO());
        dataRoleDTO.setId(String.valueOf(dataRole.getId()));
        List<DataPermissionDTO> dataPermissionmList = permissionService.listVOByRoleId(String.valueOf(dataRole.getId()));
        dataRoleDTO.setDataPermissionVOList(dataPermissionmList);

        return ResponseData.success(dataRoleDTO);
    }

    @ApiOperation(value = "更新数据角色",notes = "根据url的id来指定更新对象，并根据传过来的dataRole信息来更新数据角色详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataRoleDTO",value = "数据角色视图对象dataRoleDTO",required = true, dataType = "DataRoleDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataRoleDTO> edit(@RequestBody DataRoleDTO dataRoleDTO){

        String roleId = dataRoleDTO.getId();;

        //该列表中缺少id,需要查询数据库
        List<DataPermissionDTO> dataPermissionDTOList = dataRoleDTO.getDataPermissionVOList();
        dataPermissionDTOList = permissionService.listVOByCodeList(dataPermissionDTOList);

        DataRole dataRole = BeanUtil.transform(dataRoleDTO, DataRole.class, DataRoleConvert.toEntity());
        dataRole.setId(Long.valueOf(dataRoleDTO.getId()));

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataRole.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataRole.setModifyTime(new Date());
        roleService.updateData(dataRole);
        dataRoleDTO = BeanUtil.transform(dataRole, DataRoleDTO.class, DataRoleConvert.toVO());

        //先删除数据权限再保存
        rolePermissionService.deleteDataByRoleId(roleId);
        Boolean flag = rolePermissionService.saveData(roleId,dataPermissionDTOList);

        //查询，返回已保存的数据
        List<DataPermissionDTO> dataPermissionmList = permissionService.listVOByRoleId(roleId);
        dataRoleDTO.setDataPermissionVOList(dataPermissionmList);
        dataRoleDTO.setId(roleId);
        return ResponseData.success(dataRoleDTO);
    }

    @ApiOperation(value = "更新数据角色-状态",notes = "根据url的id来指定更新对象，并根据传过来的dataRole信息来更新数据角色详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "数据角色ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态：0 禁用 1 启用", required = true, dataType = "String")
    })
    @PutMapping(value = "/status",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<String> updateStatus(String roleId,String status){


        DataRole dataRole = roleService.getById(Long.valueOf(roleId));
        dataRole.setStatus(status);
        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataRole.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataRole.setModifyTime(new Date());
        dataRole = roleService.updateData(dataRole);
        if(dataRole == null || !status.equals(dataRole.getStatus())){
            return ResponseData.error("状态设置失败");
        }
        return ResponseData.success("状态设置成功");
    }

    @ApiOperation(value = "删除数据角色",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据角色ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        //先删除数据权限再保存
        rolePermissionService.deleteDataByRoleId(id);
        roleService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
