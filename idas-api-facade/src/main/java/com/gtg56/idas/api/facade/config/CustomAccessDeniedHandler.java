package com.gtg56.idas.api.facade.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.http.ResponseData;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class CustomAccessDeniedHandler implements AccessDeniedHandler {
     @Override
     public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, ServletException {
         ResponseData responseData = ResponseData.fail(BizException.ExceptionInfo.of("ACCESS_DENY", "访问未授权"));
         response.setStatus(HttpServletResponse.SC_FORBIDDEN);
         response.setContentType(MediaType.APPLICATION_JSON_VALUE);
//         //添加跨域参数
//         for (String key : CorsFilter.corsHeaders.keySet()) {
//             response.setHeader(key, CorsFilter.corsHeaders.get(key));
//         }
         //
         ObjectMapper objectMapper = new ObjectMapper();
         String resBody = objectMapper.writeValueAsString(responseData);
         PrintWriter printWriter = response.getWriter();
         printWriter.print(resBody);
         printWriter.flush();
         printWriter.close();
     }
 }