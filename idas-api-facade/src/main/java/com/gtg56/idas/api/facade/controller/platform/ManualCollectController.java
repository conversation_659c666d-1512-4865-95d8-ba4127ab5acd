package com.gtg56.idas.api.facade.controller.platform;

import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.service.define.IManualCollectService;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "Platform:ManualCollectController", description = "手动数据采集")
@RestController
@RequestMapping(value = "/api/collect/manual", produces = {MediaType.APPLICATION_JSON_VALUE})
public class ManualCollectController {

    @Resource
    private IManualCollectService manualCollectService;

    @ApiOperation(value = "手动补充指定日期的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warehouseCode", value = "仓库编码", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "date", value = "日期(格式: yyyy-MM-dd)", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "includeAlarm", value = "是否包含异常报警数据", required = false, dataType = "Boolean", paramType = "body", defaultValue = "true")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success")
    })
    @PostMapping("/supplement")
    public ResponseData supplementData(@RequestBody ManualCollectRequest request,
                                     HttpServletRequest httpRequest, HttpServletResponse response) {
        try {
            // 参数验证
            if (request.getWarehouseCode() == null || request.getWarehouseCode().trim().isEmpty()) {
                return ResponseData.error("仓库编码不能为空");
            }
            
            if (request.getDate() == null || request.getDate().trim().isEmpty()) {
                return ResponseData.error("日期不能为空");
            }
            
            // 验证日期格式
            if (!request.getDate().matches("\\d{4}-\\d{2}-\\d{2}")) {
                return ResponseData.error("日期格式错误，请使用 yyyy-MM-dd 格式");
            }
            
            // 调用服务进行数据补充
            ManualCollectResult result = manualCollectService.supplementData(
                    request.getWarehouseCode(), 
                    request.getDate(), 
                    request.getIncludeAlarm() != null ? request.getIncludeAlarm() : true
            );
            
            return ResponseData.success(result);
            
        } catch (Exception e) {
            return ResponseData.error("数据补充失败: " + e.getMessage());
        }
    }

    /**
     * 手动采集请求对象
     */
    public static class ManualCollectRequest {
        @ApiModelProperty(value = "仓库编码", required = true)
        private String warehouseCode;
        
        @ApiModelProperty(value = "日期(格式: yyyy-MM-dd)", required = true)
        private String date;
        
        @ApiModelProperty(value = "是否包含异常报警数据", required = false)
        private Boolean includeAlarm;

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Boolean getIncludeAlarm() {
            return includeAlarm;
        }

        public void setIncludeAlarm(Boolean includeAlarm) {
            this.includeAlarm = includeAlarm;
        }
    }

    /**
     * 手动采集结果对象
     */
    public static class ManualCollectResult {
        @ApiModelProperty(value = "仓库编码")
        private String warehouseCode;
        
        @ApiModelProperty(value = "采集日期")
        private String date;
        
        @ApiModelProperty(value = "是否包含异常数据")
        private Boolean includeAlarm;
        
        @ApiModelProperty(value = "采集的记录总数")
        private Long totalRecords;
        
        @ApiModelProperty(value = "处理成功的记录数")
        private Long successRecords;
        
        @ApiModelProperty(value = "采集状态")
        private String status;
        
        @ApiModelProperty(value = "消息")
        private String message;

        public ManualCollectResult() {}

        public ManualCollectResult(String warehouseCode, String date, Boolean includeAlarm, 
                                 Long totalRecords, Long successRecords, String status, String message) {
            this.warehouseCode = warehouseCode;
            this.date = date;
            this.includeAlarm = includeAlarm;
            this.totalRecords = totalRecords;
            this.successRecords = successRecords;
            this.status = status;
            this.message = message;
        }

        // Getters and Setters
        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Boolean getIncludeAlarm() {
            return includeAlarm;
        }

        public void setIncludeAlarm(Boolean includeAlarm) {
            this.includeAlarm = includeAlarm;
        }

        public Long getTotalRecords() {
            return totalRecords;
        }

        public void setTotalRecords(Long totalRecords) {
            this.totalRecords = totalRecords;
        }

        public Long getSuccessRecords() {
            return successRecords;
        }

        public void setSuccessRecords(Long successRecords) {
            this.successRecords = successRecords;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
