package com.gtg56.idas.api.facade.controller.platform;

import com.gtg56.idas.common.entity.dto.CollectorStatusDTO;
import com.gtg56.idas.common.entity.http.ResponseData;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Platform:HeartBeatController", description = "心跳。供负载均衡调用")
@RestController
@RequestMapping(value = "/api/heartbeat", produces = {MediaType.APPLICATION_JSON_VALUE})
public class HeartBeatController {
    
    @ApiOperation(value = "心跳")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = CollectorStatusDTO.class)
    })
    @GetMapping("/")
    public ResponseData heartbeat() {
        return ResponseData.success("I'm alive!");
    }
}
