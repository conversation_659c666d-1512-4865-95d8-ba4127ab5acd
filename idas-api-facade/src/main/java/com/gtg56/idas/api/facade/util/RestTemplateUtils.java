package com.gtg56.idas.api.facade.util;

import org.apache.commons.io.IOUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.*;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Author: czl
 * @Date: 2021/1/4 16:02
 */
public class RestTemplateUtils {

    private final static Logger logger = LoggerFactory.getLogger(RestTemplateUtils.class);

    private static RestTemplate restTemplateForNet;
    private static RestTemplate restTemplate;

    static {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = getHttpRequestFactory();

        RestTemplate template = new RestTemplate(httpRequestFactory);
        addNetMessageConvert(template);
        addLogInterceptor(template);
        restTemplateForNet = template;

        restTemplate = new RestTemplate(httpRequestFactory);
        addLogInterceptor(restTemplate);
    }

    public static void addLogInterceptor(RestTemplate template) {
        List<ClientHttpRequestInterceptor> interceptors = template.getInterceptors();
        LogClientHttpRequestInterceptor logClientHttpRequestInterceptor = new LogClientHttpRequestInterceptor();
        interceptors.add(logClientHttpRequestInterceptor);
    }

    public static void addResponseInterceptor(RestTemplate template, Consumer<String> action) {
        List<ClientHttpRequestInterceptor> interceptors = template.getInterceptors();
        ResponseHandlerClientHttpRequestInterceptor responseHandlerClientHttpRequestInterceptor = new ResponseHandlerClientHttpRequestInterceptor(action);
        interceptors.add(responseHandlerClientHttpRequestInterceptor);
    }

    public static void addNetMessageConvert(RestTemplate template) {
//        List<HttpMessageConverter<?>> messageConverters = template.getMessageConverters();
//        messageConverters.removeIf(x -> x.getSupportedMediaTypes().contains(MediaType.APPLICATION_JSON));
//        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
//        fastJsonHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON));
//        fastJsonHttpMessageConverter.getFastJsonConfig().setSerializeFilters(new PascalNameFilter());
//        messageConverters.add(fastJsonHttpMessageConverter);
    }

    public static HttpComponentsClientHttpRequestFactory getHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(1 * 1000);   //从连接池获取连接时间
        httpRequestFactory.setConnectTimeout(2 * 1000);              //建立连接时间
        httpRequestFactory.setReadTimeout(130 * 1000);                //整个调用的时间
        HttpClient httpClient = HttpClientBuilder.create().disableCookieManagement().disableRedirectHandling().build(); //禁止重定向
        httpRequestFactory.setHttpClient(httpClient);
        return httpRequestFactory;
    }

    public static RestTemplate getRestTemplateForNet() {
        return restTemplateForNet;
    }

    public static RestTemplate getRestTemplate() {
        return restTemplate;
    }

    public static class LogClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {
        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

            StringBuilder headerStringBuilder = new StringBuilder();
            request.getHeaders().entrySet().forEach(x -> {
                headerStringBuilder.append(x.getKey());
                headerStringBuilder.append(":");
                headerStringBuilder.append(x.getValue());
                headerStringBuilder.append(";");
            });

            long startTime = System.currentTimeMillis();
            ClientHttpResponseWrapper clientHttpResponseWrapper = null;

            String responseBody = "";
            String message = "";
            try {
                ClientHttpResponse response = execution.execute(request, body);

                clientHttpResponseWrapper = new ClientHttpResponseWrapper(response);

                responseBody = clientHttpResponseWrapper.getBodyAsString();
            } catch (Exception ex) {
                responseBody = ex.toString();
                throw ex;
            } finally {
                message = String.format("duration:%s url:%s requestHeaders:%s requestBody:%s responseBody:%s",
                        (System.currentTimeMillis() - startTime) + "ms", request.getURI().toString(), headerStringBuilder.toString(), new String(body), responseBody);
                logger.info(message);
            }

            return clientHttpResponseWrapper;
        }
    }

    public static class ClientHttpResponseWrapper extends AbstractClientHttpResponse {

        ClientHttpResponse clientHttpResponse;

        private final byte[] bytes;

        public ClientHttpResponseWrapper(ClientHttpResponse response) throws IOException {
            clientHttpResponse = response;
            bytes = IOUtils.toByteArray(response.getBody());
        }

        public String getBodyAsString() throws UnsupportedEncodingException {
            return new String(bytes, "utf8");
        }

        @Override
        public int getRawStatusCode() throws IOException {
            return clientHttpResponse.getRawStatusCode();
        }

        @Override
        public String getStatusText() throws IOException {
            return clientHttpResponse.getStatusText();
        }

        @Override
        public void close() {
            clientHttpResponse.close();
        }

        @Override
        public InputStream getBody() throws IOException {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
            return byteArrayInputStream;
        }

        @Override
        public HttpHeaders getHeaders() {
            return clientHttpResponse.getHeaders();
        }
    }


    public static class ResponseHandlerClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {

        Consumer<String> action;

        public ResponseHandlerClientHttpRequestInterceptor(Consumer<String> action) {
            this.action = action;
        }

        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
            ClientHttpResponse response = execution.execute(request, body);
            ClientHttpResponseWrapper clientHttpResponseWrapper = new ClientHttpResponseWrapper(response);
            String responseBody = clientHttpResponseWrapper.getBodyAsString();
            action.accept(responseBody);
            return clientHttpResponseWrapper;
        }
    }
}
