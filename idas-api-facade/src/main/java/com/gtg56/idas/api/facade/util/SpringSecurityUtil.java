package com.gtg56.idas.api.facade.util;

import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

import java.util.stream.Collectors;

public class SpringSecurityUtil {
    public static UserDTO getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        try {
            UsernamePasswordAuthenticationToken userDetails = (UsernamePasswordAuthenticationToken) ((OAuth2Authentication) authentication).getUserAuthentication();
            UserDTO userDTO = JSONObject.parseObject(authentication.getPrincipal().toString(), UserDTO.class);
            userDTO.setAuthorities(
                    userDetails.getAuthorities()
                            .stream()
                            .map(GrantedAuthority::getAuthority)
                            .collect(Collectors.toList())
            );
            return userDTO;
        } catch (RuntimeException e) {
            return null;
        }
    }
}
