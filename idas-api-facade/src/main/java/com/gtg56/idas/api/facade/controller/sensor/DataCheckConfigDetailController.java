package com.gtg56.idas.api.facade.controller.sensor;


import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.ICheckConfigDetailService;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import com.gtg56.idas.common.convert.DataCheckConfigDetailConvert;
import com.gtg56.idas.common.entity.query.DataCheckConfigDetailQuery;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RestController;
import com.gtg56.lark.web.BaseController;

/**
 * <p>
 * 数据完整性检测配置明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Api(tags = "Biz:DataCheckConfigDetailController",description = "数据完整性检测配置明细表管理功能")
@RestController
@RequestMapping("/api/dataCheckConfigDetails")
public class DataCheckConfigDetailController extends BaseController {

    @Autowired
    private ICheckConfigDetailService checkConfigDetailService;

    @ApiOperation(value="通过query对象获取数据完整性检测配置明细表列表", notes="分页获取数据完整性检测配置明细表列表。处理/dataCheckConfigDetail/的GET请求，用来获取数据完整性检测配置明细表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataCheckConfigDetailQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckConfigDetailDTO>> list(DataCheckConfigDetailQuery query,
                                    String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export,
                                    HttpServletRequest request, HttpServletResponse response){
        PageObject<DataCheckConfigDetail> pageObject = checkConfigDetailService.pageByQuery(query, initPageParams());
        PageObject<DataCheckConfigDetailDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据完整性检测配置明细表列表", notes="分页获取数据完整性检测配置明细表列表。处理/dataCheckConfigDetail/的GET请求，用来获取数据完整性检测配置明细表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckConfigId", value = "数据完整性检测配置ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dataPermissionId", value = "数据权限ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "creatorId", value = "创建用户编号 表：ba_user", required = false, dataType = "String"),
            @ApiImplicitParam(name = "createTimeStart", value = "创建时间", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckConfigDetailDTO>> listByMap(String searchKeyword,
                                    String dataCheckConfigId,
                                    String dataPermissionId,
                                    String creatorId,
                                    String createTimeStart,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("dataCheckConfigId", dataCheckConfigId);
        params.put("dataPermissionId", dataPermissionId);
        params.put("creatorId", creatorId);
        params.put("createTimeStart", createTimeStart);
        PageObject<DataCheckConfigDetail> pageObject = checkConfigDetailService.pageByMap(params, initPageParams());
        PageObject<DataCheckConfigDetailDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value = "查询数据完整性检测配置明细表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据完整性检测配置明细表详细信息")
    @ApiImplicitParam(name="ids",value = "数据完整性检测配置明细表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataCheckConfigDetailDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataCheckConfigDetailDTO> dataCheckConfigDetailDTOList = BeanUtil.transformList((checkConfigDetailService.listByIds(ids)), DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dataCheckConfigDetailDTOList);
    }

    @ApiOperation(value = "查询数据完整性检测配置明细表",notes = "根据url的ID查询数据完整性检测配置明细表详细信息")
    @ApiImplicitParam(name="id",value = "数据完整性检测配置明细表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataCheckConfigDetailDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataCheckConfigDetailDTO dataCheckConfigDetailDTO = BeanUtil.transform(checkConfigDetailService.getById(Long.valueOf(id)), DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dataCheckConfigDetailDTO);
    }

    @ApiOperation(value = "创建数据完整性检测配置明细表",notes = "权限：system:dataCheckConfigDetail:add。根据DataCheckConfigDetail对象创建数据完整性检测配置明细表,处理/dataCheckConfigDetail/的POST请求，用来创建DataCheckConfigDetail。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataCheckConfigDetailDTO",value = "数据完整性检测配置明细表视图对象dataCheckConfigDetail",required = true, dataType = "DataCheckConfigDetailDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckConfigDetailDTO> add(@RequestBody DataCheckConfigDetailDTO dataCheckConfigDetailDTO){
        DataCheckConfigDetail dataCheckConfigDetail = BeanUtil.transform(dataCheckConfigDetailDTO, DataCheckConfigDetail.class, DataCheckConfigDetailConvert.toEntity());

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataCheckConfigDetail.setCreatorId(Long.valueOf(currentUser.getId()));
            dataCheckConfigDetail.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataCheckConfigDetail = checkConfigDetailService.saveData(dataCheckConfigDetail);
        dataCheckConfigDetailDTO = BeanUtil.transform(dataCheckConfigDetail, DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dataCheckConfigDetailDTO);
    }

    @ApiOperation(value = "更新数据完整性检测配置明细表",notes = "根据url的id来指定更新对象，并根据传过来的dataCheckConfigDetail信息来更新数据完整性检测配置明细表详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckConfigDetailDTO",value = "数据完整性检测配置明细表视图对象dataCheckConfigDetailDTO",required = true, dataType = "DataCheckConfigDetailDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckConfigDetailDTO> edit(@RequestBody DataCheckConfigDetailDTO dataCheckConfigDetailDTO){
        DataCheckConfigDetail dataCheckConfigDetail = BeanUtil.transform(dataCheckConfigDetailDTO, DataCheckConfigDetail.class, DataCheckConfigDetailConvert.toEntity());
        checkConfigDetailService.updateData(dataCheckConfigDetail);
        dataCheckConfigDetailDTO = BeanUtil.transform(dataCheckConfigDetail, DataCheckConfigDetailDTO.class, DataCheckConfigDetailConvert.toVO());
        return ResponseData.success(dataCheckConfigDetailDTO);
    }

    @ApiOperation(value = "删除数据完整性检测配置明细表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据完整性检测配置明细表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        checkConfigDetailService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
