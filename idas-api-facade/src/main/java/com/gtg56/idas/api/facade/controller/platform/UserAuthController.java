package com.gtg56.idas.api.facade.controller.platform;

import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.entity.query.UserAuthQuery;
import com.gtg56.idas.common.util.AssertUtil;
import com.gtg56.idas.service.define.IAuthService;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "Platform:UserAuthController", description = "用户授权-接口")
@RestController
@RequestMapping(value = "/api/auth", produces = {MediaType.APPLICATION_JSON_VALUE})
public class UserAuthController {
    
    @Resource
    private IAuthService authService;
    
    @ApiOperation(value = "创建用户授权")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = UserAuth.class)
    })
    @PostMapping("/")
    public ResponseData create(@RequestBody UserAuth userAuth,
                               HttpServletRequest request, HttpServletResponse response) {
        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        AssertUtil.notNull(currentUser, "无法获取当前用户");
        return ResponseData.success(authService.create(userAuth, currentUser));
    }
    
    @ApiOperation(value = "修改用户授权")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = UserAuth.class)
    })
    @PostMapping("/edit")
    public ResponseData edit(@RequestBody UserAuth userAuth,
                             HttpServletRequest request, HttpServletResponse response) {
        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        AssertUtil.notNull(currentUser, "无法获取当前用户");
        return ResponseData.success(authService.edit(userAuth, currentUser));
    }
    
    @ApiOperation(value = "查询用户授权")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = UserAuth.class)
    })
    @PostMapping("/find")
    public ResponseData find(@RequestBody UserAuthQuery userAuthQuery,
                             HttpServletRequest request, HttpServletResponse response) {
        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        AssertUtil.notNull(currentUser, "无法获取当前用户");
        return ResponseData.success(authService.find(userAuthQuery));
    }
}
