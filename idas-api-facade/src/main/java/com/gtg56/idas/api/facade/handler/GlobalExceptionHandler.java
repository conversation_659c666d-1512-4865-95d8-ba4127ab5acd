package com.gtg56.idas.api.facade.handler;

import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.http.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(value = Exception.class)
    public ResponseData handleException(Exception e, HttpServletRequest request, HandlerMethod handlerMethod) {
        if (e instanceof BizException) return handleBizException((BizException) e, request, handlerMethod);
        
        List<Throwable> throwableList = ExceptionUtils.getThrowableList(e);
        for (Throwable t : throwableList) {
            if (t instanceof BizException)
                return handleBizException((BizException) t, request, handlerMethod);
        }
        
        if (e instanceof HttpRequestMethodNotSupportedException)
            return handleWrongRequestMethodException((HttpRequestMethodNotSupportedException) e, request, handlerMethod);
        
        if (e instanceof RpcException) {
            return handleRpcException((RpcException) e, request, handlerMethod);
        }
        
        log.warn("全局异常拦截器拦截到服务异常", e);
        return ResponseData.fail(e);
    }
    
    @ExceptionHandler(value = BizException.class)
    public ResponseData handleBizException(BizException e,
                                           HttpServletRequest request,
                                           HandlerMethod handlerMethod) {
        BizException.ExceptionInfo exceptionInfo = e.getExceptionInfo();
        if (exceptionInfo != null) {
            String msg = String.format("code[%s] , msg[%s]", exceptionInfo.getCode(), exceptionInfo.getMessage());
            log.debug("全局异常拦截器拦截到业务异常 {}", msg);
            return ResponseData.fail(exceptionInfo);
        }
        log.debug("全局异常拦截器拦截到业务异常", e);
        return ResponseData.fail(e);
    }
    
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public ResponseData handleWrongRequestMethodException(HttpRequestMethodNotSupportedException e,
                                                          HttpServletRequest request,
                                                          HandlerMethod handlerMethod) {
        log.warn("全局异常拦截器拦截到错误请求方法异常", e);
        return ResponseData.fail(new BizException(e));
    }
    
    @ExceptionHandler(value = RpcException.class)
    public ResponseData handleRpcException(RpcException e,
                                           HttpServletRequest request,
                                           HandlerMethod handlerMethod) {
        String msg = e.getLocalizedMessage();
        Pattern pattern = Pattern.compile("(com\\.gtg56\\.idas\\.service\\.define\\.I[a-zA-Z]{2,64}Service)");
        Matcher matcher = pattern.matcher(msg);
        log.warn("全局异常拦截器拦截到dubbo微服务异常", e);
        if (matcher.find()) {
            String serviceClassName = matcher.group();
            try {
                Class<?> clz = Class.forName(serviceClassName);
                return ResponseData.fail(
                        BizException.ExceptionInfo.of(ResponseData.CODE_FAIL_SERVER, "微服务异常 【" + clz.getSimpleName() + "】无法访问")
                );
            } catch (ClassNotFoundException ex) {
                return ResponseData.fail(
                        BizException.ExceptionInfo.of(ResponseData.CODE_FAIL_SERVER, "微服务异常 未知服务【" + serviceClassName + "】无法访问")
                );
            }
    
    
        } else {
            return ResponseData.fail(e);
        }
    }
}
