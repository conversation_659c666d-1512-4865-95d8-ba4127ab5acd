package com.gtg56.idas.api.facade.controller.platform;


import com.gtg56.idas.common.convert.DataRoleUserConvert;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.dto.auth.DataRoleDTO;
import com.gtg56.idas.common.entity.dto.auth.DataRoleUserDTO;
import com.gtg56.idas.common.entity.jdbc.DataRoleUser;
import com.gtg56.idas.common.entity.query.DataRoleUserQuery;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.IPermissionService;
import com.gtg56.idas.service.define.IRoleUserService;
import com.gtg56.lark.web.BaseController;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户数据角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Api(tags = "Platform:DataRoleUserController",description = "用户数据角色表管理功能")
@RestController
@RequestMapping("/api/dataRoleUsers")
public class DataRoleUserController extends BaseController {

    @Resource
    private IRoleUserService roleUserService;

    @Resource
    private IPermissionService permissionService;

    @ApiOperation(value="通过query对象获取用户数据角色表列表", notes="分页获取用户数据角色表列表。处理/dataRoleUser/的GET请求，用来获取用户数据角色表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataRoleUserQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRoleUserDTO>> list(DataRoleUserQuery query,
                                                          String searchKeyword,
                                                          @RequestParam(defaultValue="false")  boolean export,
                                                          HttpServletRequest request, HttpServletResponse response){
        PageObject<DataRoleUser> pageObject = roleUserService.pageByQuery(query, initPageParams());
        PageObject<DataRoleUserDTO> voPageObject = BeanUtil.transformPage(pageObject, DataRoleUserDTO.class, DataRoleUserConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value="通过前端参数获取用户数据角色表列表", notes="分页获取用户数据角色表列表。处理/dataRoleUser/的GET请求，用来获取用户数据角色表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRoleUserDTO>> listByMap(String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        PageObject<DataRoleUser> pageObject = roleUserService.pageByMap(params, initPageParams());
        PageObject<DataRoleUserDTO> voPageObject = BeanUtil.transformPage(pageObject, DataRoleUserDTO.class, DataRoleUserConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value = "查询用户数据角色表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询用户数据角色表详细信息")
    @ApiImplicitParam(name="ids",value = "用户数据角色表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataRoleUserDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataRoleUserDTO> DataRoleUserDTOList = BeanUtil.transformList(roleUserService.listByIds(Arrays.asList(ids.split(","))), DataRoleUserDTO.class, DataRoleUserConvert.toVO());
        return ResponseData.success(DataRoleUserDTOList);
    }

    @ApiOperation(value = "查询用户数据角色表",notes = "根据url的ID查询用户数据角色表详细信息")
    @ApiImplicitParam(name="id",value = "用户数据角色表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataRoleUserDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataRoleUserDTO dataRoleUserDTO = BeanUtil.transform(roleUserService.getById(Long.valueOf(id)), DataRoleUserDTO.class, DataRoleUserConvert.toVO());
        dataRoleUserDTO.setId(id);
        return ResponseData.success(dataRoleUserDTO);
    }

    @ApiOperation(value = "通过用户ID查询用户数据角色表",notes = "根据url的USER_ID查询用户数据角色列表")
    @ApiImplicitParam(name="userId",value = "用户ID",required = true, dataType = "String")
    @GetMapping(value = "/byUserId/{userId}")
    public ResponseData<List<DataRoleUserDTO>> listVOByUserId(@PathVariable("userId") String userId){
        if(StringUtil.isBlank(userId)) {
            return ResponseData.error("ID为空,查询失败");
        }
        List<DataRoleUserDTO> DataRoleUserDTOList = roleUserService.listVOByUserId(userId);

        List<DataPermissionDTO> dataPermissionmVOList = permissionService.listVOByUserId(userId);
//        System.out.println("dataPermissionmVOList==="+dataPermissionmVOList.size());


        return ResponseData.success(DataRoleUserDTOList);
    }

    @ApiOperation(value = "创建用户-数据角色表",notes = "权限：system:dataRoleUser:add。根据DataRoleUser对象创建用户数据角色表,处理/dataRoleUser/的POST请求，用来创建DataRoleUser。请求时：contentType:application/json;charset=UTF-8")

    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dataRoleDTOlist",value = "数据角色列表dataRole",required = true, dataType = "List")
    })
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<List<DataRoleUserDTO>> add(String userId,@RequestBody List<DataRoleDTO>  dataRoleDTOlist){

        if (StringUtil.isNotEmpty(userId) && CollectionUtils.isNotEmpty(dataRoleDTOlist)){

            //先删除再插入
            roleUserService.deleteByUserId(userId);

            dataRoleDTOlist.stream().forEach(dataRoleDTO -> {
                DataRoleUser dataRoleUser = new DataRoleUser();
                dataRoleUser.setDataRoleId(dataRoleDTO.getId());
                dataRoleUser.setUserId(userId);
                roleUserService.saveData(dataRoleUser);
            });
        }
        List<DataRoleUserDTO> DataRoleUserDTOList = roleUserService.listVOByUserId(userId);

        return ResponseData.success(DataRoleUserDTOList);
    }

    @ApiOperation(value = "更新用户-数据角色表",notes = "根据user_id和数据角色列表来更新对象，并根据传过来的userid和数据角色列表来更新用户数据角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dataRoleDTOlist",value = "数据角色列表dataRole",required = true, dataType = "List")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataRoleUserDTO> edit(String userId,@RequestBody List<DataRoleDTO> dataRoleDTOlist){
        if (StringUtil.isNotEmpty(userId) && CollectionUtils.isNotEmpty(dataRoleDTOlist)){

            //先删除再插入
            roleUserService.deleteByUserId(userId);

            dataRoleDTOlist.stream().forEach(dataRoleDTO -> {
                DataRoleUser dataRoleUser = new DataRoleUser();
                dataRoleUser.setDataRoleId(dataRoleDTO.getId());
                dataRoleUser.setUserId(userId);
                roleUserService.saveData(dataRoleUser);
            });
        }
        List<DataRoleUserDTO> DataRoleUserDTOList = roleUserService.listVOByUserId(userId);

        return ResponseData.success(DataRoleUserDTOList);
    }

    @ApiOperation(value = "删除用户数据角色表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "用户数据角色表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        roleUserService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
