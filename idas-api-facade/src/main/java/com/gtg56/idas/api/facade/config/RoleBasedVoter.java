package com.gtg56.idas.api.facade.config;

import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.service.define.IAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RoleBasedVoter implements AccessDecisionVoter<Object> {

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public int vote(Authentication authentication, Object object, Collection<ConfigAttribute> configAttributes) {
        if (authentication == null) {
            return ACCESS_DENIED;
        }

        if (authentication instanceof AnonymousAuthenticationToken) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

            IAuthService authService = SpringUtil.getBean(IAuthService.class);
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                headers.put(name, request.getHeader(name));
            }

            if (authService.checkGNCApiInvoke(headers)) {
                return ACCESS_GRANTED;
            } else {
                return ACCESS_DENIED;
            }
        }

//        int result = ACCESS_ABSTAIN;
//        Collection<? extends GrantedAuthority> authorities = extractAuthorities(authentication);
//
//        for (ConfigAttribute configAttribute : configAttributes) {
//            if (configAttribute.getAttribute()==null) {
//                continue;
//            }
//            if (this.supports(configAttribute)) {
//                result = ACCESS_DENIED;
//
//                // Attempt to find a matching granted authority
//                for (GrantedAuthority authority : authorities) {
//                    if (configAttribute.getAttribute().equals(authority.getAuthority())) {
//                        return ACCESS_GRANTED;
//                    }
//                }
//            }
//        }
//        log.info("=================================" + result);
        return ACCESS_GRANTED;
    }

    Collection<? extends GrantedAuthority> extractAuthorities(
            Authentication authentication) {
        return authentication.getAuthorities();
    }

    @Override
    public boolean supports(Class clazz) {
        return true;
    }
}