package com.gtg56.idas.api.facade.controller.platform;

import com.gtg56.idas.common.entity.dto.CollectorStatusDTO;
import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.common.entity.query.RunningCollectorQuery;
import com.gtg56.idas.service.define.ICollectService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "Platform:CollectController",description = "采集器")
@RestController
@RequestMapping(value = "/api/collect",produces = {MediaType.APPLICATION_JSON_VALUE})
public class CollectController {
    
    @Resource
    private ICollectService collectService;
    
    @ApiOperation(value = "查询运行中的采集器")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = CollectorStatusDTO.class)
    })
    @GetMapping("/running")
    public ResponseData listRunning(RunningCollectorQuery query,
                                    HttpServletRequest request, HttpServletResponse response) {
        try {
            boolean filterCollectorName = StringUtils.isNotBlank(query.getCollectorNameKeyword());
            boolean filterHost = StringUtils.isNotBlank(query.getHost());
            boolean filterLeader = query.getOnlyLeader() != null;
            List<CollectorStatusDTO> res = collectService.listCollectors().stream()
                    .filter(dto -> {
                        if (filterCollectorName) {
                            return dto.getCollectorName().contains(query.getCollectorNameKeyword());
                        }
                        return true;
                    })
                    .filter(dto -> {
                        if (filterHost) return query.getHost().equals(dto.getHost());
                        return true;
                    })
                    .filter(dto -> {
                        if (filterLeader) return dto.getLeader().equals(query.getOnlyLeader());
                        return true;
                    })
                    .sorted(Comparator.comparing(CollectorStatusDTO::getCollectorName))
                    .collect(Collectors.toList());
            
            
            return ResponseData.success(res);
        } catch (Exception e) {
            return ResponseData.success(Collections.emptyList());
        }
    }
}
