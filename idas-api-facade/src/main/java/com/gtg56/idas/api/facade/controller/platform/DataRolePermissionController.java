package com.gtg56.idas.api.facade.controller.platform;


import com.gtg56.idas.common.convert.DataRolePermissionConvert;
import com.gtg56.idas.common.entity.dto.auth.DataRolePermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataRolePermission;
import com.gtg56.idas.common.entity.query.DataRolePermissionQuery;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.IPermissionService;
import com.gtg56.idas.service.define.IRolePermissionService;
import com.gtg56.lark.web.BaseController;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据角色权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Api(tags = "Platform:DataRolePermissionController",description = "数据角色权限表管理功能")
@RestController
@RequestMapping("/api/dataRolePermissions")
public class DataRolePermissionController extends BaseController {

    @Resource
    private IRolePermissionService rolePermissionService;

    @ApiOperation(value="通过query对象获取数据角色权限表列表", notes="分页获取数据角色权限表列表。处理/dataRolePermission/的GET请求，用来获取数据角色权限表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataRolePermissionQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRolePermissionDTO>> list(DataRolePermissionQuery query,
                                                                String searchKeyword,
                                                                @RequestParam(defaultValue="false")  boolean export,
                                                                HttpServletRequest request, HttpServletResponse response){
        PageObject<DataRolePermission> pageObject = rolePermissionService.pageByQuery(query, initPageParams());
        PageObject<DataRolePermissionDTO> voPageObject = BeanUtil.transformPage(pageObject, DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据角色权限表列表", notes="分页获取数据角色权限表列表。处理/dataRolePermission/的GET请求，用来获取数据角色权限表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataRolePermissionDTO>> listByMap(String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        PageObject<DataRolePermission> pageObject = rolePermissionService.pageByMap(params, initPageParams());
        PageObject<DataRolePermissionDTO> voPageObject = BeanUtil.transformPage(pageObject, DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value = "查询数据角色权限表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据角色权限表详细信息")
    @ApiImplicitParam(name="ids",value = "数据角色权限表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataRolePermissionDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataRolePermissionDTO> DataRolePermissionDTOList = BeanUtil.transformList(rolePermissionService.listByIds(Arrays.asList(ids.split(","))), DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        return ResponseData.success(DataRolePermissionDTOList);
    }

    @ApiOperation(value = "查询数据角色权限表",notes = "根据url的ID查询数据角色权限表详细信息")
    @ApiImplicitParam(name="id",value = "数据角色权限表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataRolePermissionDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataRolePermissionDTO dataRolePermissionDTO = BeanUtil.transform(rolePermissionService.getById(Long.valueOf(id)), DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        dataRolePermissionDTO.setId(id);
        return ResponseData.success(dataRolePermissionDTO);
    }

    @ApiOperation(value = "创建数据角色权限表",notes = "权限：system:dataRolePermission:add。根据DataRolePermission对象创建数据角色权限表,处理/dataRolePermission/的POST请求，用来创建DataRolePermission。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "DataRolePermissionDTO",value = "数据角色权限表视图对象dataRolePermission",required = true, dataType = "DataRolePermissionDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataRolePermissionDTO> add(@RequestBody DataRolePermissionDTO DataRolePermissionDTO){
        DataRolePermission dataRolePermission = BeanUtil.transform(DataRolePermissionDTO, DataRolePermission.class, DataRolePermissionConvert.toEntity());
        dataRolePermission = rolePermissionService.saveData(dataRolePermission);
        DataRolePermissionDTO = BeanUtil.transform(dataRolePermission, DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        return ResponseData.success(DataRolePermissionDTO);
    }

    @ApiOperation(value = "更新数据角色权限表",notes = "根据url的id来指定更新对象，并根据传过来的dataRolePermission信息来更新数据角色权限表详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DataRolePermissionDTO",value = "数据角色权限表视图对象DataRolePermissionDTO",required = true, dataType = "DataRolePermissionDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataRolePermissionDTO> edit(@RequestBody DataRolePermissionDTO DataRolePermissionDTO){
        DataRolePermission dataRolePermission = BeanUtil.transform(DataRolePermissionDTO, DataRolePermission.class, DataRolePermissionConvert.toEntity());
        rolePermissionService.updateData(dataRolePermission);
        DataRolePermissionDTO = BeanUtil.transform(dataRolePermission, DataRolePermissionDTO.class, DataRolePermissionConvert.toVO());
        return ResponseData.success(DataRolePermissionDTO);
    }

    @ApiOperation(value = "删除数据角色权限表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据角色权限表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        rolePermissionService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
