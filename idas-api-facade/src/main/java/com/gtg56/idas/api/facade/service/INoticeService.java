package com.gtg56.idas.api.facade.service;


import com.gtg56.idas.api.facade.vo.DeployApplyCardVO;
import com.gtg56.idas.api.facade.vo.NoticeSettingVO;

/**
 * @Author: czl
 * @Date: 2021/6/17 10:40
 */
public interface  INoticeService {
    void reportDeployMessage(String title, String message, String env, String url);

    Boolean reportDeployMessage(NoticeSettingVO noticeSettingVO, String title, String message, String url);


    Boolean sendDeployApplyCard(DeployApplyCardVO deployApplyCardVO) throws Exception;

    Boolean updateDeployApplyCard(DeployApplyCardVO deployApplyCardVO) throws Exception;

}
