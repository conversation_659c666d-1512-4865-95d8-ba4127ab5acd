package com.gtg56.idas.api.facade.controller.sensor;


import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.ICheckResultDetailService;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import com.gtg56.idas.common.convert.DataCheckResultDetailConvert;
import com.gtg56.idas.common.entity.query.DataCheckResultDetailQuery;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RestController;
import com.gtg56.lark.web.BaseController;

/**
 * <p>
 * 数据完整性检测结果明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Api(tags = "Biz:DataCheckResultDetailController",description = "数据完整性检测结果明细表管理功能")
@RestController
@RequestMapping("/api/dataCheckResultDetails")
public class DataCheckResultDetailController extends BaseController {

    @Autowired
    private ICheckResultDetailService checkResultDetailService;

    @ApiOperation(value="通过query对象获取数据完整性检测结果明细表列表", notes="分页获取数据完整性检测结果明细表列表。处理/dataCheckResultDetail/的GET请求，用来获取数据完整性检测结果明细表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataCheckResultDetailQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckResultDetailDTO>> list(DataCheckResultDetailQuery query,
                                    String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export,
                                    HttpServletRequest request, HttpServletResponse response){
        PageObject<DataCheckResultDetail> pageObject = checkResultDetailService.pageByQuery(query, initPageParams());
        PageObject<DataCheckResultDetailDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据完整性检测结果明细表列表", notes="分页获取数据完整性检测结果明细表列表。处理/dataCheckResultDetail/的GET请求，用来获取数据完整性检测结果明细表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tradeDateStart", value = "数据日期", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "tradeDateEnd", value = "数据日期", required = false, dataType = "Date"),
            @ApiImplicitParam(name = "dataCheckConfig", value = "数据完整性检测配置表ID", required = false, dataType = "Long"),
            @ApiImplicitParam(name = "dataPermissionId", value = "数据权限ID", required = false, dataType = "String"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckResultDetailDTO>> listByMap(String searchKeyword,
                                    String tradeDateStart,
                                    String tradeDateEnd,
                                    String dataCheckConfig,
                                    String dataPermissionId,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("tradeDateStart", tradeDateStart);
        params.put("tradeDateEnd", tradeDateEnd);
        params.put("dataCheckConfig", dataCheckConfig);
        params.put("dataPermissionId", dataPermissionId);
        PageObject<DataCheckResultDetail> pageObject = checkResultDetailService.pageByMap(params, initPageParams());
        PageObject<DataCheckResultDetailDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value = "查询数据完整性检测结果明细表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据完整性检测结果明细表详细信息")
    @ApiImplicitParam(name="ids",value = "数据完整性检测结果明细表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataCheckResultDetailDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataCheckResultDetailDTO> dataCheckResultDetailDTOList = BeanUtil.transformList((checkResultDetailService.listByIds(ids)), DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dataCheckResultDetailDTOList);
    }

    @ApiOperation(value = "查询数据完整性检测结果明细表",notes = "根据url的ID查询数据完整性检测结果明细表详细信息")
    @ApiImplicitParam(name="id",value = "数据完整性检测结果明细表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataCheckResultDetailDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataCheckResultDetailDTO dataCheckResultDetailDTO = BeanUtil.transform(checkResultDetailService.getById(Long.valueOf(id)), DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dataCheckResultDetailDTO);
    }

    @ApiOperation(value = "创建数据完整性检测结果明细表",notes = "权限：system:dataCheckResultDetail:add。根据DataCheckResultDetail对象创建数据完整性检测结果明细表,处理/dataCheckResultDetail/的POST请求，用来创建DataCheckResultDetail。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataCheckResultDetailDTO",value = "数据完整性检测结果明细表视图对象dataCheckResultDetail",required = true, dataType = "DataCheckResultDetailDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckResultDetailDTO> add(@RequestBody DataCheckResultDetailDTO dataCheckResultDetailDTO){
        DataCheckResultDetail dataCheckResultDetail = BeanUtil.transform(dataCheckResultDetailDTO, DataCheckResultDetail.class, DataCheckResultDetailConvert.toEntity());

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataCheckResultDetail.setCreatorId(Long.valueOf(currentUser.getId()));
            dataCheckResultDetail.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataCheckResultDetail = checkResultDetailService.saveData(dataCheckResultDetail);
        dataCheckResultDetailDTO = BeanUtil.transform(dataCheckResultDetail, DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dataCheckResultDetailDTO);
    }

    @ApiOperation(value = "更新数据完整性检测结果明细表",notes = "根据url的id来指定更新对象，并根据传过来的dataCheckResultDetail信息来更新数据完整性检测结果明细表详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckResultDetailDTO",value = "数据完整性检测结果明细表视图对象dataCheckResultDetailDTO",required = true, dataType = "DataCheckResultDetailDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckResultDetailDTO> edit(@RequestBody DataCheckResultDetailDTO dataCheckResultDetailDTO){
        DataCheckResultDetail dataCheckResultDetail = BeanUtil.transform(dataCheckResultDetailDTO, DataCheckResultDetail.class, DataCheckResultDetailConvert.toEntity());
        checkResultDetailService.updateData(dataCheckResultDetail);
        dataCheckResultDetailDTO = BeanUtil.transform(dataCheckResultDetail, DataCheckResultDetailDTO.class, DataCheckResultDetailConvert.toVO());
        return ResponseData.success(dataCheckResultDetailDTO);
    }

    @ApiOperation(value = "删除数据完整性检测结果明细表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据完整性检测结果明细表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        checkResultDetailService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
