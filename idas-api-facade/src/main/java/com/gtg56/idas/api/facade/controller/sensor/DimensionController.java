package com.gtg56.idas.api.facade.controller.sensor;

import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.entity.dto.sensor.WarehouseDTO;
import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.WarehouseSensorQuery;
import com.gtg56.idas.service.define.ISensorService;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Api(tags = "Sensor:DimensionController", description = "传感器维度")
@RestController
@RequestMapping(value = "/api/sensor/dimension", produces = {MediaType.APPLICATION_JSON_VALUE})
public class DimensionController {
    
    @Resource
    private ISensorService sensorService;
    
    @Resource(name = "fda")
    private Map<String, String> fda;
    
    @ApiOperation(value = "查询已对接的仓库")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = WarehouseDTO.class)
    })
    @GetMapping("/warehouse")
    public ResponseData listWarehouse(HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(sensorService.listWarehouse());
    }
    
    @ApiOperation(value = "查询药监局")
    @ApiImplicitParams({
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = WarehouseDTO.class)
    })
    @GetMapping("/fda")
    public ResponseData listFDA(HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(fda);
    }
    
    @ApiOperation(value = "查询传感器")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpCode", value = "厂商编码", dataTypeClass = String.class),
            @ApiImplicitParam(name = "warehouseCode", value = "仓库编码", dataTypeClass = String.class),
            @ApiImplicitParam(name = "regionCode", value = "区域编码", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sensorCode", value = "传感器编码", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sensorType", value = "传感器类型", dataTypeClass = String.class),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = WarehouseSensor.class)
    })
    @PostMapping("/search")
    public ResponseData listSensor(@RequestBody WarehouseSensorQuery query,
                                   HttpServletRequest request, HttpServletResponse response) {
        query.setUser(SpringSecurityUtil.getCurrentUser());
        return ResponseData.success(sensorService.findSensor(query));
    }

    @ApiOperation(value = "修改传感器")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = WarehouseSensor.class)
    })
    @PostMapping("/edit")
    public ResponseData editSensor(@RequestBody WarehouseSensor sensor,
                                   HttpServletRequest request, HttpServletResponse response) {
        sensorService.edit(sensor);
        return ResponseData.success(sensor);
    }

}
