package com.gtg56.idas.api.facade.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gtg56.idas.common.core.exception.BizException;
import com.gtg56.idas.common.entity.http.ResponseData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {
     @Override
     public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
         ResponseData responseData;
         if (StringUtils.startsWith(authException.getMessage(), "Access token expired")) {
             responseData = ResponseData.fail(BizException.ExceptionInfo.of("TOKEN_EXPIRED", "用户登陆已过期"));
         } else {
             responseData = ResponseData.fail(BizException.ExceptionInfo.of("AUTH_FAIL", "用户认证失败"));
         }
         response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
         response.setContentType(MediaType.APPLICATION_JSON_VALUE);
         response.setCharacterEncoding(StandardCharsets.UTF_8.displayName());
//         //添加跨域参数
//         for (String key : CorsFilter.corsHeaders.keySet()) {
//             response.setHeader(key, CorsFilter.corsHeaders.get(key));
//         }

         ObjectMapper objectMapper = new ObjectMapper();
         String resBody = objectMapper.writeValueAsString(responseData);
         PrintWriter printWriter = response.getWriter();
         printWriter.print(resBody);
         printWriter.flush();
         printWriter.close();
     }
 }