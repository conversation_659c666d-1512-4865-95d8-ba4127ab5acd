package com.gtg56.idas.api.facade.service.impl;

import com.gtg56.idas.api.facade.service.IJobExecuteService;
import com.gtg56.idas.api.facade.service.INoticeService;
import com.gtg56.idas.common.convert.DataCheckResultConvert;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.service.define.ICheckConfigService;
import com.gtg56.idas.service.define.ICheckResultService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zcy
 * @Date: 2023/10/25 14:49
 */
@Service
public class JobExecuteServiceImpl implements IJobExecuteService {

    private final static Logger logger = LoggerFactory.getLogger(JobExecuteServiceImpl.class);

    @Autowired
    private ICheckResultService checkResultService;

    @Autowired
    private ICheckConfigService checkConfigService;

    @Autowired
    private INoticeService noticeService;


    @PostConstruct
    public void init() {
    }


    @Override
    @XxlJob("doDataCheckJobHandler")
    public ReturnT<String> doDataCheck(String param) {
        String jobName = param + "-数据完整性检测";
        try {
            List<DataCheckConfig> dataCheckConfigList = checkConfigService.findAll();


            List<DataCheckResult> dataCheckResultListAll = new ArrayList<>();

            for (DataCheckConfig dataCheckConfig:dataCheckConfigList){
                List<DataCheckResult> dataCheckResultList = checkResultService.doCheckData(dataCheckConfig);
                dataCheckResultListAll.addAll(dataCheckResultList);
            }

            int totalQuantity = 0;
            int normalQuantity = 0;
            int unnormalQuantity = 0;

            for(DataCheckResult dataCheckResult : dataCheckResultListAll){
                normalQuantity += dataCheckResult.getNormalQuantity();
                unnormalQuantity += dataCheckResult.getUnnormalQuantity();
            }
            totalQuantity = normalQuantity + unnormalQuantity;

            noticeService.reportDeployMessage("数据完整性检测结果","总检测数量:" + totalQuantity +"\n 正常数量: "+ normalQuantity  +"\n 异常数量: "+  unnormalQuantity ,"","");

            return ReturnT.SUCCESS;
        } catch (Exception ex) {
            logger.error(jobName + "运行异常", ex);
            return ReturnT.FAIL;
        }
    }
}
