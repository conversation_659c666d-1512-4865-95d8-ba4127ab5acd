package com.gtg56.idas.api.facade.controller.platform;


import com.gtg56.idas.common.convert.DataPermissionConvert;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.jdbc.DataPermission;
import com.gtg56.idas.common.entity.query.DataPermissionQuery;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.IPermissionService;
import com.gtg56.lark.web.BaseController;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据权限 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Api(tags = "Platform:DataPermissionController",description = "数据权限管理功能")
@RestController
@RequestMapping("/api/dataPermissions")
public class DataPermissionController extends BaseController {

    @Resource
    private IPermissionService permissionService;

    @ApiOperation(value="通过query对象获取数据权限列表", notes="分页获取数据权限列表。处理/DataPermission/的GET请求，用来获取数据权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataPermissionQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataPermissionDTO>> list(DataPermissionQuery query,
                                                            String searchKeyword,
                                                            @RequestParam(defaultValue="false")  boolean export,
                                                            HttpServletRequest request, HttpServletResponse response){
        PageObject<DataPermission> pageObject = permissionService.pageByQuery(query, initPageParams());
        PageObject<DataPermissionDTO> voPageObject = BeanUtil.transformPage(pageObject, DataPermissionDTO.class, DataPermissionConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据权限列表", notes="分页获取数据权限列表。处理/DataPermission/的GET请求，用来获取数据权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warehouseCode", value = "仓库编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "warehouseName", value = "仓库名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataPermissionDTO>> listByMap(String searchKeyword,
                                    String warehouseCode,
                                    String warehouseName,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("warehouseCode", warehouseCode);
        params.put("warehouseName", warehouseName);
        PageObject<DataPermission> pageObject = permissionService.pageByMap(params, initPageParams());
        PageObject<DataPermissionDTO> voPageObject = BeanUtil.transformPage(pageObject, DataPermissionDTO.class, DataPermissionConvert.toVO());
        return ResponseData.success(voPageObject);
    }

    @ApiOperation(value = "查询数据权限",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据权限详细信息")
    @ApiImplicitParam(name="ids",value = "数据权限ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataPermissionDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataPermissionDTO> dataPermissionDTOList = BeanUtil.transformList(permissionService.listByIds(Arrays.asList(ids.split(","))), DataPermissionDTO.class, DataPermissionConvert.toVO());
        return ResponseData.success(dataPermissionDTOList);
    }

    @ApiOperation(value = "查询数据权限",notes = "根据url的ID查询数据权限详细信息")
    @ApiImplicitParam(name="id",value = "数据权限ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataPermissionDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataPermissionDTO dataPermissionDTO = BeanUtil.transform(permissionService.getById(Long.valueOf(id)), DataPermissionDTO.class, DataPermissionConvert.toVO());
        dataPermissionDTO.setId(id);
        return ResponseData.success(dataPermissionDTO);
    }

    @ApiOperation(value = "查询数据权限列表",notes = "根据url的type查询不同类型数据权限列表")
    @ApiImplicitParam(name="type",value = "权限类型",required = true, dataType = "String")
    @GetMapping(value = "/byType/{type}")
    public ResponseData<DataPermissionDTO> listVOByType(@PathVariable("type") String type){
        if(StringUtil.isBlank(type)) {
            return ResponseData.error("type为空,查询失败");
        }
        List<DataPermissionDTO> DataPermissionDTOList = permissionService.listVOByType(type);

        return ResponseData.success(DataPermissionDTOList);
    }

    @ApiOperation(value = "创建数据权限",notes = "权限：system:DataPermission:add。根据DataPermission对象创建数据权限,处理/DataPermission/的POST请求，用来创建DataPermission。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataPermissionDTO",value = "数据权限视图对象DataPermission",required = true, dataType = "DataPermissionDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataPermissionDTO> add(@RequestBody DataPermissionDTO dataPermissionDTO){
        DataPermission dataPermission = BeanUtil.transform(dataPermissionDTO, DataPermission.class, DataPermissionConvert.toEntity());
        dataPermission = permissionService.saveData(dataPermission);
        dataPermissionDTO = BeanUtil.transform(dataPermission, DataPermissionDTO.class, DataPermissionConvert.toVO());
        return ResponseData.success(dataPermissionDTO);
    }

    @ApiOperation(value = "刷新创建数据权限",notes = "根据warehouse_sensor自动创建新增的数据权限，包括类型为：仓库、库房及测点终端,处理/DataPermission/的POST请求，用来创建DataPermission。请求时：contentType:application/json;charset=UTF-8")
    @PostMapping(value = "/refreshAndCreate",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<String>  refreshAndCreate(){

        boolean flag = permissionService.refreshAndCreate();
        if(flag == false) {
            return ResponseData.error("刷新失败，请联系管理员。");
        }
        return ResponseData.success();
    }

    @ApiOperation(value = "更新数据权限",notes = "根据url的id来指定更新对象，并根据传过来的DataPermission信息来更新数据权限详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataPermissionDTO",value = "数据权限视图对象DataPermissionDTO",required = true, dataType = "DataPermissionDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataPermissionDTO> edit(@RequestBody DataPermissionDTO dataPermissionDTO){
        DataPermission dataPermission = BeanUtil.transform(dataPermissionDTO, DataPermission.class, DataPermissionConvert.toEntity());
        permissionService.updateData(dataPermission);
        dataPermissionDTO = BeanUtil.transform(dataPermission, DataPermissionDTO.class, DataPermissionConvert.toVO());
        return ResponseData.success(dataPermissionDTO);
    }

    @ApiOperation(value = "删除数据权限",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据权限ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }
        permissionService.removeById(Long.valueOf(id));
        return ResponseData.success();
    }
}
