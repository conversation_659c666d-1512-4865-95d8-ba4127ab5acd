package com.gtg56.idas.api.facade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: czl
 * @Date: 2021/6/21 10:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "NoticeSettingVO", description = "通知设置")
public class NoticeSettingVO {

    @ApiModelProperty(value = "通知设置id")
    private  String id;

    @ApiModelProperty(value = "钉钉机器人url")
    private  String DingTalkRobotUrl;

    @ApiModelProperty(value = "钉钉机器人秘钥")
    private  String DingTalkRobotSecret;

    @ApiModelProperty(value = "手机号列表")
    private List<String> atPhoneList;
}
