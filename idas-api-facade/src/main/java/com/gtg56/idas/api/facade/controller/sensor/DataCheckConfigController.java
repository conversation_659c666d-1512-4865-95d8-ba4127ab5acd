package com.gtg56.idas.api.facade.controller.sensor;


import com.gtg56.idas.api.facade.util.SpringSecurityUtil;
import com.gtg56.idas.common.convert.DataCheckConfigConvert;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataRole;
import com.gtg56.idas.common.entity.query.DataCheckConfigQuery;
import com.gtg56.idas.common.service.IDataCheckConfigService;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDTO;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.StringUtil;
import com.gtg56.idas.service.define.ICheckConfigDetailService;
import com.gtg56.idas.service.define.ICheckConfigService;
import com.gtg56.lark.web.BaseController;
import com.gtg56.lark.web.PageObject;
import com.gtg56.lark.web.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据完整性检测配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Api(tags = "Sensor:DataCheckConfigController",description = "数据完整性检测配置表管理功能")
@RestController
@RequestMapping("/api/dataCheckConfigs")
public class DataCheckConfigController extends BaseController {

    @Autowired
    private ICheckConfigService checkConfigService;

    @Autowired
    private ICheckConfigDetailService checkConfigDetailService;

    @ApiOperation(value="通过query对象获取数据完整性检测配置表列表", notes="分页获取数据完整性检测配置表列表。处理/dataCheckConfig/的GET请求，用来获取数据完整性检测配置表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "query", value = "查询类", required = false, dataType = "DataCheckConfigQuery"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckConfigDTO>> list(DataCheckConfigQuery query,
                                    String searchKeyword,
                                    @RequestParam(defaultValue="false")  boolean export,
                                    HttpServletRequest request, HttpServletResponse response){
        PageObject<DataCheckConfig> pageObject = checkConfigService.pageByQuery(query, initPageParams());
        PageObject<DataCheckConfigDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value="通过前端参数获取数据完整性检测配置表列表", notes="分页获取数据完整性检测配置表列表。处理/dataCheckConfig/的GET请求，用来获取数据完整性检测配置表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "配置编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "配置名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "searchKeyword", value = "查询关键字", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页，默认为：1", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数，默认为：20", required = false, dataType = "Integer")
    })
    @GetMapping(value = "/listByMap/pageNum={pageNum}/pageSize={pageSize}")
    public ResponseData<PageObject<DataCheckConfigDTO>> listByMap(String searchKeyword,
                                    String code,
                                    String name,
                                    @RequestParam(defaultValue="false")  boolean export){
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("name", name);
        PageObject<DataCheckConfig> pageObject = checkConfigService.pageByMap(params, initPageParams());
        PageObject<DataCheckConfigDTO> dtoPageObject = BeanUtil.transformPage(pageObject, DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());
        return ResponseData.success(dtoPageObject);
    }

    @ApiOperation(value = "查询数据完整性检测配置表",notes = "根据url的ID字符串（可以为多个,逗号隔开）查询数据完整性检测配置表详细信息")
    @ApiImplicitParam(name="ids",value = "数据完整性检测配置表ID",required = true, dataType = "String")
    @GetMapping(value = "/listByIds")
    public ResponseData<List<DataCheckConfigDTO>> listByIds(String ids){
        if(StringUtil.isBlank(ids)) {
            return ResponseData.error("ID为空,查询失败");
        }
    List<DataCheckConfigDTO> dataCheckConfigDTOList = BeanUtil.transformList(checkConfigService.listByIds(ids), DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());
        return ResponseData.success(dataCheckConfigDTOList);
    }

    @ApiOperation(value = "查询数据完整性检测配置表",notes = "根据url的ID查询数据完整性检测配置表详细信息")
    @ApiImplicitParam(name="id",value = "数据完整性检测配置表ID",required = true, dataType = "String")
    @GetMapping(value = "/byId/{id}")
    public ResponseData<DataCheckConfigDTO> get(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,查询失败");
        }
        DataCheckConfigDTO dataCheckConfigDTO = BeanUtil.transform(checkConfigService.getById(Long.valueOf(id)), DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());
        List<DataCheckConfigDetailDTO> dataCheckConfigDetailDTOList = checkConfigDetailService.findByConfigId(Long.valueOf(id));
        dataCheckConfigDTO.setDataCheckConfigDetailVOList(dataCheckConfigDetailDTOList);
        return ResponseData.success(dataCheckConfigDTO);
    }

    @ApiOperation(value = "创建数据完整性检测配置表",notes = "权限：system:dataCheckConfig:add。根据DataCheckConfig对象创建数据完整性检测配置表,处理/dataCheckConfig/的POST请求，用来创建DataCheckConfig。请求时：contentType:application/json;charset=UTF-8")
    @ApiImplicitParam(name = "dataCheckConfigDTO",value = "数据完整性检测配置表视图对象dataCheckConfig",required = true, dataType = "DataCheckConfigDTO")
    @PostMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckConfigDTO> add(@RequestBody DataCheckConfigDTO dataCheckConfigDTO){
        DataCheckConfig dataCheckConfig = BeanUtil.transform(dataCheckConfigDTO, DataCheckConfig.class, DataCheckConfigConvert.toEntity());

        UserDTO currentUser = SpringSecurityUtil.getCurrentUser();
        if(currentUser != null){
            dataCheckConfig.setCreatorId(Long.valueOf(currentUser.getId()));
            dataCheckConfig.setModifier(Long.valueOf(currentUser.getId()));
        }
        dataCheckConfig = checkConfigService.saveData(dataCheckConfig);
        dataCheckConfigDTO = BeanUtil.transform(dataCheckConfig, DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());
        return ResponseData.success(dataCheckConfigDTO);
    }

    @ApiOperation(value = "更新数据完整性检测配置表",notes = "根据url的id来指定更新对象，并根据传过来的dataCheckConfig信息来更新数据完整性检测配置表详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataCheckConfigDTO",value = "数据完整性检测配置表视图对象dataCheckConfigDTO",required = true, dataType = "DataCheckConfigDTO")
    })
    @PutMapping(value = "",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<DataCheckConfigDTO> edit(@RequestBody DataCheckConfigDTO dataCheckConfigDTO){
        DataCheckConfig dataCheckConfig = BeanUtil.transform(dataCheckConfigDTO, DataCheckConfig.class, DataCheckConfigConvert.toEntity());
        checkConfigService.updateData(dataCheckConfig);
        dataCheckConfigDTO = BeanUtil.transform(dataCheckConfig, DataCheckConfigDTO.class, DataCheckConfigConvert.toVO());

        return ResponseData.success(dataCheckConfigDTO);
    }



    @ApiOperation(value = "删除数据完整性检测配置表",notes = "根据url的ID删除指定对象")
    @ApiImplicitParam(name="id",value = "数据完整性检测配置表ID",required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    public ResponseData<String> delete(@PathVariable("id") String id){
        if(StringUtil.isBlank(id)) {
            return ResponseData.error("ID为空,删除失败");
        }

        DataCheckConfig checkConfig = checkConfigService.getById(Long.valueOf(id));
        checkConfig.setDeletedFlag(1L);
        checkConfig.setDataCheckConfigDetailVOList(new ArrayList<>());
        checkConfigService.updateData(checkConfig);
        return ResponseData.success();
    }


    @ApiOperation(value = "更新数据完整性检测配置-状态",notes = "根据url的id来指定更新对象，并根据传过来的配置信息来更新配置状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "配置ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态：0 禁用 1 启用", required = true, dataType = "String")
    })
    @PutMapping(value = "/status",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public ResponseData<String> updateStatus(String id,String status){

        DataCheckConfig checkConfig = checkConfigService.updateStatus(id,status);
        if(checkConfig == null || !status.equals(checkConfig.getStatus())){
            return ResponseData.error("状态设置失败");
        }
        return ResponseData.success("状态设置成功");
    }
}
