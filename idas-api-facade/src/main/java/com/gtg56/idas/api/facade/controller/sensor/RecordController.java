package com.gtg56.idas.api.facade.controller.sensor;

import com.gtg56.idas.common.entity.dto.FDAReportDTO;
import com.gtg56.idas.common.entity.dto.sensor.OverLimitHandlingDTO;
import com.gtg56.idas.common.entity.dto.sensor.SensorRecordDTO;
import com.gtg56.idas.common.entity.dto.sensor.SensorResultDTO;
import com.gtg56.idas.common.entity.dto.sensor.SensorStatResultDTO;
import com.gtg56.idas.common.entity.http.ResponseData;
import com.gtg56.idas.common.entity.query.*;
import com.gtg56.idas.service.define.ISensorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "Sensor:RecordController",description = "传感器数据查询")
@RestController
@RequestMapping(value = "/api/sensor/record",produces = {MediaType.APPLICATION_JSON_VALUE})
public class RecordController {
    
    @Resource
    private ISensorService sensorService;
    
    @ApiOperation(value = "根据条件查询传感器最新数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = SensorResultDTO.class)
    })
    @PostMapping("/last")
    public ResponseData listRegionLastRecord(@RequestBody WarehouseSensorLastQuery query,
                                             HttpServletRequest request, HttpServletResponse response) {
        
        return ResponseData.success(sensorService.listSensorLastRecord(query));
    }
    
    @ApiOperation(value = "根据条件查询传感器历史数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = SensorResultDTO.class)
    })
    @PostMapping("/history")
    public ResponseData listRegionHistoryRecord(@RequestBody WarehouseSensorHistoryQuery query,
                                                HttpServletRequest request, HttpServletResponse response) {
        
        return ResponseData.success(sensorService.listSensorHistoryRecord(query));
    }
    
    @ApiOperation(value = "根据条件查询超标处理记录数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = OverLimitHandlingDTO.class)
    })
    @PostMapping("/overlimit")
    public ResponseData listOverlimit(@RequestBody WarehouseOverlimitHandlingQuery query,
                                      HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(sensorService.listOverlimitHandling(query));
    }
    
    @ApiOperation(value = "图表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = SensorStatResultDTO.class)
    })
    @PostMapping("/stat")
    public ResponseData listStat(@RequestBody WarehouseStatQuery query,
                                 HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(sensorService.stat(query));
    }
    
    @ApiOperation(value = "获取原始数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = SensorRecordDTO.class)
    })
    @PostMapping("/raw")
    public ResponseData listRaw(@RequestBody WarehouseSensorRawQuery query,
                                HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(sensorService.listRaw(query));
    }
    
    @ApiOperation(value = "获取药监局上报记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "success", response = FDAReportDTO.class)
    })
    @PostMapping("/fda/report")
    public ResponseData listFDAReport(@RequestBody FDAReportQuery query,
                                      HttpServletRequest request, HttpServletResponse response) {
        return ResponseData.success(sensorService.listFDAReportPage(query));
    }
    
}
