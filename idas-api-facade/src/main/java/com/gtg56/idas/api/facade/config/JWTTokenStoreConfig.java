/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: JWTTokenStoreConfig
 * @Author: zhangchuyi
 */
package com.gtg56.idas.api.facade.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

/**
 * @Description:
 *
 *
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION 
 *
 * @Author: zhangchuyi
 * @CeateDate: 2019-12-11 14:22
 * @Since: Bamboo V00.00.001
 */
@Configuration
public class JWTTokenStoreConfig {

    @Value("${system.jwt.public-key}")
    private String publicKey;

    @Bean
    public TokenStore tokenStore() {
        return new JwtTokenStore(jwtAccessTokenConverter());
    }

    @Bean
    @Primary
    public DefaultTokenServices tokenServices() {
        DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
        defaultTokenServices.setTokenStore(tokenStore());
        defaultTokenServices.setSupportRefreshToken(true);
        return defaultTokenServices;
    }


    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();//在JWT和OAuth2服务器之间充当翻译
        converter.setVerifierKey(publicKey);
        return converter;
    }
}
