package com.gtg56.idas.api.facade.service.impl;

import com.aliyun.dingtalkim_1_0.models.SendInteractiveCardHeaders;
import com.aliyun.dingtalkim_1_0.models.SendInteractiveCardRequest;
import com.aliyun.dingtalkim_1_0.models.UpdateInteractiveCardHeaders;
import com.aliyun.dingtalkim_1_0.models.UpdateInteractiveCardRequest;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaException;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.gtg56.idas.api.facade.service.INoticeService;
import com.gtg56.idas.api.facade.util.RestTemplateUtils;
import com.gtg56.idas.api.facade.vo.DeployApplyCardVO;
import com.gtg56.idas.api.facade.vo.NoticeSettingVO;
import com.gtg56.idas.common.util.StringUtil;
import com.taobao.api.ApiException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.List;

/**
 * @Author: czl
 * @Date: 2021/6/17 10:40
 */
@Service
public class NoticeServiceImpl implements INoticeService {

    private final static org.slf4j.Logger logger = LoggerFactory.getLogger(NoticeServiceImpl.class);


    @Value("${dingding.idas.alarm.url}")
    private String RobotAlarmUrl;
    @Value("${dingding.alarm.alarm.secret}")
    private String RobotAlarmSecret;



    @Override
    public void reportDeployMessage(String title, String message, String env, String url) {
        try {
            String alarmUrl = RobotAlarmUrl;
            String secret = RobotAlarmSecret;
            sendDingDing(alarmUrl, secret, title, message, url, null);
        } catch (Exception ex) {
            logger.error("钉钉报警失败", ex);
        }
    }

    @Override
    public Boolean reportDeployMessage(NoticeSettingVO noticeSettingVO, String title, String message, String url) {
        try {
            sendDingDing(noticeSettingVO.getDingTalkRobotUrl(), noticeSettingVO.getDingTalkRobotSecret(), title, message, url, noticeSettingVO.getAtPhoneList());
            return true;
        } catch (Exception ex) {
            logger.error("钉钉报警失败", ex);
            return false;
        }
    }

    @Override
    public Boolean sendDeployApplyCard(DeployApplyCardVO deployApplyCardVO) throws Exception {

        com.aliyun.dingtalkim_1_0.Client client = createClient();
        SendInteractiveCardHeaders sendInteractiveCardHeaders = new SendInteractiveCardHeaders();
        sendInteractiveCardHeaders.xAcsDingtalkAccessToken = getToken("dinggmbpay8t6unxqprx", "IU30cBhDXFwnb0K7nzm09Y6FVwHIC3KDEt5ZgExdYcowkGEibgZtB77nDvJxfWIq");

        java.util.Map<String, String> cardDataCardParamMap = TeaConverter.buildMap(
                new TeaPair("title", deployApplyCardVO.getTitle()),
                new TeaPair("type", deployApplyCardVO.getTime()),
                new TeaPair("amount", deployApplyCardVO.getReason()),
                new TeaPair("reason", deployApplyCardVO.getApplication()),
                new TeaPair("display", deployApplyCardVO.getDisplay())
        );
        SendInteractiveCardRequest.SendInteractiveCardRequestCardData cardData = new SendInteractiveCardRequest.SendInteractiveCardRequestCardData()
                .setCardParamMap(cardDataCardParamMap);
        SendInteractiveCardRequest sendInteractiveCardRequest = new SendInteractiveCardRequest()
                .setCardTemplateId("a3f0b80d-0b91-441a-84ad-f19bfc72f530")
                .setReceiverUserIdList(java.util.Arrays.asList(
                        "201718414624258383"
                        //"011059440733657613"
                ))
                .setOutTrackId(deployApplyCardVO.getId())
                .setConversationType(0)
                .setCardData(cardData)
                .setUserIdType(1);
        try {
            client.sendInteractiveCardWithOptions(sendInteractiveCardRequest, sendInteractiveCardHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                logger.error("发送钉钉卡片失败", err);
            }
            return false;
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                logger.error("发送钉钉卡片失败", err);
            }
            return false;
        }

        return true;
    }

    @Override
    public Boolean updateDeployApplyCard(DeployApplyCardVO deployApplyCardVO) throws Exception {

        com.aliyun.dingtalkim_1_0.Client client = createClient();

        java.util.Map<String, String> cardDataCardParamMap = TeaConverter.buildMap(
                new TeaPair("display", deployApplyCardVO.getDisplay())
        );

        UpdateInteractiveCardHeaders updateInteractiveCardHeaders = new UpdateInteractiveCardHeaders();
        updateInteractiveCardHeaders.xAcsDingtalkAccessToken = getToken("dinggmbpay8t6unxqprx", "IU30cBhDXFwnb0K7nzm09Y6FVwHIC3KDEt5ZgExdYcowkGEibgZtB77nDvJxfWIq");
        UpdateInteractiveCardRequest.UpdateInteractiveCardRequestCardOptions cardOptions = new UpdateInteractiveCardRequest.UpdateInteractiveCardRequestCardOptions()
                .setUpdateCardDataByKey(true)
                .setUpdatePrivateDataByKey(true);

        UpdateInteractiveCardRequest.UpdateInteractiveCardRequestCardData cardData = new UpdateInteractiveCardRequest.UpdateInteractiveCardRequestCardData()
                .setCardParamMap(cardDataCardParamMap);
        UpdateInteractiveCardRequest updateInteractiveCardRequest = new UpdateInteractiveCardRequest()
                .setOutTrackId(deployApplyCardVO.getId())
                .setCardData(cardData)
                .setUserIdType(1)
                .setCardOptions(cardOptions);
        try {
            client.updateInteractiveCardWithOptions(updateInteractiveCardRequest, updateInteractiveCardHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                logger.error("更新钉钉卡片失败", err);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                logger.error("更新钉钉卡片失败", err);
            }
        }
        return true;
    }


    public com.aliyun.dingtalkim_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkim_1_0.Client(config);
    }

    public static String getToken(String appKey, String secret) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(appKey);
        request.setAppsecret(secret);
        request.setHttpMethod("GET");
        OapiGettokenResponse response = client.execute(request);
        return response.getAccessToken();
    }


    public static void main(String[] args) throws Exception {
//        sendDingDing("https://oapi.dingtalk.com/robot/send?access_token=bf65372cb3e6f3461dac052d30c0bd24ae8f3eae0b1528c54e6053e39597f4b0",
//                "SEC1fe2197e76dc2af7df2ad12124bae74fa8371761ee5c85fbba0e6e7440daed93", "阿里业务",
//                "2021-09-29 13:46:45.333 [000df7ba2ac15649] \n\n阿里健康业务-下单提醒 采购单号:1361197313,下单时间:2021-09-29 13:46:45,总金额:283.80,推送成功   \n\n" +
//                        "-----------------------------------------------------------\n",
//                "http://api.gnc.gtg56.top/router/service/gone-manage/v1/noticeLogs/contentById/726797977897587712", null);

        sendDingDing("https://oapi.dingtalk.com/robot/send?access_token=453a35a5293868afad4c59b054b89771af4baed1f60548d16cbceb2ffe0c8cc5",
                "SEC2d1b8c7ab4756c8cf35475651113f69de372c1f2c8b3ea85e2515dd1d28082e7", "数据完整性检测",
                "2023-10-30 10:46:45.333 \n\n数据缺失提醒 共有1323条缺失记录！   \n\n" +
                        "-----------------------------------------------------------\n",
                "http://api.gnc.gtg56.top/router/service/gone-manage/v1/noticeLogs/contentById/726797977897587712", null);
    }

    private static void sendDingDing(String dingdingUrl, String secret, String title, String message, String contentUrl, List<String> mobileList) throws Exception {
        Long timestamp = System.currentTimeMillis();
        String sign = dingDingSign(timestamp, secret);
        DefaultDingTalkClient client = new DefaultDingTalkClient(dingdingUrl + String.format("&timestamp=%s&sign=%s", timestamp, sign));
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(title);

        //增加图片
        String imageUrl = "";
        try {
            //https://picsum.photos/800/400
            //https://source.unsplash.com/random/800x400
            //https://bing.ioliu.cn/v1/rand/?w=800&h=400
            ResponseEntity<String> responseEntity = RestTemplateUtils.getRestTemplate().exchange("https://source.unsplash.com/random/800x400", HttpMethod.GET, null, String.class);
            imageUrl = ((List) responseEntity.getHeaders().get("Location")).get(0).toString();
        } catch (Exception ex) {
        }

        String atStr = "";
        if (CollectionUtils.isNotEmpty(mobileList)) {
            atStr = mobileList.stream().reduce("", (a, b) -> a + " @" + b);
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(mobileList);
            request.setAt(at);
        }
        message = message.replace("\n", "\n > ");
        message = message.replace("\n > \n", "\n\n");
        message = message.replace("###", "");
        String content = String.format("![screenshot](%s) \n\n > ## %s \n\n > [%s](%s) \n\n > %s \n\n > %s", imageUrl, title, StringUtil.isNotBlank(contentUrl) ? "日志详情" : "", contentUrl, message, atStr);

        markdown.setText(content);
        request.setMarkdown(markdown);
        OapiRobotSendResponse response = client.execute(request);
        if (!response.isSuccess()) {
            throw new RuntimeException("钉钉消息发送异常" + response.getErrmsg());
        }
    }

    private static String dingDingSign(Long timestamp, String secret) throws Exception {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        return sign;
    }


}
