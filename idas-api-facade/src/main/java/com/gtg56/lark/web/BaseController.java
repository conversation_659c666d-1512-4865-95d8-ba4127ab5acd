/**
 * Copyright (C), 2016-2019, 广州交通集团物流有限公司
 *
 * @FileName: AbstractController
 * @Author: zhangchuyi
 */
package com.gtg56.lark.web;


import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * @Description: 抽象的Controller基类
 * <p>
 * Revision History:
 * DATE        AUTHOR        VERSION        DESCRIPTION
 * @Author: zhangchuyi
 * @CeateDate: 2018/9/13 09:49
 * @Since: Bamboo V00.00.001
 */
public class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final static String SUCCESS = "success";

    //AES秘钥
    private static String AESKEY = "cmyynet201700001";
    private static String IV_STRING = "A-16-Byte-String";

    protected HttpServletRequest getRequest(){
        return ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
    }

    /**
     * MyBatis分页对象初始化
     *
     * @return
     */
    protected com.github.pagehelper.Page initMyBatisPageParams() {
        return initMyBatisPageParams(null);
    }

    /**
     * MyBatis分页对象初始化
     *
     * @return
     */
    protected com.github.pagehelper.Page initMyBatisPageParams(String sort) {
        HttpServletRequest request = getRequest();
        Integer pageNum = NumberUtils.toInt(getRestParam(request, "pageNum"));
        Integer pageSize = NumberUtils.toInt(getRestParam(request, "pageSize"));
        if(pageNum == null && pageSize == null) {
            pageNum = NumberUtils.toInt(getRequestParam(request, "pageNum"));
            pageSize = NumberUtils.toInt(getRequestParam(request, "pageSize"));
        }
        if(StringUtils.isEmpty(sort)) {
            sort = request.getParameter("sort");
        }
        if (pageNum == null) {
            pageNum = 1;
        }

        if (pageSize == null) {
            pageSize = 20;
        }

        //JPA 分页对象的起始页为1
        //MyBatis PageHelper的起始页为1
        com.github.pagehelper.Page page = null;

        if (!StringUtils.isEmpty(sort)) {
            page = PageHelper.startPage(pageNum, pageSize, sort);
        } else {
            //page = PageHelper.startPage(pageNum, pageSize);
            page = PageHelper.startPage(pageNum, pageSize,true,false,null);
        }

        return page;
    }

    /**
     * 框架分页对象初始化
     *
     * @return
     */
    protected PageObject initPageParams() {
        return initPageParams(null);
    }

    /**
     * 框架分页对象初始化
     *
     * @return
     */
    protected PageObject initPageParams(Order sort) {
        List<Order> orderList = new ArrayList<>();
        HttpServletRequest request = getRequest();
        Integer pageNum = NumberUtils.toInt(getRestParam(request, "pageNum"));
        Integer pageSize = NumberUtils.toInt(getRestParam(request, "pageSize"));
        if(sort != null) {
            orderList.add(sort);
        }
        String sortStr = request.getParameter("sort");
        if(StringUtils.isNotBlank(sortStr)) {
            String[] sortStrs = sortStr.split(",");
            for (String str : sortStrs) {
                String field = StringUtils.substringBefore(str, ":");
                String dbField = "";//TODO 如果后续引入mybatisplus时，再放开
//                String dbField = com.baomidou.mybatisplus.core.toolkit.StringUtils.camelToUnderline(field);
                if (StringUtils.trimToEmpty(StringUtils.substringAfter(str, ":")).equalsIgnoreCase("desc")) {
                    orderList.add(Order.desc(dbField));
                } else {
                    orderList.add(Order.asc(dbField));
                }
            }
        }
        if (pageNum == null || pageNum == 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        //JPA 分页对象的起始页为1
        //MyBatis PageHelper的起始页为1
        PageObject pageObject = new PageObject(pageNum, pageSize);
        pageObject.addOrders(orderList.toArray(new Order[orderList.size()]));
        return pageObject;
    }


    /**
     * AES解密
     * content 解密内容
     */
    public static String AESDecode(String content) {
        return AESDecode(AESKEY, content);
    }

    /**
     * AES解密
     * encodeRules 秘钥
     * content 解密内容
     */
    public static String AESDecode(String key, String content) {
        try {
            // base64 解码
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] encryptedBytes = decoder.decode(content.replace(" ", "+"));
            byte[] enCodeFormat = key.getBytes();
            SecretKeySpec secretKey = new SecretKeySpec(enCodeFormat, "AESUtil");
            byte[] initParam = IV_STRING.getBytes();
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AESUtil/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(encryptedBytes);
            return new String(result, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getRequestParam(HttpServletRequest request, String name) {
        String[] param = request.getParameterValues(name);
        if(param != null && param[0] != null) {
            return param[0];
        }
        return null;
    }

    public static String getRestParam(HttpServletRequest request, String name) {
         String url = request.getRequestURL().toString();
         String fullParamName = "/" + name + "=";
         if(url.indexOf(fullParamName) != -1) {
             String value = StringUtils.substringBetween(url, fullParamName, "/");
             if(StringUtils.isBlank(value)) {
                 value = StringUtils.substringAfter(url, fullParamName);
             }
             return value;
         }
         //获取request param
        if(StringUtils.isNotBlank(request.getParameter(name))) {
            return request.getParameter(name);
        }
         return null;
    }
}
