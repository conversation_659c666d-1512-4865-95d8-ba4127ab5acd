<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <groupId>com.gtg56</groupId>
    <artifactId>idas-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modelVersion>4.0.0</modelVersion>

    <description>
        <![CDATA[
            这里是广交物流数据平台项目parent

            本pom主要维护：
            项目主版本
            远程maven仓库地址
            编译构建参数控制
            依赖版本控制
            依赖管理
            构建插件管理

            维护规约：
            1、所有项目内子模块必须将本项目作为parent，并在
               项目根pom modules处添加对应artifactId。
               使用IDEA添加模块时：
               Add as module to 选择 idas
               Parent 选择 idas-parent
               即可自动添加
            2、所有第三方依赖必须在本pom中维护gav，不得
               在子模块pom中私自指定依赖。本地静态依赖
               （通过systemPath映射已加入项目的jar）除外。
            3、第三方依赖，相同项目有多个模块的，必须通过
                properties集中控制版本。只有一个模块，
                则可以直接在version输入常数（但不建议）。
                示例见hadoop、spark等多模块项目的依赖。
            4、循环依赖请自觉查找并打破。
            5、引入第三方模块时，如其依赖模块与现有冲突，请
               自行解决。（日志类最常见，请提高警惕）
            6、活用IDEA插件 Maven Helper 解决4、5问题。
            7、具有关联性的依赖，请放在一起，方便维护。

        ]]>
    </description>

    <repositories>


        <repository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
        <repository>
            <id>cloudera</id>
            <url>http://**********:8081/nexus/content/repositories/cloudera/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://**********:8081/nexus/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <url>http://**********:8081/nexus/content/repositories/releases/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>http://***********:8081/repository/maven-public/</url>
        </repository>


        <repository>
            <id>SparkPackagesRepo</id>
            <name>Spark Packages Repo</name>
            <url>https://repos.spark-packages.org</url>
            <layout>default</layout>
        </repository>
    </repositories>


    <pluginRepositories>
        <pluginRepository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <!-- 在这里对依赖版本作统一管理。所有子模块不允许私自指定依赖版本 -->
    <properties>
        <!-- env config -->
        <jdk.version>1.8</jdk.version>
        <scala.major.version>2.11</scala.major.version>
        <scala.lib.version>${scala.major.version}.12</scala.lib.version>
        <charset.encoding>UTF-8</charset.encoding>
        <project.build.sourceEncoding>${charset.encoding}</project.build.sourceEncoding>
        <maven.compiler.encoding>${charset.encoding}</maven.compiler.encoding>

        <lombok.version>1.18.16</lombok.version>

        <spring-boot-dependencies.version>2.3.0.RELEASE</spring-boot-dependencies.version>

        <spring-cloud.version>Hoxton.SR3</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>

        <slf4j.version>1.7.30</slf4j.version>

        <!-- json -->
        <fastjson.version>1.2.62</fastjson.version>
        <gson.version>2.8.6</gson.version>
        <jackson.version>2.10.5</jackson.version>

        <janino.version>3.0.8</janino.version>
        <dubbo.version>2.7.8</dubbo.version>
        <hessian.version>4.0.63</hessian.version>

        <axis.version>1.4</axis.version>
        <axis2.version>1.7.9</axis2.version>

        <!-- jdbc redis etc. -->
        <mysql.version>8.0.22</mysql.version>
        <mssql.version>7.4.1.jre8</mssql.version>
        <druid.version>1.1.22</druid.version>
        <redis.version>3.2.0</redis.version>
        <mybatis.version>3.4.5</mybatis.version>
        <mybatis-spring.version>1.3.1</mybatis-spring.version>
        <tk.mybatis.version>4.0.3</tk.mybatis.version>
        <tk.mybatis-spring.version>2.1.5</tk.mybatis-spring.version>
        <mybatis.pagehelper-spring.version>1.2.5</mybatis.pagehelper-spring.version>
        <mybatis.pagehelper.version>5.1.4</mybatis.pagehelper.version>
        <mybatis-redis.version>1.0.0-beta2</mybatis-redis.version>

        <cdh.version>6.3.2</cdh.version>
        <csa.version>1.0.0.0</csa.version>
        <hadoop.base.version>3.0.0</hadoop.base.version>
        <hive.base.version>2.1.1</hive.base.version>
        <hbase.base.version>2.1.0</hbase.base.version>
        <zookeeper.base.version>3.4.5</zookeeper.base.version>
        <spark.base.version>2.4.0</spark.base.version>
        <spark.base.major.version>2.4</spark.base.major.version>
        <kafka.base.version>2.2.1</kafka.base.version>
        <flink.base.version>1.9.0</flink.base.version>
        <flume.base.version>1.9.0</flume.base.version>
        <graphframes.base.version>0.8.2</graphframes.base.version>

        <hadoop.version>${hadoop.base.version}-cdh${cdh.version}</hadoop.version>
        <hive.version>${hive.base.version}-cdh${cdh.version}</hive.version>
        <hbase.version>${hbase.base.version}-cdh${cdh.version}</hbase.version>
        <zookeeper.version>${zookeeper.base.version}-cdh${cdh.version}</zookeeper.version>
        <curator.version>2.12.0</curator.version>
        <spark.version>${spark.base.version}-cdh${cdh.version}</spark.version>
        <kafka.version>${kafka.base.version}-cdh${cdh.version}</kafka.version>
        <flink.version>${flink.base.version}-csa${csa.version}</flink.version>
        <flume.version>${flume.base.version}-cdh${cdh.version}</flume.version>
        <graphframes.version>${graphframes.base.version}-spark${spark.base.major.version}-s_${scala.major.version}</graphframes.version>

        <delta.version>0.6.0</delta.version>
        <hudi.version>0.5.1-incubating</hudi.version>
<!--        <lark.version>1.0.0-2011-SNAPSHOT</lark.version>-->
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.spring4all</groupId>
                <artifactId>swagger-spring-boot-starter</artifactId>
                <version>1.9.1.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>io.zipkin.brave</groupId>
                <artifactId>brave-instrumentation-dubbo-rpc</artifactId>
                <version>5.13.3</version>

            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.lib.version}</version>
            </dependency>


            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>0.5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>org.anarres.lzo</groupId>
                <artifactId>lzo-core</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.8.4</version>
            </dependency>


            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>28.2-jre</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13</version>
                <scope>test</scope>
            </dependency>


            <!-- log for java-->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-scala_${scala.major.version}</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!--  xml  -->
            <dependency>
                <groupId>com.jamesmurty.utils</groupId>
                <artifactId>java-xmlbuilder</artifactId>
                <version>1.3</version>
            </dependency>

            <!--  Runtime compiler  -->
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${janino.version}</version>
            </dependency>

            <!-- jdbc redis etc. -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.mybatis</groupId>-->
<!--                <artifactId>mybatis</artifactId>-->
<!--                <version>${mybatis.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.mybatis</groupId>-->
<!--                <artifactId>mybatis-spring</artifactId>-->
<!--                <version>${mybatis-spring.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.6</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>${tk.mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${tk.mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${mybatis.pagehelper-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${mybatis.pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.6</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.version}</version>
            </dependency>

            <!-- SOA -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sourceforge.htmlunit</groupId>
                <artifactId>htmlunit</artifactId>
                <version>2.45.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2</artifactId>
                <version>${axis2.version}</version>
                <type>pom</type>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-adb</artifactId>
                <version>${axis2.version}</version>
                <type>jar</type>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-kernel</artifactId>
                <version>${axis2.version}</version>
                <type>jar</type>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-http</artifactId>
                <version>${axis2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-local</artifactId>
                <version>${axis2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-jaxws</artifactId>
                <version>${axis2.version}</version>
            </dependency>

            <!-- Hadoop & HBase -->
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty.orbit</groupId>
                        <artifactId>javax.servlet</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty.orbit</groupId>
                        <artifactId>javax.servlet</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-client</artifactId>
                <version>${hbase.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-server</artifactId>
                <version>${hbase.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty.orbit</groupId>
                        <artifactId>javax.servlet</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api-2.5</artifactId>
                        <groupId>org.mortbay.jetty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-core_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <!--<exclusion>-->
                        <!--<groupId>log4j</groupId>-->
                        <!--<artifactId>log4j</artifactId>-->
                    <!--</exclusion>-->
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.janino</groupId>
                        <artifactId>janino</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-avro_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-hive_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.janino</groupId>
                        <artifactId>janino</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>HikariCP</artifactId>
                        <groupId>com.zaxxer</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.derby</groupId>
                        <artifactId>derby</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-streaming_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-streaming-kafka-0-10_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-mllib_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-yarn_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-graphx_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>graphframes</groupId>
                <artifactId>graphframes</artifactId>
                <version>${graphframes.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bigts</groupId>
                <artifactId>opentsdb-spark</artifactId>
                <version>0.1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.cloudera.spark</groupId>
                <artifactId>spark-lineage_${scala.major.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.otter</groupId>
                <artifactId>canal.client</artifactId>
                <version>1.1.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.rocketmq</groupId>
                        <artifactId>rocketmq-acl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.rocketmq</groupId>
                        <artifactId>rocketmq-client</artifactId>
                    </exclusion>
                     <exclusion>
                         <groupId>ch.qos.logback</groupId>
                         <artifactId>logback-core</artifactId>
                     </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.delta</groupId>
                <artifactId>delta-core_${scala.major.version}</artifactId>
                <version>${delta.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.hudi</groupId>
                <artifactId>hudi-spark-bundle_${scala.major.version}</artifactId>
                <version>${hudi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-core</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java_${scala.major.version}</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients_${scala.major.version}</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka_${scala.major.version}</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-api-java</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-planner-blink_${scala.major.version}</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flume</groupId>
                <artifactId>flume-ng-core</artifactId>
                <version>${flume.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flume</groupId>
                <artifactId>flume-ng-sdk</artifactId>
                <version>${flume.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <directory>target</directory>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-dependencies.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.3.2</version>
                    <configuration>
                        <source>${jdk.version}</source>
                        <target>${jdk.version}</target>
                        <encoding>${charset.encoding}</encoding>
                        <!--<compilerArguments>
                            <bootclasspath>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</bootclasspath>
                        </compilerArguments>-->
                        <showDeprecation>true</showDeprecation>
                        <showWarnings>true</showWarnings>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-clover2-plugin</artifactId>
                    <version>4.0.0</version>
                    <configuration>
                        <licenseLocation>clover.license</licenseLocation>
                        <generateHistorical>true</generateHistorical>
                        <excludes>
                            <exclude>**/notjunit/*.java</exclude>
                        </excludes>
                        <generateXml>true</generateXml>
                        <generateHtml>true</generateHtml>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.5.1</version>
                    <configuration>
                        <downloadSources>true</downloadSources>
                        <downloadJavadocs>false</downloadJavadocs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.4.2</version>
                    <configuration>
                        <skipTests>true</skipTests>
                        <argLine>-Xms512m -Xmx1024m</argLine>
                        <forkMode>once</forkMode>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.0</version>
                    <configuration>
                        <locales>zh_CN</locales>
                        <outputEncoding>${charset.encoding}</outputEncoding>
                        <inputEncoding>${charset.encoding}</inputEncoding>
                        <reportPlugins>
                            <plugin>
                                <groupId>org.apache.maven.plugins</groupId>
                                <artifactId>maven-javadoc-plugin</artifactId>
                                <version>2.8</version>
                                <configuration>
                                    <encoding>${charset.encoding}</encoding>
                                </configuration>
                            </plugin>
                            <plugin>
                                <groupId>org.apache.maven.plugins</groupId>
                                <artifactId>maven-project-info-reports-plugin</artifactId>
                                <version>2.4</version>
                                <configuration>
                                    <dependencyDetailsEnabled>false</dependencyDetailsEnabled>
                                    <dependencyLocationsEnabled>false</dependencyLocationsEnabled>
                                </configuration>
                                <!-- simpler configuration without reportSets available for usual cases -->
                                <reports>
                                    <report>dependencies</report>
                                    <report>scm</report>
                                </reports>
                            </plugin>
                        </reportPlugins>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>jar</goal>
                                <goal>test-jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2.1</version>
                    <configuration>
                        <attach>true</attach>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                                <goal>test-jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.8</version>
                    <executions>
                        <execution>
                            <id>copy-dependency</id>
                            <phase>package</phase>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.basedir}/target/lib</outputDirectory>
                                <overWriteReleases>true</overWriteReleases>
                                <overWriteSnapshots>true</overWriteSnapshots>
                                <overWriteIfNewer>true</overWriteIfNewer>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <!--  plugin for build fat jar  -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>2.4.1</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
                                <promoteTransitiveDependencies>false</promoteTransitiveDependencies>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <minimizeJar>false</minimizeJar>

                                <outputFile>${project.basedir}/target/${project.artifactId}-${project.version}-shaded.jar</outputFile>

                                <transformers>
                                    <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                        <resource>META-INF/spring.handlers</resource>
                                    </transformer>
                                    <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                        <resource>META-INF/spring.schemas</resource>
                                    </transformer>
                                </transformers>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-clover2-plugin</artifactId>
                    <version>4.0.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.10</version>
                </plugin>

            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.atlassian.maven.plugins</groupId>
                <artifactId>maven-clover2-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
