#!/bin/bash
set -e
echo "usage : streaming-up.sh <dev prod (spring.profiles.active)> -entrance <entrance>"
SHELL_FOLDER=$(dirname $(readlink -f "$0"))
mainJar=${SHELL_FOLDER}/../target/idas-service-spark-1.0-SNAPSHOT.jar

profile=$1
entrance=$3
pidBaseDir="/tmp"
stdoutBaseDir="/var/log/idas"
dateStr=`date +%Y-%m-%d_%H.%M.%S`
moduleStdout=${stdoutBaseDir}/idas-service-spark-streaming-${dateStr}.out

fs=`hdfs getconf -confKey fs.defaultFS`

# 删除spark/hadoop自带GSON库的软连接
rm -f /opt/cloudera/parcels/CDH/lib/spark/jars/gson*.jar
sed '/gson/d' /etc/spark/conf/classpath.txt > /etc/spark/conf/classpath.txt
# 更新spark环境变量
source /etc/spark/conf/spark-env.sh
for f in /data/src/gtg56/idas/idas-service-spark/target/lib/*.jar; do
    echo "CP"|grep "$f" > /dev/null 2>&1 || CP="$CP,local:$f";
done

# 提交
sudo -u hdfs nohup spark-submit --master yarn --deploy-mode client \
  --conf "spark.yarn.jars=local:/opt/cloudera/parcels/CDH/lib/spark/jars/*,local:/opt/cloudera/parcels/CDH/lib/spark/hive/*,file:/data/src/gtg56/idas/idas-service-spark/target/idas-service-spark-1.0-SNAPSHOT.jar,${CP}" \
  --class com.gtg56.idas.service.spark.StreamingApplication \
  --jars "local:/opt/cloudera/parcels/CDH/lib/spark/jars/*,local:/opt/cloudera/parcels/CDH/lib/spark/hive/*,file:/data/src/gtg56/idas/idas-service-spark/target/idas-service-spark-1.0-SNAPSHOT.jar,${CP}" \
  --driver-java-options "-Dorg.springframework.boot.logging.LoggingSystem=none -Dspring.profiles.active=$profile" \
  ${mainJar} "$@" > ${moduleStdout} 2>&1 &

pid=$!
pidFile=$pidBaseDir/idas-service-spark-streaming-$entrance-$profile-$pid.pid
echo "service idas-service-spark-streaming entrance $entrance pid $pid , pid file at $pidFile"
echo $pid > $pidFile
echo "stdout file : ${moduleStdout}"