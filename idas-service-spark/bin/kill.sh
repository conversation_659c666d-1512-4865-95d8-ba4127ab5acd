#!/bin/bash
SHELL_FOLDER=$(dirname $(readlink -f "$0"));
# kill spark-submit process
$SHELL_FOLDER/../../bin/kill.sh $1 idas-service-spark;
mainClass=com.gtg56.idas.service.spark.Application
# kill spark driver process
numSpark=`jps -mlvV | grep $mainClass | awk 'END{print NR}'`;
if [ $numSpark == "0" ]; then
  echo "no idas-service-spark driver";
elif [ $numSpark == "1" ]; then
  pid=`jps -mlvV | grep $mainClass | awk '{print $1}'`;
  echo "kill idas-service-spark driver pid $pid";
  kill $pid;
else
  pids=`jps -mlvV | grep $mainClass | awk '{print $1}' | xargs `;
  for pid in $pids
  do
    echo "kill idas-service-spark driver pid $pid";
    kill $pid;
  done
fi

## remove spark application temp dirs
#numUuid=`ls -F /tmp/ | grep ^spark- | awk 'END{print NR}'`;
#if [ $numUuid == "0" ]; then
#  echo "no resources to clean"
#else
#  uuids=`ls -F /tmp/ | grep ^spark- | awk -v FS='-' -v OFS='-' '{print $2 $3,$4,$5,substr($6,0,12)}' | xargs`
#  for uuid in $uuids
#  do
#    sprkTmp="/tmp/spark-${uuid}";
#    sparkRes="/tmp/${uuid}_resources";
#    rm -rf sparkTmp
#    rm -rf sparkRes
#  done
#fi