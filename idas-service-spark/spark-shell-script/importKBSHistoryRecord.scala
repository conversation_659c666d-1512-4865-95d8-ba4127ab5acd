import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord
import com.gtg56.idas.common.util.JDBCUtil
import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制
spark.sparkContext.setLogLevel("warn")

val url = "*****************************************************"
val user = "query"
val password = "query"
val warehouseCode = "STYYWLZX001"
val tsdbUrl = "http://10.1.9.63:4242"

case class KBSHistotyRecord(val recordid:String,val orgcode:String,val orgName:String,val nodecode:String,val nodeName:String,val temperature:java.math.BigDecimal,val humidity:java.math.BigDecimal,val temperatureHighLimit:java.math.BigDecimal,val temperatureLowLimit:java.math.BigDecimal,val humidityHighLimit:java.math.BigDecimal,val humidityLowLimit:java.math.BigDecimal,val recorddatetime:java.sql.Timestamp)

val minMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url,user,password,"monrecord","ID").split(",")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "monrecord").option("partitionColumn", "Id").option("lowerBound", minMax(0)).option("upperBound", minMax(1)).option("numPartitions", 200).load.createOrReplaceTempView("monrecord")
spark.read.format("jdbc").option("url",url).option("user",user).option("password",password).option("dbtable","Info_Mon").load.createOrReplaceTempView("Info_Mon")

spark.read.format("jdbc").option("url",url).option("user",user).option("password",password).option("dbtable","Info_Line").load.createOrReplaceTempView("Info_Line")


val sqlText =
  """
    |select t.id as recordid, --1
    |       t1.LineCode   as orgcode, --2
    |       t2.LineName   as orgName, --3
    |       t.moncode     as nodecode, --4
    |       t1.MonName    as nodeName, --5
    |       t.temperature, --6
    |       humidity, --7
    |       t1.TULimit    as temperatureHighLimit,--8
    |       t1.TLLimit    as temperatureLowLimit,--9
    |       t1.WULimit    as humidityHighLimit,--10
    |       t1.WLLimit    as humidityLowLimit,--11
    |       mtime         as recorddatetime --12
    |  from monrecord t, Info_Mon t1, Info_Line t2
    | where t.moncode = t1.MonCode
    |   and t1.LineCode = t2.LineCode
  """.stripMargin



val dataFrame = spark.sql(sqlText).na.drop()

val hisDS = dataFrame.as[KBSHistotyRecord]

hisDS.foreachPartition(it => {
           it.foreach(record => {
             val client = new com.gtg56.idas.common.tool.http.OpenTSDBClient(tsdbUrl)

             val sensorCode = warehouseCode +"-" + record.nodecode
             val temStatus = com.gtg56.idas.common.consts.SensorConsts.toStatus(record.temperature,record.temperatureHighLimit,record.temperatureLowLimit)
             val humStatus = com.gtg56.idas.common.consts.SensorConsts.toStatus(record.humidity,record.humidityHighLimit,record.humidityLowLimit)

             val temNormal = if(com.gtg56.idas.common.consts.SensorConsts.STATUS_NORMAL.equals(temStatus)) com.gtg56.idas.common.consts.CommonConsts.TRUE else com.gtg56.idas.common.consts.CommonConsts.FALSE
             val humNormal = if(com.gtg56.idas.common.consts.SensorConsts.STATUS_NORMAL.equals(humStatus) ) com.gtg56.idas.common.consts.CommonConsts.TRUE else com.gtg56.idas.common.consts.CommonConsts.FALSE

             val time = record.recorddatetime.getTime / 1000

             val temTags = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord.Tags().setWarehouseCode(warehouseCode).setRegionCode(record.orgcode).setSensorCode(sensorCode).setType(com.gtg56.idas.common.consts.SensorConsts.TYPE_TEMPERATURE).setStatus(temStatus).setNormal(temNormal)
             val tem = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord().setValue(record.temperature).setTimestamp(time).setTags(temTags)
             client.put(tem,false)

             val humTags = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord.Tags().setWarehouseCode(warehouseCode).setRegionCode(record.orgcode).setSensorCode(sensorCode).setType(com.gtg56.idas.common.consts.SensorConsts.TYPE_HUMIDITY).setStatus(humStatus).setNormal(humNormal)
             val hum = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord().setValue(record.humidity).setTimestamp(time).setTags(humTags)

             client.put(hum,false)

           })
})








