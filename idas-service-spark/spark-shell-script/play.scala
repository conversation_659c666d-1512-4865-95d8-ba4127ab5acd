import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")

import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.functions._

val ds = "20230405"
val merchantSaltBucket = 256
val warehouseSaltBucket = 1024
val carrierSaltBucket = 256

val merchantSalt = spark.range(0, merchantSaltBucket)
val merchantDs = spark.sql(String.format("select id, merchant_name from ods.oms_md_merchant where ds = %s", ds))
val merchantSalted =
  merchantDs.crossJoin(merchantSalt)
    .select(merchantDs.col("*"), concat(merchantDs.col("id"), lit("_"), merchantSalt.col("id")).as("merchant_id_proxy"))
    .alias("m")


val warehouseSalt = spark.range(0, warehouseSaltBucket)
val warehouseDs = spark.sql(String.format("select id, warehouse_name from ods.oms_md_warehouse where ds = %s", ds))
val warehouseSalted =
  warehouseDs.crossJoin(warehouseSalt)
    .select(warehouseDs.col("*"), concat(warehouseDs.col("id"), lit("_"), warehouseSalt.col("id")).as("wh_id_proxy"))
    .alias("w")

val carrierSalt = spark.range(0, carrierSaltBucket)
val carrierDs = spark.sql(String.format("select id, name carrier_name, code carrier_code from ods.oms_md_carrier where ds = %s", ds))
val carrierSalted =
  carrierDs.crossJoin(carrierSalt)
    .select(carrierDs.col("*"), concat(carrierDs.col("id"), lit("_"), carrierSalt.col("id")).as("carrier_id_proxy"))
    .as("c")

var doDs = spark.sql(String.format("select * from mid.oms_do_all where ds = %s", ds))

val doSalted = doDs
  .withColumn("wh_id_proxy", concat(doDs.col("wh_id"), lit("_"), pmod(abs(hash(randn())), lit(warehouseSaltBucket))))
  .withColumn("merchant_id_proxy", concat(doDs.col("merchant_id"), lit("_"), pmod(abs(hash(randn())), lit(merchantSaltBucket))))
  .withColumn("carrier_id_proxy", concat(doDs.col("carrier_id"), lit("_"), pmod(abs(hash(randn())), lit(carrierSaltBucket))))
  .repartition(2000, col("wh_id_proxy"), col("merchant_id_proxy"), col("carrier_id_proxy"))
  .alias("d")


val d = doSalted
  .join(merchantSalted, "merchant_id_proxy")
  .join(warehouseSalted, "wh_id_proxy")
  .join(carrierSalted, "carrier_id_proxy")
  .select(col("d.*"), col("w.warehouse_name"), col("c.carrier_name"), col("c.carrier_code"))
  .drop("wh_id_proxy")
  .drop("merchant_id_proxy")
  .drop("carrier_id_proxy")

d.write.mode(SaveMode.Overwrite).insertInto("mid.oms_do_merge")