import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")

import java.sql.Date
import java.util.Base64
import scala.util.parsing.json._

import com.gtg56.idas.common.consts._
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent
import com.gtg56.idas.common.tool.http.OpenTSDBClient
import com.gtg56.idas.common.util.DateUtil
import spark.implicits._

import scala.collection.JavaConversions._
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils

// 修改下列变量
val warehouseCode = "GZGJDJKCYY"
val apiUrl = "http://**********:3001"
val apiUsername = "sscdatadocking"
val apiPassword = "ssc123456&"
val startDate = DateUtil.ymdhms().parse("2025-01-01 00:00:00")
val endDate = DateUtil.ymdhms().parse("2025-01-03 23:59:59")

val tsdbAddr = "http://***********:4242"
val jdbcUrl = "***********************************************************************************************************************************************************"
val jdbcUser = "idas"
val jdbcPasswd = "Idas#2020"

val diff = DateUtil.dayDiff(startDate, endDate)

val prop = new java.util.Properties()
prop.setProperty("user", jdbcUser)
prop.setProperty("password", jdbcPasswd)
prop.setProperty("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
val sensor = spark.read.jdbc(jdbcUrl, "warehouse_sensor", prop).where(s"warehouse_code = '${warehouseCode}' and corp_code = '${SensorConsts.CORP_ZD}'")

val dates = spark.range(0, diff).map(i => new Date(DateUtil.dayAdd(startDate, i.toInt).getTime.getTime)).selectExpr("value as date")

val sensorDates = sensor.crossJoin(dates).repartition(200)

// 登录获取sessid的函数
def login(apiUrl: String, username: String, password: String): String = {
  val httpClient = HttpClients.createDefault()
  val httpPost = new HttpPost(s"${apiUrl}/login")
  httpPost.setHeader("Content-Type", "application/json")
  
  val encodedUsername = Base64.getEncoder.encodeToString(username.getBytes())
  val encodedPassword = Base64.getEncoder.encodeToString(password.getBytes())
  val json = s"""{"username":"${encodedUsername}","pwd":"${encodedPassword}"}"""
  
  val entity = new StringEntity(json)
  httpPost.setEntity(entity)
  
  val response = httpClient.execute(httpPost)
  val responseString = EntityUtils.toString(response.getEntity)
  response.close()
  httpClient.close()
  
  // 解析JSON获取sessid
  JSON.parseFull(responseString) match {
    case Some(map: Map[String, Any]) =>
      if (map.get("success").contains(true)) {
        map.get("sessid").map(_.toString).getOrElse("")
      } else {
        println(s"Login failed: ${responseString}")
        ""
      }
    case _ =>
      println(s"Failed to parse login response: ${responseString}")
      ""
  }
}

// 获取设备历史数据的函数
def getDeviceHistoryData(apiUrl: String, sessid: String, deviceTag: String, fromTime: String, toTime: String, coll: Int): List[Map[String, Any]] = {
  val httpClient = HttpClients.createDefault()
  val httpPost = new HttpPost(s"${apiUrl}/history/device")
  httpPost.setHeader("Content-Type", "application/json")
  
  val json = s"""{"sessid":"${sessid}","tag":"${deviceTag}","from":"${fromTime}","to":"${toTime}","coll":${coll}}"""
  
  val entity = new StringEntity(json)
  httpPost.setEntity(entity)
  
  val response = httpClient.execute(httpPost)
  val responseString = EntityUtils.toString(response.getEntity)
  response.close()
  httpClient.close()
  
  // 解析JSON获取结果
  JSON.parseFull(responseString) match {
    case Some(map: Map[String, Any]) =>
      if (map.get("success").contains(true)) {
        map.get("results") match {
          case Some(results: List[Map[String, Any]]) => results
          case _ => List.empty
        }
      } else {
        println(s"Get device history data failed: ${responseString}")
        List.empty
      }
    case _ =>
      println(s"Failed to parse history data response: ${responseString}")
      List.empty
  }
}

// 获取设备列表的函数
def getDeviceBrief(apiUrl: String, sessid: String): List[Map[String, Any]] = {
  val httpClient = HttpClients.createDefault()
  val httpPost = new HttpPost(s"${apiUrl}/device/brief")
  httpPost.setHeader("Content-Type", "application/json")
  
  val json = s"""{"sessid":"${sessid}"}"""
  
  val entity = new StringEntity(json)
  httpPost.setEntity(entity)
  
  val response = httpClient.execute(httpPost)
  val responseString = EntityUtils.toString(response.getEntity)
  response.close()
  httpClient.close()
  
  // 解析JSON获取设备列表
  JSON.parseFull(responseString) match {
    case Some(map: Map[String, Any]) =>
      if (map.get("success").contains(true)) {
        map.get("devlist") match {
          case Some(devlist: List[Map[String, Any]]) => devlist
          case _ => List.empty
        }
      } else {
        println(s"Get device brief failed: ${responseString}")
        List.empty
      }
    case _ =>
      println(s"Failed to parse device brief response: ${responseString}")
      List.empty
  }
}

println("开始执行数据补充...")

sensorDates.foreachPartition(it => {
  val openTSDBClient = new OpenTSDBClient(tsdbAddr)
  
  // 每个分区登录一次
  val sessid = login(apiUrl, apiUsername, apiPassword)
  if (sessid.isEmpty) {
    println("登录失败，跳过当前分区")
    return
  }
  
  // 获取设备列表
  val devices = getDeviceBrief(apiUrl, sessid)
  val deviceMap = devices.map(d => d.get("tag").toString -> d).toMap
  
  it.foreach(row => {
    val sensorCodeOrigin = row.getAs[String]("sensor_code_origin")
    val sensorCode = row.getAs[String]("sensor_code")
    val regionCode = row.getAs[String]("region_code")
    val th = row.getAs[java.math.BigDecimal]("temperature_high_limit")
    val tl = row.getAs[java.math.BigDecimal]("temperature_low_limit")
    val hh = row.getAs[java.math.BigDecimal]("humidity_high_limit")
    val hl = row.getAs[java.math.BigDecimal]("humidity_low_limit")
    val date = row.getAs[Date]("date")
    
    // 提取设备标识符（去掉仓库编码前缀）
    val deviceTag = sensorCodeOrigin
    
    if (deviceMap.contains(deviceTag)) {
      val fromTime = DateUtil.ymdhm().format(date)
      val toTime = DateUtil.ymdhm().format(new Date(date.getTime + 24 * 60 * 60 * 1000 - 1))
      
      // 获取正常数据 (coll=0)
      val normalData = getDeviceHistoryData(apiUrl, sessid, deviceTag, fromTime, toTime, 0)
      
      normalData.foreach(record => {
        val timeStr = record.get("time").toString
        val dataMap = record.get("data").asInstanceOf[Map[String, String]]
        
        try {
          val time = DateUtil.ymdhm().parse(timeStr)
          
          // 处理温度数据 (通道1)
          dataMap.get("1").foreach(tempStr => {
            if (tempStr != "ERR" && tempStr.nonEmpty) {
              try {
                val temperature = new java.math.BigDecimal(tempStr)
                val tt = new WarehouseSensorRecord.Tags
                val ts = SensorConsts.toStatus(temperature, th, tl)
                tt.setWarehouseCode(warehouseCode)
                  .setRegionCode(regionCode)
                  .setSensorCode(sensorCode)
                  .setStatus(ts)
                  .setType(SensorConsts.TYPE_TEMPERATURE)
                  .setNormal(if (SensorConsts.STATUS_NORMAL.eq(ts)) CommonConsts.TRUE else CommonConsts.FALSE)
                val tr = new WarehouseSensorRecord
                tr.setValue(temperature)
                  .setTimestamp(time.getTime / 1000L)
                  .setTags(tt)
                openTSDBClient.put(tr)
              } catch {
                case e: Exception => println(s"处理温度数据失败: ${e.getMessage}")
              }
            }
          })
          
          // 处理湿度数据 (通道2)
          dataMap.get("2").foreach(humStr => {
            if (humStr != "ERR" && humStr.nonEmpty) {
              try {
                val humidity = new java.math.BigDecimal(humStr)
                val ht = new WarehouseSensorRecord.Tags
                val hs = SensorConsts.toStatus(humidity, hh, hl)
                ht.setWarehouseCode(warehouseCode)
                  .setRegionCode(regionCode)
                  .setSensorCode(sensorCode)
                  .setStatus(hs)
                  .setType(SensorConsts.TYPE_HUMIDITY)
                  .setNormal(if (SensorConsts.STATUS_NORMAL.eq(hs)) CommonConsts.TRUE else CommonConsts.FALSE)
                val hr = new WarehouseSensorRecord
                hr.setValue(humidity)
                  .setTimestamp(time.getTime / 1000L)
                  .setTags(ht)
                openTSDBClient.put(hr)
              } catch {
                case e: Exception => println(s"处理湿度数据失败: ${e.getMessage}")
              }
            }
          })
          
        } catch {
          case e: Exception => println(s"解析时间失败: ${timeStr}, ${e.getMessage}")
        }
      })
      
      // 获取异常数据 (coll=1)
      val alarmData = getDeviceHistoryData(apiUrl, sessid, deviceTag, fromTime, toTime, 1)
      
      alarmData.foreach(record => {
        val timeStr = record.get("time").toString
        val solve = record.get("solve").toString
        val solveUser = record.get("solve_user").toString
        val solveTime = record.get("solve_time").toString
        
        // 只处理有处理措施的异常数据
        if (solve.nonEmpty && solveUser.nonEmpty && solveTime.nonEmpty) {
          try {
            val time = DateUtil.ymdhm().parse(timeStr)
            
            // 创建异常处理事件（这里需要根据实际需求发送到Kafka或其他处理）
            val overLimitHandlingEvent = new OverLimitHandlingEvent()
            overLimitHandlingEvent.setWarehouseCode(warehouseCode)
            overLimitHandlingEvent.setRegionCode(regionCode)
            overLimitHandlingEvent.setSensorCode(sensorCode)
            overLimitHandlingEvent.setStartTime(time.getTime)
            overLimitHandlingEvent.setRange(false)
            overLimitHandlingEvent.setPrincipal(solveUser)
            overLimitHandlingEvent.setSuggestion(solve)
            
            println(s"异常处理记录: ${sensorCode}, 时间: ${timeStr}, 处理人: ${solveUser}, 处理措施: ${solve}")
            
          } catch {
            case e: Exception => println(s"处理异常数据失败: ${e.getMessage}")
          }
        }
      })
      
    } else {
      println(s"设备 ${deviceTag} 未在设备列表中找到")
    }
  })
})

println("数据补充完成！")
