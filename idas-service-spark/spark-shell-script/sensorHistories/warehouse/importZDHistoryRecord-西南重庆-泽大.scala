import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")

import java.sql.Date

import com.gtg56.idas.common.consts._
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord
import com.gtg56.idas.common.tool.http.OpenTSDBClient
import com.gtg56.idas.common.tool.http.wsdl.ZDClient
import com.gtg56.idas.common.util.DateUtil
import spark.implicits._

import scala.collection.JavaConversions._

// 修改下列变量
val warehouseCode = "CQYYWLZX001"
val wsdlAddr = "http://10.10.16.32:8200/BSQSdk?wsdl"
val startDate = DateUtil.ymdhms().parse("2025-05-19 11:00:00")
val endDate = DateUtil.ymdhms().parse("2025-05-22 11:00:00")

val tsdbAddr = "http://10.1.128.17:4242"
val jdbcUrl = "***********************************************************************************************************************************************************"
val jdbcUser = "idas"
val jdbcPasswd = "Idas#2020"

val diff = DateUtil.dayDiff(startDate, endDate)

val prop = new java.util.Properties()
prop.setProperty("user", jdbcUser)
prop.setProperty("password", jdbcPasswd)
prop.setProperty("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
val sensor = spark.read.jdbc(jdbcUrl, "warehouse_sensor", prop).where(s"warehouse_code = '${warehouseCode}' and corp_code = '${SensorConsts.CORP_ZD}'")

val dates = spark.range(0, diff).map(i => new Date(DateUtil.dayAdd(startDate, i.toInt).getTime.getTime)).selectExpr("value as date")

val sensorDates = sensor.crossJoin(dates).repartition(200)

sensorDates.foreachPartition(it => {
  val zdClient = new ZDClient(wsdlAddr, "wuhan", "6560777d")
  val openTSDBClient = new OpenTSDBClient(tsdbAddr)

  it.foreach(row => {
    val devId = row.getAs[String]("sensor_code_origin")
    val sensorCode = row.getAs[String]("sensor_code")
    val regionCode = row.getAs[String]("region_code")
    val th = row.getAs[java.math.BigDecimal]("temperature_high_limit")
    val tl = row.getAs[java.math.BigDecimal]("temperature_low_limit")
    val hh = row.getAs[java.math.BigDecimal]("humidity_high_limit")
    val hl = row.getAs[java.math.BigDecimal]("humidity_low_limit")
    val date = row.getAs[Date]("date")
    val his = zdClient.getHistory(devId, date, 1000)
    his.toSeq.foreach(h => {
      val time = DateUtil.ymdhms.parse(h.getCjsj)
      val temperature = scala.util.Try(new java.math.BigDecimal(h.getWdz))
      val humidity = scala.util.Try(new java.math.BigDecimal(h.getSdz))

      if (temperature.isSuccess) {
        val t = temperature.get
        val tt = new WarehouseSensorRecord.Tags
        val ts = SensorConsts.toStatus(t, th, tl)
        tt.setWarehouseCode(warehouseCode)
          .setRegionCode(regionCode)
          .setSensorCode(sensorCode)
          .setStatus(ts)
          .setType(SensorConsts.TYPE_TEMPERATURE)
          .setNormal(if (SensorConsts.STATUS_NORMAL.eq(ts)) CommonConsts.TRUE else CommonConsts.FALSE)
        val tr = new WarehouseSensorRecord
        tr.setValue(t)
          .setTimestamp(time.getTime / 1000L)
          .setTags(tt)
        openTSDBClient.put(tr)

      }

      if (humidity.isSuccess) {
        val h = humidity.get
        val ht = new WarehouseSensorRecord.Tags
        val hs = SensorConsts.toStatus(h, hh, hl)
        ht.setWarehouseCode(warehouseCode)
          .setRegionCode(regionCode)
          .setSensorCode(sensorCode)
          .setStatus(hs)
          .setType(SensorConsts.TYPE_HUMIDITY)
          .setNormal(if (SensorConsts.STATUS_NORMAL.eq(hs)) CommonConsts.TRUE else CommonConsts.FALSE)
        val hr = new WarehouseSensorRecord
        hr.setValue(h)
          .setTimestamp(time.getTime / 1000L)
          .setTags(ht)
        openTSDBClient.put(hr)
      }
    })
  })
})
