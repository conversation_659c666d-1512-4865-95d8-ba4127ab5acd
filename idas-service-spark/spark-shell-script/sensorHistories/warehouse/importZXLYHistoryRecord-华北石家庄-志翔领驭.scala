import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")
// 修改下列变量
val url = "*****************************************************************************************************************************"
val user = "admin"
val password = "IdasRds#2021"
val warehouseCode = "HBSJZYYWLZX"
val tsdbUrl = "http://10.1.128.17:4242"

val devTimeMin = "2025-06-16 01:00:00"
val devTimeMax = "2025-06-16 10:00:00"

val realMinMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url, user, password, "real_temphum", "id").split(",")
val hisMinMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url, user, password, "record_temphum", "id").split(",")

val real = spark.read.format("jdbc")
  .option("url", url).option("user", user).option("password", password).option("dbtable", "real_temphum").option("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
  .option("partitionColumn", "id").option("lowerBound", realMinMax(0)).option("upperBound", realMinMax(1)).option("numPartitions", 200)
  .load
val his = spark.read.format("jdbc")
  .option("url", url).option("user", user).option("password", password).option("dbtable", "record_temphum").option("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
  .option("partitionColumn", "id").option("lowerBound", hisMinMax(0)).option("upperBound", hisMinMax(1)).option("numPartitions", 2000)
  .load

real.union(his).createOrReplaceTempView("tmp_data")

spark.read.format("jdbc")
  .option("url", url).option("user", user).option("password", password).option("dbtable", "cdpz").option("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
  .load
  .createOrReplaceTempView("tmp_config")

val res = spark.sql(
  s"""
     |SELECT d.AreaCode ac , concat('${warehouseCode}-',d.DeviceCode) as dc , d.wdz , d.sdz , d.dt ,
     |c.WDBJSX th , c.WDBJXX tl , c.SDBJSX hh , c.SDBJXX hl
     |FROM tmp_data d
     |JOIN tmp_config c
     |ON d.DeviceCode = c.DeviceCode
     |WHERE d.dt >= '${devTimeMin}' and d.dt <= '${devTimeMax}'
     |""".stripMargin)

import java.sql.Timestamp

import com.gtg56.idas.common.consts.{CommonConsts, SensorConsts}
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord

val fm = (row: org.apache.spark.sql.Row) => {
  val regionCode = row.getAs[String](0)
  val sensorCode = row.getAs[String](1)
  val t = new java.math.BigDecimal(row.getAs[Float](2).toString)
  val h = new java.math.BigDecimal(row.getAs[Float](3).toString)
  val time = row.getAs[Timestamp](4).getTime / 1000
  val th = new java.math.BigDecimal(row.getAs[Float](5).toString)
  val tl = new java.math.BigDecimal(row.getAs[Float](6).toString)
  val hh = new java.math.BigDecimal(row.getAs[Float](7).toString)
  val hl = new java.math.BigDecimal(row.getAs[Float](8).toString)

  val tem = new WarehouseSensorRecord
  val temTags = new WarehouseSensorRecord.Tags
  temTags.setWarehouseCode(warehouseCode)
    .setRegionCode(regionCode)
    .setSensorCode(sensorCode)
    .setStatus(SensorConsts.toStatus(t, th, tl))
    .setType(SensorConsts.TYPE_TEMPERATURE)
    .setNormal(if (SensorConsts.STATUS_NORMAL == temTags.getStatus) CommonConsts.TRUE
    else CommonConsts.FALSE)
  tem.setValue(t).setTimestamp(time).setTags(temTags)

  val hum = new WarehouseSensorRecord
  val humTags = new WarehouseSensorRecord.Tags
  humTags.setWarehouseCode(warehouseCode)
    .setRegionCode(regionCode)
    .setSensorCode(sensorCode)
    .setStatus(SensorConsts.toStatus(h, hh, hl))
    .setType(SensorConsts.TYPE_HUMIDITY)
    .setNormal(if (SensorConsts.STATUS_NORMAL == humTags.getStatus) CommonConsts.TRUE else CommonConsts.FALSE)
  hum.setValue(h).setTimestamp(time).setTags(humTags)

  List(tem, hum)
}

val sr = res.repartition(200).rdd.flatMap(fm)

sr.foreachPartition(it => {
  val client = new com.gtg56.idas.common.tool.http.OpenTSDBClient(tsdbUrl)
  it.foreach(r => client.put(r, false))
})
