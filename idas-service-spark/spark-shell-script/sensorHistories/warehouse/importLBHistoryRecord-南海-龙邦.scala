import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")
// 修改下列变量
val url = "******************************************************************************************************************************"
val user = "lbcx"
val password = "longbangv5"
val warehouseCode = "NHYYWLZX001"
val tsdbUrl = "http://10.1.128.17:4242"
val devTimeMin = "2025-03-24 09:00:00"
val devTimeMax = "2025-03-24 11:00:00"

//val cnt = com.gtg56.idas.common.util.JDBCUtil.count(url,user,password,"data_home")

val his = spark.read.format("jdbc")
  .option("url", url)
  .option("user", user)
  .option("password", password)
  .option("dbtable", s"(select * FROM data_home where devtime >= '$devTimeMin' and devtime <= '$devTimeMax') t")
  //  .option("partitionColumn", "createDate")
  //  .option("lowerBound", devTimeMin)
  //  .option("upperBound", devTimeMax)
  //  .option("numPartitions", 200)
  .option("driver", classOf[com.mysql.cj.jdbc.Driver].getName)
  .load

val dev = spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "lb_device_information").option("driver", classOf[com.mysql.cj.jdbc.Driver].getName).load
val wh = spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "lb_house_type").option("driver", classOf[com.mysql.cj.jdbc.Driver].getName).load

his.createOrReplaceTempView("tmp_his")
dev.createOrReplaceTempView("tmp_dev")
wh.createOrReplaceTempView("tmp_wh")

val join = spark.sql(
  """
    |SELECT
    |b.temperature,b.humidity,b.devTime as devTime,
    |h.id as regionCode,h.`name` as regionName,
    |concat(d.measureCode,'_',d.meterNo) as sensorCodeOrigin, d.terminalname as sensorName,
    |d.t_high as temperatureHighLimit ,d.t_low as temperatureLowLimit,d.h_high as humidityHighLimit,d.h_low as humidityLowLimit
    |FROM tmp_his b
    |LEFT JOIN tmp_dev d
    |ON b.measureCode = d.measureCode and b.meterNo = d.meterNo
    |LEFT JOIN tmp_wh h
    |ON d.house_code = h.id
    |WHERE b.temperature is not null and b.humidity is not null
    |""".stripMargin).cache


import com.gtg56.idas.common.consts.{CommonConsts, SensorConsts}
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord
import com.gtg56.idas.common.tool.http.WarehouseSensorClient

join.repartition(join.col("devTime")).foreachPartition(it => {
  val client = new WarehouseSensorClient(tsdbUrl)
  it.foreach(row => {
    val regionCode = row.getString(3)
    val sensorCodeOrigin = row.getString(5)
    val sensorCode = warehouseCode + "-" + sensorCodeOrigin

    val t = new java.math.BigDecimal(row.getDouble(0).toString)
    val th = new java.math.BigDecimal(row.getDouble(7).toString)
    val tl = new java.math.BigDecimal(row.getDouble(8).toString)
    val h = new java.math.BigDecimal(row.getDouble(1).toString)
    val hh = new java.math.BigDecimal(row.getDouble(9).toString)
    val hl = new java.math.BigDecimal(row.getDouble(10).toString)


    val time = row.getTimestamp(2).getTime / 1000L

    val tem = new WarehouseSensorRecord
    val temTags = new WarehouseSensorRecord.Tags
    temTags.setWarehouseCode(warehouseCode)
      .setRegionCode(regionCode)
      .setSensorCode(sensorCode)
      .setStatus(SensorConsts.toStatus(t, th, tl))
      .setType(SensorConsts.TYPE_TEMPERATURE)
      .setNormal(if (SensorConsts.STATUS_NORMAL == temTags.getStatus) CommonConsts.TRUE
      else CommonConsts.FALSE)
    tem.setValue(t).setTimestamp(time).setTags(temTags)

    val hum = new WarehouseSensorRecord
    val humTags = new WarehouseSensorRecord.Tags
    humTags.setWarehouseCode(warehouseCode)
      .setRegionCode(regionCode)
      .setSensorCode(sensorCode)
      .setStatus(SensorConsts.toStatus(h, hh, hl))
      .setType(SensorConsts.TYPE_HUMIDITY)
      .setNormal(if (SensorConsts.STATUS_NORMAL == humTags.getStatus) CommonConsts.TRUE else CommonConsts.FALSE)
    hum.setValue(h).setTimestamp(time).setTags(humTags)

    client.put(tem)
    client.put(hum)
  })
})