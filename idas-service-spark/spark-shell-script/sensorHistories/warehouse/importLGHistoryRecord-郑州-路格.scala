import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制


spark.sparkContext.setLogLevel("warn")
// 修改下列变量
val url = "******************************************************"
val user = "sa"
val password = "sa123"
val warehouseCode = "GZZ002"
val tsdbUrl = "http://10.1.128.17:4242"

val timeBetween = " BETWEEN '2025-06-10 01:00:00' AND '2025-06-11 01:30:00'"
//
val dataWhere = "DevTime " + timeBetween;
val dataMinMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url, user, password, "dbo.Record_Data", "Id", dataWhere).split(",")
//
val channelWhere = "DevTime " + timeBetween;
val channelMinMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url, user, password, "dbo.Record_Channel", "Id", channelWhere).split(",")

spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.Record_Data").option("partitionColumn", "Id").option("lowerBound", dataMinMax(0)).option("upperBound", dataMinMax(1)).option("numPartitions", 200).load.createOrReplaceTempView("tmp_data")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.Record_Channel").option("partitionColumn", "Id").option("lowerBound", channelMinMax(0)).option("upperBound", channelMinMax(1)).option("numPartitions", 200).load.createOrReplaceTempView("tmp_channel")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.Dev_Info").load.createOrReplaceTempView("tmp_dev_info")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.Dev_Channel").load.createOrReplaceTempView("tmp_dev_channel")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.Warehouse_Dev").load.createOrReplaceTempView("tmp_warehouse_dev")
spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "dbo.WareHouse_Info").load.createOrReplaceTempView("tmp_warehouse_info")

val res = spark.sql(
  s"""
    |SELECT rd.Id  as recordId ,
    |	 wh.Id as orgCode,
    |	 wh.Name as orgName ,
    |	 dev.SN as nodeCode ,
    |	 dev.Name as nodeName ,
    |	 rct.Value as temperature ,
    |	 rch.Value as humidity ,
    |	 tc.Upper as temperatureHighLimit,
    |	 tc.Lower as temperatureLowLimit,
    |	 hc.Upper as humidityHighLimit,
    |	 hc.Lower as humidityLowLimit ,
    |	 rd.DevTime as recordDateTime,
    |	 rd.OverReason as overReason
    |FROM tmp_data rd
    |LEFT JOIN tmp_channel rct ON
    |	 rd.Id = rct.DataId
    |	 and rct.ChannelPort = 1
    |LEFT JOIN tmp_channel rch ON
    |	 rd.Id = rch.DataId
    |	 and rch.ChannelPort = 2
    |LEFT JOIN tmp_dev_info dev ON
    |	 rd.SN = dev.SN
    |LEFT JOIN tmp_dev_channel tc ON
    |	 dev.SN = tc.SN
    |	 and tc.Index = 1
    |LEFT JOIN tmp_dev_channel hc ON
    |	 dev.SN = hc.SN
    |	 and hc.Index = 2
    |LEFT JOIN tmp_warehouse_dev whd ON
    |	 whd.DevId = dev.Id
    |LEFT JOIN tmp_warehouse_info wh ON
    |	 wh.Id = whd.WareHouseId
    |WHERE wh.Id is not null
    | AND rd.DevTime ${timeBetween}
    |""".stripMargin).cache

val fm = (row: org.apache.spark.sql.Row) => {
  val orgCode = row.getAs[Int](1)
  val nodeCode = warehouseCode + "-" + row.getAs[String](3)
  val tempuraTure = java.math.BigDecimal.valueOf(row.getAs[Double](5))
  val humidity = java.math.BigDecimal.valueOf(row.getAs[Double](6))
  val temperatureHighLimit = java.math.BigDecimal.valueOf(row.getAs[Double](7))
  val temperatureLowLimit = java.math.BigDecimal.valueOf(row.getAs[Double](8))
  val humidityHighLimit = java.math.BigDecimal.valueOf(row.getAs[Double](9))
  val humidityLowLimit = java.math.BigDecimal.valueOf(row.getAs[Double](10))
  val recordTime = row.getAs[java.sql.Timestamp](11)

  import com.gtg56.idas.common.consts.{CommonConsts, SensorConsts}

  val temStatus = SensorConsts.toStatus(tempuraTure, temperatureHighLimit, temperatureLowLimit);
  val humStatus = SensorConsts.toStatus(humidity, humidityHighLimit, humidityLowLimit)

  val temNormal = if (SensorConsts.STATUS_NORMAL.equals(temStatus)) CommonConsts.TRUE else CommonConsts.FALSE
  val humNormal = if (SensorConsts.STATUS_NORMAL.equals(humStatus)) CommonConsts.TRUE else CommonConsts.FALSE

  import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord

  val time = recordTime.getTime / 1000
  val temTags = new WarehouseSensorRecord.Tags()
    .setWarehouseCode(warehouseCode)
    .setRegionCode(orgCode.toString)
    .setSensorCode(nodeCode)
    .setType(SensorConsts.TYPE_TEMPERATURE)
    .setStatus(temStatus)
    .setNormal(temNormal)

  val tem = new WarehouseSensorRecord()
    .setValue(tempuraTure)
    .setTimestamp(time)
    .setTags(temTags)

  val humTags = new WarehouseSensorRecord.Tags()
    .setWarehouseCode(warehouseCode)
    .setRegionCode(orgCode.toString)
    .setSensorCode(nodeCode)
    .setType(SensorConsts.TYPE_HUMIDITY)
    .setStatus(humStatus)
    .setNormal(humNormal)

  val hum = new WarehouseSensorRecord()
    .setValue(humidity)
    .setTimestamp(time)
    .setTags(humTags)

  List(tem, hum)
}

val sr = res.repartition(res.col("recordId")).rdd.flatMap(fm)

sr.foreachPartition(it => {
  val client = new com.gtg56.idas.common.tool.http.OpenTSDBClient(tsdbUrl)
  it.foreach(r => client.put(r, false))
})

val over = res.filter("overReason is not null and overReason != ''")

over.foreachPartition(it => {
  import java.util.Date

  import com.alibaba.fastjson.JSON
  import com.gtg56.idas.common.consts.SensorConsts
  import com.gtg56.idas.common.dao.hbase.OverLimitHandlingDAO
  import com.gtg56.idas.common.entity.hbase.OverLimitHandling
  import com.gtg56.idas.common.util.DateUtil
  import org.apache.commons.lang3.StringUtils

  val dao = OverLimitHandlingDAO.getInstance
  it.foreach(row => {
    val orgCode = row.getAs[Int](1)
    val nodeCode = warehouseCode + "-" + row.getAs[String](3)
    val tempuraTure = java.math.BigDecimal.valueOf(row.getAs[Double](5))
    val humidity = java.math.BigDecimal.valueOf(row.getAs[Double](6))
    val temperatureHighLimit = java.math.BigDecimal.valueOf(row.getAs[Double](7))
    val temperatureLowLimit = java.math.BigDecimal.valueOf(row.getAs[Double](8))
    val humidityHighLimit = java.math.BigDecimal.valueOf(row.getAs[Double](9))
    val humidityLowLimit = java.math.BigDecimal.valueOf(row.getAs[Double](10))
    val recordTime = row.getAs[java.sql.Timestamp](11)
    val overReason = row.getAs[String](12)

    val temStatus = SensorConsts.toStatus(tempuraTure, temperatureHighLimit, temperatureLowLimit);
    val humStatus = SensorConsts.toStatus(humidity, humidityHighLimit, humidityLowLimit)

    val overJson =
      if (overReason.endsWith(",")) {
        val array = JSON.parseArray("[" + StringUtils.left(overReason, overReason.length - 1) + "]")
        if (array.size == 0) null
        //如果Json 数组只有一个，那么取当前，如果不是，则取最后一个
        array.getJSONObject(if (array.size == 1) 0
        else array.size - (array.size - 1))
      } else {
        JSON.parseObject(overReason)
      }
    if (overJson != null) {
      val date = overJson.getString("adddate")
      val time = overJson.getString("addtime")
      val ymdhms = date + " " + time + ":00"
      val handleTime = try {
        if (ymdhms.contains("/")) DateUtil.tsdb().parse(ymdhms) else DateUtil.ymdhms().parse(ymdhms)
      } catch {
        case e: Exception => null
      }
      if (handleTime != null) {
        val olh = new OverLimitHandling
        olh.setCorpCode(SensorConsts.CORP_LG)
          .setWarehouseCode(warehouseCode)
          .setSensorCode(nodeCode)
          .setTemperature(tempuraTure)
          .setTemperatureHighLimit(temperatureHighLimit)
          .setTemperatureLowLimit(temperatureLowLimit)
          .setTemperatureStatus(temStatus)
          .setHumidity(humidity)
          .setHumidityHighLimit(humidityHighLimit)
          .setHumidityLowLimit(humidityLowLimit)
          .setHumidityStatus(humStatus)
          .setRecordTime(new Date(recordTime.getTime))
          .setHandleTime(handleTime)
          .setSuggestion(overJson.getString("overreason"))
          .setPrincipal(overJson.getString("userrealname"))

        dao.put(olh)
      }
    }
  })
})
