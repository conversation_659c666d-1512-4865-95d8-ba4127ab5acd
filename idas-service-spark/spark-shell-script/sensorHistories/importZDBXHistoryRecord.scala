import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制


spark.sparkContext.setLogLevel("warn")
// 修改下列变量
val url = "*************************************************"
val user = "sa"
val password = "zdbx"
val warehouseCode = "SSCYL0927"
val tsdbUrl = "http://10.1.128.17:4242"

val minMax = com.gtg56.idas.common.util.JDBCUtil.getTableMinMaxId(url, user, password, "MonitorRecord", "Id").split(",")

spark.read.format("jdbc")
	.option("url", url).option("user", user).option("password", password).option("dbtable", "MonitorRecord")
	.option("partitionColumn", "Id").option("lowerBound", minMax(0)).option("upperBound", minMax(1)).option("numPartitions", 200)
	.load
	//  .where("RecordDateTime >= '2021-01-19 00:00:00'")
	.createOrReplaceTempView("tmp_record")

spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "SlaveNode").load.createOrReplaceTempView("tmp_slave")

spark.read.format("jdbc").option("url", url).option("user", user).option("password", password).option("dbtable", "Organization").load.createOrReplaceTempView("tmp_org")

val res = spark.sql("SELECT cr.Id as recordId,\n" +
            "       O.Code as orgCode,O.Name as orgName,cr.NodeId as nodeCode,sn.Name as nodeName,\n" +
            "       cr.Temperature as temperature ,cr.Humidity as humidity ,\n" +
            "       sn.TemperatureHighLimit as temperatureHighLimit,sn.TemperatureLowLimit as temperatureLowLimit,\n" +
            "       sn.HumidityHighLimit as humidityHighLimit,sn.HumidityLowLimit as humidityLowLimit ,\n" +
            "       cr.RecordDateTime as recordDateTime\n" +
            "	 FROM tmp_record cr \n" +
            "    JOIN tmp_slave sn ON cr.NodeId = sn.NodeId\n" +
            "    JOIN tmp_org O on sn.OrganizationId = O.Id").na.drop

val fm = (row : org.apache.spark.sql.Row) => {
	val orgCode = row.getAs[String](1).replaceAll("－", "-").replace("- ", "-")
	val nodeCode = warehouseCode + "-" + row.getAs[String](3)
	val tempuraTure = row.getAs[java.math.BigDecimal](5)
	val humidity = row.getAs[java.math.BigDecimal](6)
	val temperatureHighLimit = row.getAs[java.math.BigDecimal](7)
	val temperatureLowLimit = row.getAs[java.math.BigDecimal](8)
	val humidityHighLimit = row.getAs[java.math.BigDecimal](9)
	val humidityLowLimit = row.getAs[java.math.BigDecimal](10)
	val recordTime = row.getAs[java.sql.Timestamp](11)

	val temStatus = com.gtg56.idas.common.consts.SensorConsts.toStatus(tempuraTure,temperatureHighLimit,temperatureLowLimit);
	val humStatus = com.gtg56.idas.common.consts.SensorConsts.toStatus(humidity,humidityHighLimit,humidityLowLimit)

	val temNormal = if(com.gtg56.idas.common.consts.SensorConsts.STATUS_NORMAL.equals(temStatus)) com.gtg56.idas.common.consts.CommonConsts.TRUE else com.gtg56.idas.common.consts.CommonConsts.FALSE
	val humNormal = if(com.gtg56.idas.common.consts.SensorConsts.STATUS_NORMAL.equals(humStatus) ) com.gtg56.idas.common.consts.CommonConsts.TRUE else com.gtg56.idas.common.consts.CommonConsts.FALSE

	val time = recordTime.getTime / 1000
	val temTags = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord.Tags().setWarehouseCode(warehouseCode).setRegionCode(orgCode).setSensorCode(nodeCode).setType(com.gtg56.idas.common.consts.SensorConsts.TYPE_TEMPERATURE).setStatus(temStatus).setNormal(temNormal)
	val tem = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord().setValue(tempuraTure).setTimestamp(time).setTags(temTags)

	val humTags = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord.Tags().setWarehouseCode(warehouseCode).setRegionCode(orgCode).setSensorCode(nodeCode).setType(com.gtg56.idas.common.consts.SensorConsts.TYPE_HUMIDITY).setStatus(humStatus).setNormal(humNormal)
	val hum = new com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord().setValue(humidity).setTimestamp(time).setTags(humTags)

	List(tem,hum)
}

val sr = res.repartition(res.col("recordId")).rdd.flatMap(fm)

sr.foreachPartition(it => {
	val client = new com.gtg56.idas.common.tool.http.OpenTSDBClient(tsdbUrl)
	it.foreach(r => client.put(r,false))
})