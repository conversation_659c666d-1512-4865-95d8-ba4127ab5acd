import org.apache.spark.sql.SparkSession

val spark = SparkSession.builder.getOrCreate
// 上面不用复制

spark.sparkContext.setLogLevel("warn")

import java.time.Duration
import java.util.Date

import com.gtg56.idas.common.consts._
import com.gtg56.idas.common.entity.sensor.sd._
import com.gtg56.idas.common.entity.tsdb.WarehouseSensorRecord
import com.gtg56.idas.common.tool.http.{OpenTSDBClient, SDColdCloudClient}
import com.gtg56.idas.common.util.DateUtil
import spark.implicits._

import scala.collection.JavaConverters._

// 修改下列变量
// token在浏览器登录后F12看
val token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEwMDEsIk1vYmlsZVBob25lIjoiMTUzMzUyNzUxMzQiLCJVc2VyTmFtZSI6IumZhuaWh-iJsyIsIlVzZXJUeXBlSWQiOjQsIkN1c3RvbWVySWQiOjQyOCwiZXhwIjoxNjI2OTIyMzE3LCJpYXQiOjE2MjQzMzAzMTd9.vWPhWh_LROzow2KA2poa3QV96OHR1s6hkEtPO1LLbio"
val customerId = 428
val deviceId = 1596
val warehouseCode = "GKS002"
val warehouseName = "广交华东昆山二仓"
val startDate = DateUtil.ymd().parse("2020-04-28")
val endDate = DateUtil.ymd().parse("2021-05-15")
val tsdbAddr = "http://10.1.9.63:4242"
val jdbcUrl = "********************************************************************************************************************************************************"
val jdbcUser = "root"
val jdbcPasswd = "CdhDB_321"

val diff = DateUtil.dayDiff(startDate, endDate)

//sensor.repartition(1).write.csv("/user/hdfs/sd.csv")

//val dates = scala.collection.immutable.Seq.range(0,diff).map(i => DateUtil.dayAdd(startDate,i).getTime)

val dates = spark.range(0, diff)
  .map(i => DateUtil.dayAdd(startDate, i.toInt).getTime.getTime)
  .flatMap(i => {
    val times = new scala.collection.mutable.MutableList[Long]
    for (a <- 0 to 23) {
      times += (i + Duration.ofHours(a).toMillis)
    }
    times
  })
  .selectExpr("value as t").repartition(200)

val data = dates.flatMap(row => {
  val t = row.getAs[Long]("t")
  val from = new Date(t)
  val to = new Date(from.getTime + Duration.ofHours(1).toMillis)
  val cc = new SDColdCloudClient(token, customerId, deviceId)
  cc.listHistoryRecord(from, to).asScala.map((t: SDColdCloudSensorHistoryRecord) => t.toJson)
})

data.write.mode("overwrite").text("/user/hdfs/sd_ks2.json")

spark.read.json("/user/hdfs/sd_ks2.json").repartition(200, $"operdate").createOrReplaceTempView("tmp_record")


// 下面是获取测点配置，
val client = new SDColdCloudClient(token, customerId, deviceId)
val sensorOrigin = spark.read.json(spark.createDataset[String](client.listAllSensor.asScala.map(_.toJson)))
val sensor = sensorOrigin.selectExpr(
  s"'${SensorConsts.CORP_SD}' as corpCode",
  s"'${warehouseCode}' as warehouseCode",
  s"'${warehouseName}' as warehouseName",
  "cast(warehouseid as string) as regionCode",
  "concat(warehousename,'（归档）') as regionName",
  "sn as sensorCodeOrigin",
  s"concat('${warehouseCode}-',sn) as sensorCode",
  "concat('#',sensorcode,'（归档）') as sensorName ",
  "sensorcode as sc"
)
sensor.persist.createOrReplaceTempView("tmp_sensor")

val s = spark.sql(
  s"""
     |SELECT
     |s.regionCode,
     |s.sensorCode,
     |
     |cast(r.temperature as decimal(10,2)) as temperature,
     |cast(r.maxtemperature as decimal(10,2)) as temperature_high_limit,
     |cast(r.mintemperature as decimal(10,2)) as temperature_low_limit,
     |cast(r.humidity as decimal(10,2)) as humidity,
     |cast(r.maxhumidity as decimal(10,2)) as humidity_high_limit,
     |cast(r.minhumidity as decimal(10,2)) as humidity_low_limit,
     |r.operdate
     |
     |FROM tmp_record r
     |JOIN tmp_sensor s
     |ON cast(r.sensorcode as string) = s.sc
     |""".stripMargin)

s.foreachPartition(it => {
  val tsdbClient = new OpenTSDBClient(tsdbAddr)
  it.foreach(row => {
    val sensorCode = row.getAs[String]("sensorCode")
    val regionCode = row.getAs[String]("regionCode")
    val t = row.getAs[java.math.BigDecimal]("temperature")
    val th = row.getAs[java.math.BigDecimal]("temperature_high_limit")
    val tl = row.getAs[java.math.BigDecimal]("temperature_low_limit")
    val h = row.getAs[java.math.BigDecimal]("humidity")
    val hh = row.getAs[java.math.BigDecimal]("humidity_high_limit")
    val hl = row.getAs[java.math.BigDecimal]("humidity_low_limit")
    val time = row.getAs[Long]("operdate")

    if (t != null) {
      val tt = new WarehouseSensorRecord.Tags
      val ts = SensorConsts.toStatus(t, th, tl)
      tt.setWarehouseCode(warehouseCode)
        .setRegionCode(regionCode)
        .setSensorCode(sensorCode)
        .setStatus(ts)
        .setType(SensorConsts.TYPE_TEMPERATURE)
        .setNormal(if (SensorConsts.STATUS_NORMAL.eq(ts)) CommonConsts.TRUE else CommonConsts.FALSE)
      val tr = new WarehouseSensorRecord
      tr.setValue(t)
        .setTimestamp(time / 1000L)
        .setTags(tt)
      tsdbClient.put(tr)
    }

    if (h != null) {
      val ht = new WarehouseSensorRecord.Tags
      val hs = SensorConsts.toStatus(h, hh, hl)
      ht.setWarehouseCode(warehouseCode)
        .setRegionCode(regionCode)
        .setSensorCode(sensorCode)
        .setStatus(hs)
        .setType(SensorConsts.TYPE_HUMIDITY)
        .setNormal(if (SensorConsts.STATUS_NORMAL.eq(hs)) CommonConsts.TRUE else CommonConsts.FALSE)
      val hr = new WarehouseSensorRecord
      hr.setValue(h)
        .setTimestamp(time / 1000L)
        .setTags(ht)
      tsdbClient.put(hr)
    }
  })
})


