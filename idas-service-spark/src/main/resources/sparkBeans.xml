<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd




       http://www.springframework.org/schema/util
       https://www.springframework.org/schema/util/spring-util.xsd">

    <!--    spark beans    -->
    <util:properties id="sparkProp" location="classpath:/spark.properties"/>
    <bean id="sparkFactory" class="com.gtg56.idas.service.spark.tool.SparkFactory"/>
    <bean id="spark" factory-bean="sparkFactory" factory-method="build" destroy-method="stop"/>
    <bean id="jSparkContext" factory-bean="sparkFactory" factory-method="getSparkContext">
        <constructor-arg name="spark" ref="spark"/>
    </bean>

    <bean id="sparkInit" class="com.gtg56.idas.service.spark.tool.SparkInit" depends-on="spark" init-method="init">
        <property name="initWorks">
            <util:list id="initWorks" list-class="java.util.ArrayList" value-type="com.gtg56.idas.service.spark.tool.SparkInitWork">
                <bean class="com.gtg56.idas.service.spark.tool.init.SetLogLevel"/>
                <bean class="com.gtg56.idas.service.spark.tool.init.LoadMappingView"/>
                <bean class="com.gtg56.idas.service.spark.tool.init.RegisterUDF"/>
            </util:list>
        </property>
    </bean>
</beans>