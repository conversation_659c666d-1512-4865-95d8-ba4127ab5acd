<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="idas-service-spark" version="1.0" architecture="query"/>
    <dubbo:reference id="workloadProviderService" interface="com.gtg56.idas.service.define.IWorkloadProviderService" check="false" lazy="true"/>

    <import resource="classpath:/spring/hbase.xml"/>
    <import resource="classpath:/spring/redis.xml"/>
    <import resource="classpath:/spring/dubbo.xml"/>
    <import resource="classpath:sparkBeans.xml"/>

    <dubbo:service interface="com.gtg56.idas.service.define.ISparkService" ref="sparkService" protocol="dubbo" registry="zookeeper" filter="-exception"/>
    <dubbo:service interface="com.gtg56.idas.service.define.IDimensionService" ref="dimensionService" protocol="dubbo" registry="zookeeper" filter="-exception"/>

    <bean id="sparkService" class="com.gtg56.idas.service.spark.dubbo.SparkServiceImpl"/>
    <bean id="dimensionService" class="com.gtg56.idas.service.spark.dubbo.DimensionServiceImpl"/>

    <bean id="workloadScheduler" class="com.gtg56.idas.service.spark.schedule.InnerQueueScheduler">
        <property name="schedulerExecutorService" ref="schedulerExecutorService"/>
    </bean>

    <bean id="schedulerWorkQueue" class="java.util.concurrent.LinkedBlockingQueue">
        <constructor-arg name="capacity" value="${spark.scheduler.workQueueCapacity}"/>
    </bean>
    <bean id="schedulerExecutorService" class="java.util.concurrent.ThreadPoolExecutor">
        <constructor-arg name="corePoolSize" value="${spark.scheduler.parallelize}"/>
        <constructor-arg name="maximumPoolSize" value="${spark.scheduler.parallelize}"/>
        <constructor-arg name="keepAliveTime" value="0"/>
        <constructor-arg name="unit" value="MICROSECONDS"/>
        <constructor-arg name="workQueue" ref="schedulerWorkQueue"/>
        <constructor-arg name="handler" ref="workloadScheduler"/>
    </bean>
    <bean id="nodeLRUCache" class="org.apache.dubbo.common.utils.LRUCache">
        <constructor-arg name="maxCapacity" value="10"/>
    </bean>
</beans>
