package com.gtg56.idas.service.spark.udf.date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

public class YMDHMSToTimestamp extends AbstractSparkSqlUdf implements UDF1<String,Long> {
    @Override
    public String registerName() {
        return "ymdhms_to_ts";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.LongType;
    }
    
    @Override
    public Long call(String s) throws Exception {
        if (s == null) return null;
        return DateUtil.ymdhms().parse(s).getTime();
    }
}
