package com.gtg56.idas.service.spark.streaming;

import com.gtg56.idas.common.core.kafka.KafkaProperties;
import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.common.util.StreamUtil;
import com.gtg56.idas.service.spark.StreamingApplication;
import com.gtg56.idas.service.spark.tool.SSKafkaOffsetListener;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.streaming.DataStreamReader;
import org.springframework.core.env.Environment;

import java.io.Serializable;

/**
 * SparkStreaming作业入口
 */
@Slf4j
public abstract class StreamingEntrance implements Serializable {
    
    private static final long serialVersionUID = -487773036618968778L;
    
    public String streamingName() {
        return this.getClass().getSimpleName();
    }
    
    @SneakyThrows
    public void init(Environment env, SparkSession spark, JavaSparkContext jsc, FileSystem fs) {
        this.env = env;
        String checkPoint = env.getProperty("spark.streaming.checkpont");
        if (StringUtils.isBlank(checkPoint)) {
            this.checkPoint = String.format("/tmp/checkpoint_%s", streamingName());
        } else {
            this.checkPoint = checkPoint;
        }
        this.jsc = jsc;
        this.spark = spark;
        this.fs = fs;
    }

    @Getter @Setter
    private transient Environment env;
    @Getter
    private transient SparkSession spark;
    @Getter
    private transient JavaSparkContext jsc;
    @Getter
    private transient String checkPoint;
    @Getter
    private transient FileSystem fs;
    
    /**
     * 流作业入口 重写此方法以实现逻辑
     */
    public abstract void action();
    
    /**
     * 每个StreamingEntrance都是一个独立的Spark Driver JVM，所以每个Driver都只会有一个StreamingEntrance。
     * 见 {@link StreamingArgsHelper} {@link StreamingApplication}
     */
    public transient static StreamingEntrance currentEntrance = null;
    
    /**
     * 从Kafka开启结构流
     *
     * @param topic           topic
     * @param continueConsume 是否不间断消费（即指定offsetPath）
     * @param rate            限制每个Trigger读取的总消息条数，不指定则不限制
     * @return 结构流。key value默认是byte[]需要手动cast。
     * 如 ds.selectExpr("CAST(value as STRING) as event")
     */
    protected Dataset<Row> structuredStreaming(String topic, boolean continueConsume, String rate) {
        if (continueConsume) {
            String offsetPath = "/user/hdfs/idas/structuredStreamingOffsets/" + streamingName() + "Offset.json";
            return structuredStreaming(topic, offsetPath, rate);
        } else {
            return structuredStreaming(topic, null, rate);
        }
    }
    
    /**
     * 从Kafka开启结构流
     *
     * @param topic      topic
     * @param offsetPath 消费偏移存储文件路径（HDFS）。
     *                   若不指定，则每次启动从最新偏移量读取；
     *                   若指定，则启动 {@link SSKafkaOffsetListener}，
     *                   将成功消费的偏移量写入文件。
     *                   重启后从上次成功消费的偏移量重新执行。
     *                   如果文件不存在，则从topic最早偏移量开始
     * @param rate       限制每个Trigger读取的总消息条数，不指定则不限制
     * @return 结构流。key value默认是byte[]需要手动cast。
     * 如 ds.selectExpr("CAST(value as STRING) as event")
     */
    @SneakyThrows
    protected Dataset<Row> structuredStreaming(String topic, String offsetPath, String rate) {
        KafkaProperties consumerProperties = SpringUtil.getBean("kafkaConsumerProperties", KafkaProperties.class);
        String kafkaBrokers = consumerProperties.getProperty("bootstrap.servers");
        
        log.info("start structured streaming from {} topic {}", kafkaBrokers, topic);
        
        DataStreamReader reader = getSpark().readStream()
                .format("kafka")
                .option("kafka.bootstrap.servers", kafkaBrokers)
                .option("subscribe", topic);
        
        if (StringUtils.isNotBlank(offsetPath)) {
            Path path = new Path(offsetPath);
            if (fs.exists(path)) {
                FSDataInputStream fis = fs.open(path);
                byte[] offsetData = StreamUtil.fromStream(fis);
                String offset = new String(offsetData);
                
                reader.option("startingOffsets", offset);
            } else {
                reader.option("startingOffsets", "earliest");
            }
            spark.streams().addListener(new SSKafkaOffsetListener(streamingName(), fs, path));
        } else {
            reader.option("startingOffsets", "latest");
        }
        
        if (StringUtils.isNotBlank(rate)) {
            reader.option("maxOffsetsPerTrigger", rate);
        }
        
        return reader.load();
    }
    
    @SneakyThrows
    protected void awaitStructuredStreaming() {
        spark.streams().awaitAnyTermination();
    }
}
