package com.gtg56.idas.service.spark.tool.init;

import com.gtg56.idas.service.spark.tool.SparkInitWork;
import lombok.Getter;
import lombok.Setter;
import org.apache.log4j.Level;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Value;

public class SetLogLevel implements SparkInitWork {
    
    @Getter @Setter @Value("${logging.level.org.apache}")
    private String logLevel;
    
    @Override
    public void init(SparkSession spark) throws Exception {
        org.apache.log4j.Logger.getLogger("org.apache").setLevel(Level.toLevel(logLevel));
        org.apache.log4j.Logger.getLogger("org.spark_project").setLevel(Level.toLevel(logLevel));
        org.apache.log4j.Logger.getLogger("org.eclipse").setLevel(Level.toLevel(logLevel));
    }
}
