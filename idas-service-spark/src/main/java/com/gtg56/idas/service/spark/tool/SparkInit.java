package com.gtg56.idas.service.spark.tool;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.sql.SparkSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by Link on 2019/12/31.
 * Spark初始化操作
 *
 * sparkFactory -> spark -> sparkInit
 */
@Slf4j
public class SparkInit {
    @Resource(name = "spark")
    private SparkSession spark;

    @Setter
    private List<SparkInitWork> initWorks;

    public void init(SparkSession sparkSession) {
        if (CollectionUtils.isNotEmpty(initWorks)) {
            log.info("start Spark init works , total {} works", initWorks.size());

            initWorks.forEach(work -> {
                String workName = work.name();

                log.info("start Spark init work : {}", workName);
                try {
                    work.init(sparkSession);
                    log.info("Spark init work : {} done ", workName);
                } catch (Throwable t) {
                    log.warn("Spark init work : " + workName + "fail", t);
                }
            });
        } else {
            log.info("no Spark init work");
        }
    }

    public void init() {
        init(spark);
    }

}
