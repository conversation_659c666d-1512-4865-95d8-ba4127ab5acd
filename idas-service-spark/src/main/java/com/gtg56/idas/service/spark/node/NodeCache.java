package com.gtg56.idas.service.spark.node;

import com.gtg56.idas.common.entity.dto.NodeProvideDTO;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeException;
import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.service.define.IDimensionService;
import com.gtg56.idas.service.define.IWorkloadProviderService;
import com.gtg56.idas.service.spark.tool.security.WorkloadNodeChecker;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.LRUCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class NodeCache {
    
    @Resource
    private IWorkloadProviderService workloadProviderService;
    
    @Resource
    private LRUCache<String,Class<? extends WorkloadNode>> nodeLRUCache;
    
    /**
     * 当 workloadProviderService 无法服务时，
     * 尝试从最新一次获得的Node Class返回
     * @param name node name
     * @return workloadNode
     */
    @SneakyThrows
    public WorkloadNode getNode(String name) {
        if("help".equalsIgnoreCase(name)) {
            return new Help(workloadProviderService);
        }
        if("GenDateDim".equalsIgnoreCase(name)) {
            return new GenDateDim(SpringUtil.getBean(IDimensionService.class));
        }
        try {
            NodeProvideDTO nodeProvideDTO = workloadProviderService.provideNode(name);
            WorkloadNodeChecker.check(nodeProvideDTO);
            WorkloadNode node = nodeProvideDTO.build();
            nodeLRUCache.put(name,node.getClass());
            return node;
        } catch (WorkloadNodeException e) {
            throw e;
        } catch (Exception e) {
            log.warn("try to fetch " + name + " from workloadProviderService fail, try to use cache",e);
            if(nodeLRUCache.containsKey(name)) {
                Class<? extends WorkloadNode> nodeClass = nodeLRUCache.get(name);
                log.info("name {} hit cache , class name : {}",name,nodeClass.getName());
                return nodeClass.newInstance();
            } else {
                log.warn("no {} cache hit",name);
                throw WorkloadNodeException.providerServiceNotAvailable(name,e);
            }
        }
    }
}
