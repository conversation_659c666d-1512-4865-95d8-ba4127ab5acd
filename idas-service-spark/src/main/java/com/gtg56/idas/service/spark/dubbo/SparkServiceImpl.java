package com.gtg56.idas.service.spark.dubbo;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.entity.dto.SparkWorkloadNodeInstanceStatusDTO;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.tool.NodeArgsHelper;
import com.gtg56.idas.service.define.ISparkService;
import com.gtg56.idas.service.spark.node.NodeCache;
import com.gtg56.idas.service.spark.schedule.IWorkloadScheduler;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * Created by Link on 2019/12/31.
 */
@Slf4j
public class SparkServiceImpl implements ISparkService {
    
    @Resource
    private IWorkloadScheduler workloadScheduler;
    
    @Resource
    private NodeCache nodeCache;
    
    @Override
    public String startNode(String nodeName, NodeArgsDTO nodeArgs, String role) {
        WorkloadNode workloadNode = nodeCache.getNode(nodeName);
        
        log.info("accept node : {}  args : {} submitting... ",workloadNode.nodeName(),nodeArgs);
        
        return workloadScheduler.submit(workloadNode,nodeArgs);
    }
    
    @Override
    public String startNodeByCli(String nodeName, String[] args, String role) {
        WorkloadNode workloadNode = nodeCache.getNode(nodeName);
        NodeArgsDTO nodeArgsDTO = new NodeArgsHelper(workloadNode).toDTO(args);
        return startNode(nodeName,nodeArgsDTO,role);
    }
    
    @Override
    public SparkWorkloadNodeInstanceStatusDTO getStatus(String instanceId) {
        return workloadScheduler.getStatus(instanceId);
    }
}
