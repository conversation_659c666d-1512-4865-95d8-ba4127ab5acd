package com.gtg56.idas.service.spark.udf.decimal;

import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ToDecimalWithScale2 extends AbstractSparkSqlUdf implements UDF1<String, BigDecimal> {
    private static final long serialVersionUID = 5080713698818847104L;
    
    @Override
    public String registerName() {
        return "to_2scale_decimal";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.createDecimalType(10,2);
    }
    
    @Override
    public BigDecimal call(String s) throws Exception {
        if (StringUtils.isBlank(s)) return null;
        return new BigDecimal(s).setScale(2, RoundingMode.CEILING);
    }
}
