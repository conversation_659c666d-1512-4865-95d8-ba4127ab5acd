package com.gtg56.idas.service.spark;

import com.gtg56.idas.service.spark.streaming.StreamingArgsHelper;
import com.gtg56.idas.service.spark.streaming.StreamingEntrance;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.apache.hadoop.fs.FileSystem;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import tk.mybatis.spring.annotation.MapperScan;

@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.gtg56.idas.common"
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@DubboComponentScan
@MapperScan("com.gtg56.idas.common.mapper")
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(value = {"classpath:/sparkStreamingBeans.xml"})
public class StreamingApplication {
    public static void main(String[] args) {
        StreamingEntrance entrance = StreamingArgsHelper.fromArgs(args);

        ConfigurableApplicationContext context = SpringApplication.run(StreamingApplication.class, args);

        SparkSession spark = context.getBean("spark", SparkSession.class);
        JavaSparkContext jSparkContext = context.getBean("jSparkContext", JavaSparkContext.class);
        FileSystem hdfs = context.getBean("hdfs", FileSystem.class);
        ConfigurableEnvironment env = context.getEnvironment();

        entrance.init(env, spark, jSparkContext, hdfs);

        try {
            entrance.action();
        } catch (Exception e) {
            log.error("catch exception", e);
            spark.stop();
            System.exit(1);
        }
    }
}
