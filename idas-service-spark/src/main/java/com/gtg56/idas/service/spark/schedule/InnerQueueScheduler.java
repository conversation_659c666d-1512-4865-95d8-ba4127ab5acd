package com.gtg56.idas.service.spark.schedule;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.entity.dto.SparkWorkloadNodeInstanceStatusDTO;
import com.gtg56.idas.common.node.NodeStatus;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.common.node.WorkloadNodeInstance;
import com.gtg56.idas.common.node.WorkloadNodeInstanceException;
import com.gtg56.idas.common.util.NetworkUtil;
import com.gtg56.idas.common.util.RedisUtil;
import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.service.spark.tool.SparkInit;
import lombok.AllArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.spark.sql.SparkSession;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class InnerQueueScheduler implements IWorkloadScheduler, RejectedExecutionHandler {
    
    @Resource
    private RedisUtil redisUtil;
    
    @Resource
    private SparkSession spark;
    
    @Setter
    private ThreadPoolExecutor schedulerExecutorService;
    
    @Override
    public String submit(WorkloadNode node, NodeArgsDTO args) {
        String key = genInstanceKey(node);
        
        WorkloadNodeInstance workloadNodeInstance = new WorkloadNodeInstance(node,args,key);
        
        InstanceExecutor instanceExecutor = new InstanceExecutor(workloadNodeInstance, this, spark);
        workloadNodeInstance.setSubmitTime(new Date());
        
        updateStatus(workloadNodeInstance, NodeStatus.SUBMIT);
        schedulerExecutorService.submit(instanceExecutor);
        log.info(
                "submit success , instance id {} , queue remaining capacity {} ",
                key,
                schedulerExecutorService.getQueue().remainingCapacity()
        );
        
        return key;
    }
    
    private String genInstanceKey(WorkloadNode node) {
        return node.nodeName() + "-" + System.currentTimeMillis();
    }
    
    private void instanceStart(WorkloadNodeInstance instance) {
        instance.setExecutionTime(new Date());
        updateStatus(instance, NodeStatus.RUNNING);
    }
    
    private void instanceEnd(WorkloadNodeInstance instance) {
        instance.setEndTime(new Date());
        updateStatus(instance, NodeStatus.SUCCESS_END);
    }
    
    private void instanceOnException(WorkloadNodeInstance instance , Exception e) {
        instance.setLastExceptionLocalized(ExceptionUtils.getStackTrace(e));
        instance.setEndTime(new Date());
        updateStatus(instance, NodeStatus.FAIL_END);
    }
    
    private void instanceReject(WorkloadNodeInstance instance,int remainingCapacity) {
        String msg = "instance was rejected by scheduler executor service , " +
                "instance id " + instance.getInstanceId() +
                " , queue remaining capacity " + remainingCapacity;
        WorkloadNodeInstanceException e = new WorkloadNodeInstanceException(instance, msg);
        Date date = new Date();
        instance.setExecutionTime(date);
        instance.setEndTime(date);
        instance.setLastExceptionLocalized(ExceptionUtils.getStackTrace(e));
        updateStatus(instance,NodeStatus.SUBMIT_REJECT);
    }
    
    @SneakyThrows
    private synchronized void updateStatus(WorkloadNodeInstance instance,String status) {
        SparkWorkloadNodeInstanceStatusDTO dto = new SparkWorkloadNodeInstanceStatusDTO();
        BeanUtils.copyProperties(dto,instance);
        dto.setNodeName(instance.getNode().nodeName());
        dto.setNodeArgs(instance.getArgs());
        dto.setDriverIp(NetworkUtil.getLocalIpv4());
        dto.setStatus(status);
        
        redisUtil.set(redisKey(instance.getInstanceId()),dto);
    }
    
    @Override
    public SparkWorkloadNodeInstanceStatusDTO getStatus(String instanceId) {
        return redisUtil.get(redisKey(instanceId));
    }
    
    private String redisKey(String instanceId) {
        return "SPARK_WORKLOAD_INSTANCE_" + instanceId;
    }
    
    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        if(InstanceExecutor.class.isAssignableFrom(r.getClass())) {
            InstanceExecutor ie = (InstanceExecutor) r;
            int remainingCapacity = executor.getQueue().remainingCapacity();
            instanceReject(ie.instance,remainingCapacity);
        } else {
            log.error("rejectedExecution request not InstanceExecutor task : {} , wtf ? ",r.getClass().getName());
        }
    }
    
    @Slf4j
    @AllArgsConstructor
    private static final class InstanceExecutor implements Runnable {
        private WorkloadNodeInstance instance;
        private InnerQueueScheduler innerQueueScheduler;
        private SparkSession spark;
    
        @Override
        public void run() {
            log.info("instance {} start", instance.getInstanceId());
            innerQueueScheduler.instanceStart(instance);

            if (instance.requireSpark()) {
                SparkInit sparkInit = SpringUtil.getBean("sparkInit", SparkInit.class);
                SparkSession newSpark = spark.newSession();
                sparkInit.init(newSpark);
                instance.setSpark(newSpark);
            }

            instance.setLog(log);

            try {
                log.info("executing instance {} beforeExecution()", instance.getInstanceId());
                instance.beforeExecution();

                log.info("executing instance {} execution()", instance.getInstanceId());
                String returnJson = instance.execution();
                log.debug("instance {} execution done . result : \n {}", instance.getInstanceId(), returnJson);
                instance.setReturnJson(returnJson);
    
                log.info("executing instance {} afterExecution()",instance.getInstanceId());
                instance.afterExecution();

                innerQueueScheduler.instanceEnd(instance);
                log.info("instance {} end", instance.getInstanceId());
            } catch (Exception e) {
                log.warn("instance " + instance.getInstanceId() + " catch exception", e);
                log.info("executing instance {} onException( e : {} )", instance.getInstanceId(), e.getClass().getName());
                instance.onException(e);

                innerQueueScheduler.instanceOnException(instance, e);
            }

            instance.dropSpark();
        }
    }
}
