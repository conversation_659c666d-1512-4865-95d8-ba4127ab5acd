package com.gtg56.idas.service.spark.streaming;

import com.gtg56.idas.common.util.PackageUtil;
import lombok.SneakyThrows;
import org.apache.commons.cli.*;

/**
 * 命令行解析SparkStreaming启动参数
 * 主要是解析 entrance 参数以获取{@link StreamingEntrance}
 */
public class StreamingArgsHelper {
    
    @SneakyThrows
    public static StreamingEntrance fromArgs(String []args) {
        Options options = new Options();
        options.addOption(Option.builder().argName("entrance").longOpt("entrance").hasArg().required().build());
        
        CommandLineParser parser = new DefaultParser();
        CommandLine parse = parser.parse(options, args);
        
        String value = parse.getOptionValue("entrance");
        String myPackage = PackageUtil.getMyPackage();
        String className = myPackage + "." + value;
        
        Class<?> clz = Class.forName(className);
        if(StreamingEntrance.class.isAssignableFrom(clz)) {
            Class<StreamingEntrance> entranceClass = (Class<StreamingEntrance>) clz;
            StreamingEntrance streamingEntrance = entranceClass.newInstance();
            StreamingEntrance.currentEntrance = streamingEntrance;
            return streamingEntrance;
        } else {
            throw new Error("no entrance name " + value);
        }
    }
}
