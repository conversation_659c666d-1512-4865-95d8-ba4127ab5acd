package com.gtg56.idas.service.spark.udf.decimal;

import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

import java.math.BigDecimal;

public class AmountScaleFormat extends AbstractSparkSqlUdf implements UDF1<Number, BigDecimal> {
    private static final long serialVersionUID = -1640369107272974723L;
    
    @Override
    public String registerName() {
        return "amount_scale";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.createDecimalType(10,2);
    }
    
    @Override
    public BigDecimal call(Number number) throws Exception {
        return new BigDecimal(number.toString()).setScale(2,BigDecimal.ROUND_CEILING);
    }
}
