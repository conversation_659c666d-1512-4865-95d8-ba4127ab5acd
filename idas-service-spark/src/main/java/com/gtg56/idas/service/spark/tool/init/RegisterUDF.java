package com.gtg56.idas.service.spark.tool.init;

import com.gtg56.idas.common.util.PackageUtil;
import com.gtg56.idas.service.spark.tool.SparkInitWork;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.UDFRegistration;
import org.apache.spark.sql.api.java.*;

import java.lang.reflect.Modifier;
import java.util.List;

@Slf4j
public class RegisterUDF implements SparkInitWork {
    @Override
    public void init(SparkSession spark) throws Exception {
        List<String> udfs = PackageUtil.getClassName("com.gtg56.idas.service.spark.udf");
    
        UDFRegistration udf = spark.udf();
    
        for(String udfCls : udfs) {
            Class<?> udfClass = this.getClass().getClassLoader().loadClass(udfCls);
            
            if(AbstractSparkSqlUdf.class.isAssignableFrom(udfClass)
                    && !Modifier.isAbstract(udfClass.getModifiers())) {
                
                register(udf, (Class<AbstractSparkSqlUdf>) udfClass);
            }
        }
    }
    
    @SneakyThrows
    private void register(UDFRegistration udf, Class<AbstractSparkSqlUdf> udfClass) {
        AbstractSparkSqlUdf udfInstance = udfClass.newInstance();
        
        if(UDF0.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF0<?>) udfInstance, udfInstance.returnType());
        } else if(UDF1.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF1<?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF2.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF2<?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF3.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF3<?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF4.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF4<?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF5.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF5<?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF6.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF6<?,?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF7.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF7<?,?,?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF8.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF8<?,?,?,?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF9.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF9<?,?,?,?,?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else if(UDF10.class.isAssignableFrom(udfClass)) {
            udf.register(udfInstance.registerName(), (UDF10<?,?,?,?,?,?,?,?,?,?,?>) udfInstance, udfInstance.returnType());
        } else {
            throw new UnsupportedOperationException("unsupported udf " + udfClass.getName() + " , only support arguments num 0 - 10");
        }
        log.info("register udf {} as {}",udfClass.getSimpleName(),udfInstance.registerName());
    }
}
