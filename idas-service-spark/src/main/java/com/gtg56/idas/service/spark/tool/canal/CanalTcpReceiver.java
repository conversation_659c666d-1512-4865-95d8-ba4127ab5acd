package com.gtg56.idas.service.spark.tool.canal;

import org.apache.spark.storage.StorageLevel;
import org.apache.spark.streaming.receiver.Receiver;

public class CanalTcpReceiver extends Receiver<String> {
    private static final long serialVersionUID = 8339913005477354763L;
    
    public CanalTcpReceiver(StorageLevel storageLevel) {
        super(storageLevel);
    }
    
    @Override
    public void onStart() {
    
    }
    
    @Override
    public void onStop() {
    
    }
}
