package com.gtg56.idas.service.spark.udf.date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

public class YMDToDateSplit extends AbstractSparkSqlUdf implements UDF1<String,String> {
    private static final long serialVersionUID = -5381988957164995048L;
    
    @Override
    public String registerName() {
        return "ymd2ds";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.StringType;
    }
    
    @Override
    public String call(String ymd) throws Exception {
        if (StringUtils.isBlank(ymd)) return StringUtils.EMPTY;
        return DateUtil.ds().format(DateUtil.ymd().parse(ymd));
    }
}
