package com.gtg56.idas.service.spark.udf.date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

public class DateSplitToYMD extends AbstractSparkSqlUdf implements UDF1<String,String> {
    private static final long serialVersionUID = 9188970485013358015L;
    
    @Override
    public String registerName() {
        return "ds2ymd";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.StringType;
    }
    
    @Override
    public String call(String ds) throws Exception {
        if (StringUtils.isBlank(ds)) return StringUtils.EMPTY;
        return DateUtil.ymd().format(DateUtil.parseDS(ds));
    }
}
