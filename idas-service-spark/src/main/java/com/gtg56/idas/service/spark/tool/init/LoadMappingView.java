package com.gtg56.idas.service.spark.tool.init;

import com.gtg56.idas.common.entity.jdbc.ExternalTable;
import com.gtg56.idas.common.entity.jdbc.ExternalTableSparkViewMapping;
import com.gtg56.idas.common.service.IExternalTableService;
import com.gtg56.idas.common.service.IExternalTableSparkViewMappingService;
import com.gtg56.idas.service.spark.tool.DeltaTool;
import com.gtg56.idas.service.spark.tool.SparkInitWork;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class LoadMappingView implements SparkInitWork {
    
    @Resource(name = "extTableSparkViewMappingService")
    private IExternalTableSparkViewMappingService extTableSparkViewMappingService;
    
    @Resource(name = "extTableService")
    private IExternalTableService extTableService;
    
    
    @Override
    public void init(SparkSession spark) throws Exception {
        List<ExternalTableSparkViewMapping> viewMappings = extTableSparkViewMappingService.findAll();
        
        viewMappings.forEach(vm -> {
            Long id = vm.getId();
            String databaseName = vm.getDatabaseName();
            String viewName = vm.getViewName();
    
            ExternalTable extTable = extTableService.findById(id);
            if(extTable == null) {
                log.warn("");
                return;
            }
            
            if(Objects.equals(1,extTable.getActive())) return;
            
            if("delta".equals(extTable.getMode())) {
                DeltaTool deltaTool = new DeltaTool(spark, extTable.getStorePath());
                Dataset<Row> df = deltaTool.toDF();
                String vn = String.format("%s.%s", databaseName, viewName);
                log.info("mapping delta table {} into {}",extTable.getStorePath(),vn);
                df.createOrReplaceTempView(vn);
            }
        });
    }
}
