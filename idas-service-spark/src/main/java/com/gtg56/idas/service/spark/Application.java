package com.gtg56.idas.service.spark;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * Created by Link on 2019/12/31.
 */
@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.gtg56.idas.common",
                "com.gtg56.idas.service.spark.tool",
                "com.gtg56.idas.service.spark.node",
                "com.gtg56.idas.service.spark.dubbo"
        },
        exclude = {
                org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration.class
        })
@DubboComponentScan
@MapperScan("com.gtg56.idas.common.mapper")
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(value = {"classpath:/sparkServiceBeans.xml"})
public class Application {

    @SneakyThrows
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(Application.class,args);
    }
}
