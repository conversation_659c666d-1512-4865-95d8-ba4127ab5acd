package com.gtg56.idas.service.spark.udf.str;

import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

public class StringNotBlank extends AbstractSparkSqlUdf implements UDF1<String,Boolean> {
    private static final long serialVersionUID = -6568009887753780294L;
    
    @Override
    public Boolean call(String s) throws Exception {
        return StringUtils.isNotBlank(s);
    }
    
    
    @Override
    public String registerName() {
        return "str_not_blank";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.BooleanType;
    }
}
