package com.gtg56.idas.service.spark.streaming;

import com.alibaba.fastjson.JSON;
import com.gtg56.idas.common.dao.hbase.OverLimitHandlingDAO;
import com.gtg56.idas.common.entity.dto.sensor.SensorRecordDTO;
import com.gtg56.idas.common.entity.hbase.OverLimitHandling;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.kafka.OverLimitHandlingEvent;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.util.DateUtil;
import lombok.SneakyThrows;
import org.apache.spark.api.java.function.FilterFunction;
import org.apache.spark.api.java.function.FlatMapFunction;
import org.apache.spark.api.java.function.ForeachPartitionFunction;
import org.apache.spark.api.java.function.VoidFunction2;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.streaming.OutputMode;
import org.apache.spark.sql.streaming.Trigger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 超标处理记录处理
 * <p>
 * 针对中大百迅超标处理记录，存在超长时间范围而设置
 */
public class OverLimitProcessor extends StreamingEntrance {
    
    private static final long serialVersionUID = -180010148640208991L;
    
    @SneakyThrows
    @Override
    public void action() {
    
        String tsdbAddress = getEnv().getProperty("tsdb.address");
    
        Dataset<Row> dataset = structuredStreaming("overlimit_handling", "/user/hdfs/idas/OverLimitProcessorOffset.json", "10");
    
        Dataset<Row> casted = dataset.selectExpr("CAST(value as STRING) as event");

        Dataset<Row> filtered = casted.filter(new FilterFunction<Row>() {
            private static final long serialVersionUID = -4713191926798703089L;
        
            @Override
            public boolean call(Row row) throws Exception {
                String eventJson = row.getAs("event");
                OverLimitHandlingEvent event = JSON.parseObject(eventJson, OverLimitHandlingEvent.class);
                return event.getHandleTime() != null && event.getStartTime() != null;
            }
        });
        
        filtered.writeStream()
                .trigger(Trigger.ProcessingTime(0, TimeUnit.SECONDS))
                .outputMode(OutputMode.Append())
                .queryName(streamingName())
                .foreachBatch(new VoidFunction2<Dataset<Row>, Long>() {
                    private static final long serialVersionUID = 7105316768196853453L;
                    
                    @Override
                    public void call(Dataset<Row> ds, Long batchId) throws Exception {
                        Dataset<String> flat = ds.flatMap(new FlatMapFunction<Row, String>() {
                            private static final long serialVersionUID = 2781993960280469472L;
        
                            @Override
                            public Iterator<String> call(Row row) throws Exception {
                                Logger log = LoggerFactory.getLogger(OverLimitProcessor.class);
                                WarehouseSensorClient client = new WarehouseSensorClient(tsdbAddress);
    
                                String eventJSON = row.getAs("event");
                                OverLimitHandlingEvent event = JSON.parseObject(eventJSON, OverLimitHandlingEvent.class);
                                WarehouseSensor sensor = new WarehouseSensor();
                                BeanUtils.copyProperties(event, sensor);
    
                                Date startTime = event.getStartTime();
                                List<String> ret = new LinkedList<>();
                                List<SensorRecordDTO> overLimit = new LinkedList<>();
    
                                // 是时间范围模式
                                if (event.getRange() != null && event.getRange()) {
                                    Date endTime = event.getEndTime();
        
                                    // 如果时间范围超过1天，则拆成1天1批去查询OpenTSDB，防止出现TooManyAttempt
                                    if (TimeUnit.DAYS.toMillis(1) > endTime.getTime() - startTime.getTime()) {
                                        Date curr = startTime;
            
                                        while (curr.getTime() < endTime.getTime()) {
                                            Date currEnd = DateUtil.dayAdd(curr, 1).getTime();
                                            if (currEnd.after(endTime)) {
                                                currEnd = endTime;
                                            }
                                            overLimit.addAll(client.listOverLimit(sensor, curr, currEnd));
                                            curr = currEnd;
                                        }
                                    } else {
                                        overLimit.addAll(
                                                client.listOverLimit(sensor, event.getStartTime(), event.getEndTime())
                                        );
                                    }
                                } else {
                                    // 前后半分钟以覆盖点
                                    Date start = new Date(startTime.getTime() - 30000);
                                    Date end = new Date(startTime.getTime() + 30000);
                                    overLimit.addAll(client.listOverLimit(sensor, start, end));
                                }
    
                                // 绝大多数情况，不是所有报告的传感器都有超标记录，做个判空减少无谓消耗
                                if (!overLimit.isEmpty()) {
                                    log.info("number over {}", overLimit.size());
                                    overLimit.forEach(ol -> {
                                        OverLimitHandling oh = new OverLimitHandling();
                                        BeanUtils.copyProperties(event, oh);
                                        BeanUtils.copyProperties(ol, oh);
                                        ret.add(JSON.toJSONString(oh));
                                    });
                                }
                                return ret.iterator();
                            }
                        }, Encoders.STRING()).persist();
    
                        if (!flat.isEmpty()) {
                            flat.repartition(50)
                                    .foreachPartition(new ForeachPartitionFunction<String>() {
                                        private static final long serialVersionUID = 4257460306456611245L;
                    
                                        @Override
                                        public void call(Iterator<String> it) throws Exception {
                                            if (!it.hasNext()) return;
                                            OverLimitHandlingDAO dao = OverLimitHandlingDAO.getInstance();
                                            while (it.hasNext()) {
                                                String json = it.next();
                                                OverLimitHandling oh = JSON.parseObject(json, OverLimitHandling.class);
                                                dao.put(oh);
                                            }
                                        }
                                    });
                        }
                        flat.unpersist();
                    }
                })
                .start();
        
        awaitStructuredStreaming();
    }
}
