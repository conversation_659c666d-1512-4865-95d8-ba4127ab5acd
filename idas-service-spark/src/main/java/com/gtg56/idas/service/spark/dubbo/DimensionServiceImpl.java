package com.gtg56.idas.service.spark.dubbo;

import com.gtg56.idas.common.entity.hive.DateDimension;
import com.gtg56.idas.common.entity.jdbc.Festival;
import com.gtg56.idas.common.service.IFestivalService;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.common.util.SparkUtil;
import com.gtg56.idas.service.define.IDimensionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class DimensionServiceImpl implements IDimensionService {
    
    @Resource
    private IFestivalService festivalService;
    @Resource
    private SparkSession spark;
    @Resource
    private JavaSparkContext jSparkContext;
    
    @Override
    public void generateDateDimension(int year, String tableName) {
        LocalDate startLD = LocalDate.of(year,1,1);
        boolean leapYear = startLD.isLeapYear();
        int numDays = leapYear ? 366 : 365;
    
        List<Festival> festivals = festivalService.findByYear(year);
    
        List<DateDimension> dateDimensions = IntStream.range(0, numDays)
                .mapToObj(startLD::plusDays)
                .map(DateUtil::localDateToDate)
                .map(date -> {
                    String ymd = DateUtil.ymd().format(date);
                    return DateUtil.buildDate(ymd, festivals);
                })
                .collect(Collectors.toList());
    
        JavaRDD<DateDimension> rdd = jSparkContext.parallelize(dateDimensions);
        Dataset<Row> dataFrame =
                SparkUtil.camelColumnToUnderscore(spark.createDataFrame(rdd, DateDimension.class));
        if(spark.catalog().tableExists(tableName)) {
            String view = "tmp_date_dim" + System.currentTimeMillis();
            dataFrame.createOrReplaceTempView(view);
            
            List<String> cols = new ArrayList<>(Arrays.asList(dataFrame.columns()));
            cols.remove("year");
            String colStr = StringUtils.join(cols,",");
            String sql = "INSERT OVERWRITE TABLE " + tableName + " PARTITION(year="+year+") SELECT " + colStr + " FROM " + view;
            
            spark.sql(sql);
            
            spark.catalog().dropTempView(view);
        } else {
            dataFrame.write().partitionBy("year").mode(SaveMode.Overwrite).saveAsTable(tableName);
        }
    }
}
