package com.gtg56.idas.service.spark.tool;

import com.gtg56.idas.service.spark.streaming.StreamingEntrance;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Properties;

/**
 * Created by Link on 2020/1/9.
 */
public class SparkFactory {
    
    @Setter @Value("${spark.master}")
    private String master;

    @Setter @Value("${spark.appName}")
    private String appName;
    
    @Resource(name = "sparkProp")
    private Properties sparkProp;

    public SparkSession build() {
        SparkSession.Builder builder = SparkSession.builder();
        
        if (isLocal()) {
            builder.master("local[*]");
        } else if (StringUtils.isNotBlank(master))
            builder.master(master);
        
        if(StreamingEntrance.currentEntrance != null) {
            builder.appName(StreamingEntrance.currentEntrance.streamingName());
        } else if (StringUtils.isNotBlank(appName)) {
            builder.appName(appName);
        }
        
        loadConf(builder);
        
        return builder.getOrCreate();
    }
    
    public JavaSparkContext getSparkContext(SparkSession spark) {
        return new JavaSparkContext(spark.sparkContext());
    }
    
    static {
        if(isLocal()) {
            loadWindowsUtilLib();
        }
    }
    
    private static boolean isLocal() {
        return System.getProperty("os.name","").startsWith("Windows");
    }
    
    private void loadConf(SparkSession.Builder builder) {
        if(sparkProp != null && !sparkProp.isEmpty()) {
            for (Map.Entry<Object, Object> prop : sparkProp.entrySet()) {
                builder.config(prop.getKey().toString(),prop.getValue().toString());
            }
        }
    }
    
    private static void loadWindowsUtilLib() {
        try {
            // 设置 HADOOP_HOME 目录
            System.setProperty("hadoop.home.dir", "D:\\dev\\winutils\\hadoop-3.0.0\\");
            // 加载库文件
            System.load("D:\\dev\\winutils\\hadoop-3.0.0\\bin\\hadoop.dll");
        } catch (Throwable t) {
            System.out.println("Load Hadoop winutil lib fail!!!");
            t.printStackTrace();
            System.exit(1);
        }
    }
}
