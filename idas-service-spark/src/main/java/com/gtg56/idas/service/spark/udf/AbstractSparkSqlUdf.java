package com.gtg56.idas.service.spark.udf;

import org.apache.spark.sql.types.DataType;

public abstract class AbstractSparkSqlUdf {
    
    public AbstractSparkSqlUdf() {}
    
    /**
     * 设置注册到Spark的udf函数名
     * @return 函数名
     */
    public abstract String registerName();
    
    public abstract DataType returnType();
    
    /**
     * 是否自动注册
     * @return 是否自动注册
     */
    public boolean autoRegister() { return true; }
    
}
