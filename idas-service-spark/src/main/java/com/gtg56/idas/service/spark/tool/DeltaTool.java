package com.gtg56.idas.service.spark.tool;

import io.delta.tables.DeltaTable;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.*;
import org.apache.spark.sql.delta.DeltaLog;
import org.json4s.Diff;

import javax.sql.DataSource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Link on 2020/1/2.
 */
@Slf4j
public class DeltaTool {
    private SparkSession spark;
    private String savePath;

    public static final String ID_COLUMN_NAME = "id";
    public static final List<String> ID_CONDITION = Collections.singletonList(ID_COLUMN_NAME);
    private static final String NEVER_MATCH_CONDITION = "1!=1";
    private static final String ALWAYS_MATCH_CONDITION = "1=1";
    
    public DeltaTool(SparkSession spark, String savePath) {
        if(!deltaTableExists(spark,savePath)) {
            throw new IllegalArgumentException("path " + savePath + " not exists or not delta table");
        }
        this.spark = spark;
        this.savePath = savePath;
    }

    public DeltaTable getTable() {
        return DeltaTable.forPath(spark, savePath);
    }
    
    private DeltaLog getLog() {
        return DeltaLog.forTable(spark,savePath);
    }
    
    /**
     * 创建delta表
     * @param df 数据集
     * @param savePath 保存目录
     * @param partitionBy 分区字段
     * @return deltaTool
     */
    public static DeltaTool create(Dataset<Row> df,String savePath,String... partitionBy) {
        return create(df,savePath,DiffSchemaPolicy.IGNORE_DIFF,partitionBy);
    }
    
    public static DeltaTool create(Dataset<Row> df,String savePath,DiffSchemaPolicy policy,String... partitionBy) {
        DataFrameWriter<Row> writer = df.write().format("delta").mode(SaveMode.Overwrite);
        SparkSession spark = df.sparkSession();
    
        if(partitionBy != null && partitionBy.length > 0) {
            writer.partitionBy(partitionBy);
        }
        writer.save(savePath);
        return new DeltaTool(spark, savePath);
    }
    
    public static boolean deltaTableExists(SparkSession spark,String path) {
        return DeltaTable.isDeltaTable(spark,path);
    }

    /**
     * Update or Insert（upsert），根据id判断update or insert
     * @param newData 增量数据
     */
    public void upsert(Dataset<Row> newData) {
        upsert(newData, ID_CONDITION);
    }


    /**
     * Update or Insert（upsert）
     * @param newData 增量数据
     * @param updateConditionColumns 当此集合内条件符合，则update，否则insert（old.xxx = new.xxx AND old.yyy = new.yyy）
     */
    public void upsert(Dataset<Row> newData, List<String> updateConditionColumns) {
        String random = RandomStringUtils.random(10, true, true);
        String oldAlias = "old_" + random;
        String newAlias = "new_" + random;
    
        String condition = NEVER_MATCH_CONDITION;
    
        Set<String> newColumns = new LinkedHashSet<>(Arrays.asList(newData.schema().fieldNames()));
    
        Map<String,Column> updateColumns = new HashMap<>();
        newColumns.stream()
                .filter(newColumn ->
                        CollectionUtils.isNotEmpty(updateConditionColumns) &&
                                !updateConditionColumns.contains(newColumn))
                .forEach(newColumn ->
                        updateColumns.put(newColumn,newData.col(newColumn)));
    
//        Map<String,String> insertColumns = new HashMap<>();
//        newColumns.forEach(newColumn -> insertColumns.put(newColumn,newAlias + "." + newColumn));
    
        if(CollectionUtils.isNotEmpty(updateConditionColumns)) {
            List<String> conditions = updateConditionColumns.stream().map(col ->
                    oldAlias + "." + col + " = " + newAlias + "." + col
            ).collect(Collectors.toList());
            condition = StringUtils.join(conditions," and ");
        }
    
        log.info("upsert onto delta table `{}` condition is {}", savePath, condition);
        
        getTable().as(oldAlias)
                .merge(newData.as(newAlias),condition)
                .whenMatched()
                .update(updateColumns)
                .whenNotMatched()
                .insertAll()
                .execute();
    
        spark.catalog().dropTempView(oldAlias);
        spark.catalog().dropTempView(newAlias);
    }

    public void delete(String condition) {
        getTable().delete(condition);
    }

    public void vacuum() {
        getTable().vacuum();
    }

    public Dataset<Row> dropOldData(@NotNull Dataset<Row> data) {
        return dropOldData(data,ID_COLUMN_NAME);
    }

    public Dataset<Row> dropOldData(@NotNull Dataset<Row> data, @NotNull String identityColName) {
        return dropOldData(data,identityColName,null);
    }

    public Dataset<Row> dropOldData(@NotNull Dataset<Row> data, @NotNull String identityColName, String updateTimeColName) {
        List<String> fieldNames = new LinkedList<>(Arrays.asList(data.schema().fieldNames()));

        Dataset<Row> cachedData = data.cache();
        String tempViewName = "tmp_" + RandomStringUtils.random(10,true,true);
        
        String sql;
        if(StringUtils.isNotBlank(updateTimeColName)) {
            String fieldNameStr = StringUtils.join(fieldNames,",");
            sql = "WITH (SELECT rank() OVER (PARTITION BY " + identityColName + " ORDER BY " + updateTimeColName +" DESC ) AS rn , * " +
                    "FROM " + tempViewName + ") t \n" +
                    "SELECT " + fieldNameStr + " FROM t WHERE rn = 1";
        } else {
            fieldNames.remove(identityColName);
            List<String> lastSegments = fieldNames.stream()
                    .map(name -> "\tlast(" + name + ") as " + name + " ")
                    .collect(Collectors.toList());
            String lastSegment = StringUtils.join(lastSegments,",\n");
            sql = "SELECT \n\t" + identityColName + ",\n" + lastSegment +
                    " \nFROM " + tempViewName + " \n" +
                    "GROUP BY " + identityColName + " ORDER BY " + identityColName;
        }
        
        log.info("dropOldData sql : {}",sql);
        
        cachedData.createOrReplaceTempView(tempViewName);
        long rowsBefore = cachedData.count();
        Dataset<Row> ret = spark.sql(sql);

        long rowsAfter = ret.count();
        spark.catalog().dropTempView(tempViewName);

        log.info("dropOldData done , #row before drop : {} ; #row after : {} ; drop {} rows ",rowsBefore,rowsAfter,rowsBefore - rowsAfter);

        return ret;
    }

    public void dropOldAndUpsert(Dataset<Row> newData) {
        upsert(dropOldData(newData));
    }

    public Dataset<Row> toDF() {
        return getTable().toDF();
    }

    public enum DiffSchemaPolicy {
        IGNORE_DIFF("ignore"),
        THROW_EXCEPTION("exception"),
        MERGE_SCHEMA("merge"),
        OVERWRITE_SCHEMA("overwrite");

        @Getter
        private String key;
        DiffSchemaPolicy(String key) {
            this.key = key;
        }

        public static DiffSchemaPolicy byKey(String key) {
            for(DiffSchemaPolicy policy : values()) {
                if(policy.key.equalsIgnoreCase(key))
                    return policy;
            }
            return IGNORE_DIFF;
        }
    }
}
