package com.gtg56.idas.service.spark.node;

import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.service.define.IDimensionService;

import java.util.Arrays;
import java.util.List;

public class GenDateDim extends WorkloadNode {
    private static final long serialVersionUID = 6732552247427375699L;
    
    
    IDimensionService dimensionService;
    
    public GenDateDim(IDimensionService dimensionService) {
        this.dimensionService = dimensionService;
    }
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Arrays.asList(
                NodeArg.of("year",true,null),
                NodeArg.of("table",false,"dim.date_dimension_year")
        );
    }
    
    @Override
    public String describe() {
        return "generate date dimension partition by year";
    }
    
    @Override
    public boolean requireSpark() {
        return true;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        Integer year = args.getAsInteger("year");
        String table = args.get("table");
        
        dimensionService.generateDateDimension(year,table);
        
        return noDataReturn();
    }
}
