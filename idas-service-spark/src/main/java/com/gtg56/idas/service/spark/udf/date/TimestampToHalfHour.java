package com.gtg56.idas.service.spark.udf.date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

import java.util.Calendar;
import java.util.Date;

public class TimestampToHalfHour extends AbstractSparkSqlUdf implements UDF1<Long,String> {
    private static final long serialVersionUID = 2707177013793479935L;
    
    @Override
    public String registerName() {
        return "ts_to_half_hour";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.StringType;
    }
    
    @Override
    public String call(Long ts) throws Exception {
        if (ts == null) return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(ts));
    
        int min = cal.get(Calendar.MINUTE);
        if (min < 30) {
            cal.set(Calendar.MINUTE,0);
        } else {
            cal.set(Calendar.MINUTE,30);
        }
        cal.set(Calendar.MILLISECOND,0);
        cal.set(Calendar.SECOND,0);
        
        return DateUtil.ymdhms().format(cal.getTime());
    }
}
