package com.gtg56.idas.service.spark.streaming;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.entity.jdbc.FDAReport;
import com.gtg56.idas.common.service.IFDAReportService;
import com.gtg56.idas.common.tool.compress.CompressorFactory;
import com.gtg56.idas.common.tool.http.ZJFDAReportClientNew;
import com.gtg56.idas.common.util.ListUtil;
import com.gtg56.idas.common.util.RedisUtil;
import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.spark.api.java.function.VoidFunction2;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.streaming.OutputMode;
import org.apache.spark.sql.streaming.Trigger;

import java.io.IOException;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.spark.sql.functions.*;

@Slf4j
public class ZJFDAReport extends StreamingEntrance {
    
    private static final long serialVersionUID = 4787220146096492367L;
    
    @SneakyThrows
    public ZJFDAReport() {
        super();
        prop = new Properties();
        prop.load(StreamUtil.resourceInputStream("config/zjFDAReport.properties"));
        reportMinute = Integer.parseInt(prop.getProperty("report.minute"));
    }
    
    private Properties prop;
    
    private int reportMinute;
    
    private RedisUtil redisUtil;
    private IFDAReportService fdaReportService;
    
    @SneakyThrows
    @Override
    public void action() {
        redisUtil = SpringUtil.getBean("redisUtils", RedisUtil.class);
        fdaReportService = SpringUtil.getBean(IFDAReportService.class);
    
        String topic = prop.getProperty("kafka.topic");
        String offsetPath = prop.getProperty("kafka.offset.path");
    
        List<String> reportWarehouseCodes = Arrays.asList(prop.getProperty("report.warehouseCodes").split(","));
    
        String where = reportWarehouseCodes.size() > 1 ?
                "k in ('" + StringUtils.join(reportWarehouseCodes, "','") + "')" :
                "k = '" + reportWarehouseCodes.get(0) + "'";
    
        log.info("using key filter : {}", where);
    
        Dataset<Row> casted = structuredStreaming(topic, offsetPath, "")
                .selectExpr("CAST(key AS STRING) as k", "CAST(value AS STRING) as vs");
                
        Dataset<Row> processed = casted
                .where(where)
                .select(
                        col("k").alias("warehouse_code"),
                        get_json_object(col("vs"), "$.value").alias("v"),
                        get_json_object(col("vs"), "$.timestamp").alias("ts_sec"),
                        get_json_object(col("vs"), "$.tags").alias("tags")
                ).select(
                        col("warehouse_code"),
                        col("v"),
                        col("ts_sec"),
                        get_json_object(col("tags"), "$.type").alias("type"),
                        get_json_object(col("tags"), "$.sensorCode").alias("sensor_code")
                ).selectExpr(
                        "warehouse_code","sensor_code",
                        "to_2scale_decimal(v) as record_value",
                        "ts_to_half_hour(CAST(ts_sec AS long) * 1000) as half_hour",
                        "CAST((CAST(ts_sec AS long) * 1000) as timestamp) as ts",
                        "type"
                );
                
        Dataset<Row> avg = processed.groupBy(
                window(processed.col("ts"),"30 minutes"),
                processed.col("sensor_code"),
                processed.col("half_hour"),
                processed.col("type")
        )
                .agg(
                avg(processed.col("record_value")).alias("v"), max(processed.col("ts"))
        );
    
        avg.printSchema();
        
        int batchWindow = 2;
        int reportPerBatch = reportMinute / batchWindow;
        
        avg.writeStream()
                .trigger(Trigger.ProcessingTime(batchWindow, TimeUnit.MINUTES))
                .queryName("fileGen")
                .outputMode(OutputMode.Update())
                .foreachBatch(new VoidFunction2<Dataset<Row>, Long>() {
                    private static final long serialVersionUID = 4993222067226669254L;
    
                    @Override
                    public void call(Dataset<Row> ds, Long batchId) throws Exception {
                        Dataset<Row> agg = ds.groupBy("sensor_code", "half_hour")
                                .pivot("type", Arrays.asList(SensorConsts.TYPE_TEMPERATURE, SensorConsts.TYPE_HUMIDITY))
                                .agg(last("v"));
                        List<String> jsons = agg.toJSON().collectAsList();
                        log.info("calculate {} rows of data",jsons.size());
                        if (jsons.size() > 0) {
                            long time = System.currentTimeMillis();
                            Path path = new Path(cachePath + time + ".json");
                            FSDataOutputStream fos = getFs().createFile(path)
                                    .recursive()
                                    .overwrite(true)
                                    .build();
                            jsons.forEach(json -> {
                                String content = json + "\n";
                                try {
                                    StreamUtil.writeStream(content.getBytes(), fos, false);
                                } catch (IOException e) {
                                    log.warn("write json to path " + path.toString() + " fail", e);
                                }
                            });
                            StreamUtil.safeClose(fos);
                            log.info("write json to path {} ", path.toString());
                        }
                        if(batchId % reportPerBatch == 1) {
                            mergeReport();
                        }
                    }
                })
                .start();
        
        awaitStructuredStreaming();
    }
    
    private static final String cachePath = "/user/hdfs/idas/zjFDAReportCache/";
    
    
    private void mergeReport() {
        List<String> files = new LinkedList<>();
        try {
            RemoteIterator<LocatedFileStatus> listFiles = getFs().listFiles(new Path(cachePath), false);
    
            while (listFiles.hasNext()) {
                files.add(listFiles.next().getPath().toString());
            }
        } catch (IOException e) {
            log.warn("ls for " + cachePath + " jsons fail",e);
        }
        if(files.size() > 0) {
            Dataset<Row> ds = getSpark().read().json(files.toArray(new String[0]));
    
            List<String> jsons = ds.groupBy("sensor_code", "half_hour")
                    .agg(last(ds.col("TEMPERATURE")).as("TEMPERATURE"), last(ds.col("HUMIDITY")).as("HUMIDITY"))
                    .na().drop()
                    .toJSON().collectAsList();
            if(jsons.size() > 0) {
                report(jsons);
            }
            
            files.forEach(file -> {
                try {
                    getFs().delete(new Path(file), false);
                } catch (IOException e) {
                    log.warn("delete hdfs file " + file + "fail",e);
                }
            });
        }
    }

    @SneakyThrows
    private void report(List<String> jsons) {
        String cropCode = prop.getProperty("report.cropCode");

        List<ZJFDAReportClientNew.TemperatureHumidityRecord> records = jsons.stream().map(json -> {
            JSONObject info = JSON.parseObject(json);

            String time = info.getString("half_hour");
            String id = cropCode + "-" + info.getString("sensor_code");
            String redisKey = "ZJFDAReport-" + time;

            if (!redisUtil.hHasKey(redisKey, id)) {
                String temperature = info.getBigDecimal("TEMPERATURE").setScale(2, RoundingMode.CEILING).toString();
                String humidity = info.getBigDecimal("HUMIDITY").setScale(2, RoundingMode.CEILING).toString();


                redisUtil.hset(redisKey, id, 1, 30 * 24 * 60 * 60);

                return new ZJFDAReportClientNew.TemperatureHumidityRecord().setId(id).setTemperature(temperature).setHumidity(humidity).setTime(time);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (records.size() > 100) {
            List<List<ZJFDAReportClientNew.TemperatureHumidityRecord>> pages = ListUtil.getPages(records, 100);
            pages.forEach(this::sendReport);
        } else {
            sendReport(records);
        }
    }

    private void sendReport(List<ZJFDAReportClientNew.TemperatureHumidityRecord> records) {
        String cropCode = prop.getProperty("report.cropCode");
        String password = prop.getProperty("report.password");
        String code = prop.getProperty("report.code");
        String compress = prop.getProperty("report.compress");

        ZJFDAReportClientNew client = new ZJFDAReportClientNew();
        client.setCropCode(cropCode);
        client.setPassword(password);
        client.setCode(code);

        Date now = new Date();

        JSONObject extInfo = new JSONObject();
        extInfo.put("fileTypeNumber", "");
        extInfo.put("corpCode", client.getCropCode());

        FDAReport fdaReport = new FDAReport()
                .setFdaCode(SensorConsts.ZJFDA_CODE)
                .setReportTime(now)
                .setReportCount(records.size())
                .setReportExtInfo(extInfo.toJSONString())
                .setReportContentCompress(compress);

        fdaReport
                .setCreateTime(now)
                .setModifyTime(now);

        try {
            ZJFDAReportClientNew.Result result = client.uploadTemperatureHumidityRecord(records);
            Boolean success = result.getSuccess();
            fdaReport.setReportContent(CompressorFactory.forName(compress).compress(result.getRequestBody().getBytes()))
                    .setStatusFlag(success ? CommonConsts.INT_FLAG_SUCCESS : CommonConsts.INT_FLAG_FAIL)
                    .setStatusMsg(success ? CommonConsts.CHN_SUCCESS : result.getCode() + "-" + result.getMessage());

            if (success) {
                log.info("浙江药监上报成功");
            } else {
                log.warn("浙江药监上报失败 {}", result.getCode() + "-" + result.getMessage());
            }
        } catch (Exception e) {
            log.warn("浙江药监上报异常 ", e);
            String stackTrace = ExceptionUtils.getStackTrace(e);
            fdaReport.setReportContent(CompressorFactory.forName(compress).compress(stackTrace.getBytes()));
            fdaReport.setStatusFlag(CommonConsts.INT_FLAG_FAIL);
            fdaReport.setStatusMsg(e.getMessage());
            fdaReport.baseSet();
        } finally {
            fdaReportService.save(fdaReport);
        }
    }
}
