package com.gtg56.idas.service.spark.tool;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.gtg56.idas.common.util.PackageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.serializer.KryoRegistrator;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class EntityKryoRegister implements KryoRegistrator {
    @Override
    public void registerClasses(Kryo kryo) {
        listClass("com.gtg56.idas.common.entity").forEach(clz -> {
            kryo.register(clz, new FieldSerializer<>(kryo, clz));
        });
    }
    
    private List<Class<?>> listClass(String basePackage) {
        List<String> clz = PackageUtil.getClassName(basePackage, true);
        
        return clz.stream().map(c -> {
            try {
                return Class.forName(c);
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            return null;
        }).filter(Objects::nonNull)
                .filter(Serializable.class::isAssignableFrom)
                .collect(Collectors.toList());
    }
}
