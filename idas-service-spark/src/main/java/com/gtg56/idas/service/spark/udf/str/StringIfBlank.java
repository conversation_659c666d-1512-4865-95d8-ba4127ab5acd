package com.gtg56.idas.service.spark.udf.str;

import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

public class StringIfBlank extends AbstractSparkSqlUdf implements UDF2<String,String,String> {
    private static final long serialVersionUID = -3116024775195236887L;
    
    @Override
    public String registerName() {
        return "str_if_blank";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.StringType;
    }
    
    @Override
    public String call(String test, String def) throws Exception {
        if(StringUtils.isBlank(def)) throw new NullPointerException("def is null");
        return StringUtils.isBlank(test) ? def : test;
    }
}
