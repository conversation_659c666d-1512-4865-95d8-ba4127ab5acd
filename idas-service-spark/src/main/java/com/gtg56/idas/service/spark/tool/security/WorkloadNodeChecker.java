package com.gtg56.idas.service.spark.tool.security;

import com.google.common.collect.ImmutableList;
import com.gtg56.idas.common.entity.dto.NodeProvideDTO;
import com.gtg56.idas.common.node.WorkloadNodeException;
import javassist.bytecode.ClassFile;
import javassist.bytecode.ConstPool;
import lombok.SneakyThrows;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class WorkloadNodeChecker {
    private final static List<Pattern> blacklistPatterns = ImmutableList.copyOf(Arrays.asList(
            Pattern.compile("java\\.lang\\.reflect\\..+"),
            Pattern.compile(".+reflect.+"),
            Pattern.compile("java\\.io\\.File.+"),
            Pattern.compile("java\\.lang\\.Thread"),
            Pattern.compile(".*javassist\\..+"),
            Pattern.compile(".+cglib\\..+")
    ));
    
    private static List<String> fitPattern(String str) {
        return blacklistPatterns.stream().filter(pattern -> {
            Matcher matcher = pattern.matcher(str);
            return matcher.groupCount() > 0;
        }).map(Pattern::pattern).collect(Collectors.toList());
    }
    
    @SneakyThrows
    public static void check(NodeProvideDTO dto) {
        ClassFile cf = new ClassFile(new DataInputStream(new ByteArrayInputStream(dto.getNodeByteCode())));
        ConstPool constPool = cf.getConstPool();
        HashSet<String> toFilter = new HashSet<>();
        for ( int i = 1 ; i<constPool.getSize() ; i++ ) {
            int descriptorIndex;
            if(ConstPool.CONST_Class == constPool.getTag(i)) {
                toFilter.add(constPool.getClassInfo(i));
            } else if (ConstPool.CONST_NameAndType == constPool.getTag(i)) {
                descriptorIndex = constPool.getNameAndTypeDescriptor(i);
                String desc = constPool.getUtf8Info(descriptorIndex);
                for (int p = 0; p < desc.length(); p++) {
                    if (desc.charAt(p) == 'L') {
                        toFilter.add(desc.substring(++p, p = desc.indexOf(';', p)).replace('/', '.'));
                    }
                }
            }
        }
        Set<String> hitBlackPatterns = new HashSet<>();
        toFilter.forEach(classInfo -> hitBlackPatterns.addAll(fitPattern(classInfo)));
        
        if(!hitBlackPatterns.isEmpty()) {
            Exception iae = new IllegalAccessException("Workload Node " +
                    dto.getNodeClassName() +
                    " access blacklist classes");
            
            throw new WorkloadNodeException(null,"Illegal WorkloadNode",iae);
        }
    }
}
