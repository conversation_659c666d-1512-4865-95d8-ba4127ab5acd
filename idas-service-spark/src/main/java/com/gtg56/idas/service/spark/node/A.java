package com.gtg56.idas.service.spark.node;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.Metadata;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;

import java.util.Arrays;

//import org.graphframes.GraphFrame;

public class A {
    public void a(SparkSession spark) {
        // spark.sparkContext().setCheckpointDir("d:\\dev\\checkpoint");
    
        // Dataset<Row> productInfo = productInfo(spark);
        // orderInfo(spark).createOrReplaceTempView("order");
    
        // Dataset<Row> edgeInfo = spark.sql(
        //         "SELECT l.product_id as src , r.product_id as dst , " +
        //                 "count(distinct  l.order_id) as relationship " +
        //                 "FROM order l " +
        //                 "JOIN order r " +
        //                 "ON l.order_id = r.order_id AND l.product_id != r.product_id " +
        //                 "GROUP BY l.product_id , r.product_id");
    
        // edgeInfo.show();
        // GraphFrame graph = GraphFrame.apply(productInfo, edgeInfo);
    
        // System.out.println("GraphX degrees");
        // graph.degrees().show();
    
        // System.out.println("GraphX PageRank Score");
        // GraphFrame result = graph.pageRank()
        //         .resetProbability(0.15) // 随机重置的概率
        //         .maxIter(25)            // 迭代次数
        //         .run();
        // result.edges().show();
        // result.vertices().orderBy("pagerank").show();
    
    
        // Dataset<Row> items = spark.sql("SELECT order_id , collect_set(product_id) as items from order group by order_id");
        // items.show();
    
        // FPGrowth fpg = new FPGrowth()
        //         .setMinSupport(0.2)     // 支持度 P(A ∩ B)
        //         .setMinConfidence(0.8)  // 置信度 P(B|A)
        //         .setNumPartitions(10);
        // FPGrowthModel model = fpg.fit(items);
    
        // System.out.println("FPG freqItems");
        // model.freqItemsets().show();
    
        // System.out.println("FPG associationRules");
        // model.associationRules().show();
    
    }
    
    private static Dataset<Row> productInfo(SparkSession spark) {
        StructField[] structField = {
                new StructField("id", DataTypes.LongType,true, Metadata.empty()),
                new StructField("name", DataTypes.StringType,true, Metadata.empty())
        };
        StructType schema = new StructType(structField);
        return spark.createDataFrame(Arrays.asList(
                RowFactory.create(1L,"999感冒灵"),
                RowFactory.create(2L,"急支糖浆"),
                RowFactory.create(3L,"喉疾灵"),
                RowFactory.create(4L,"泰诺"),
                RowFactory.create(5L,"美林")
        ),schema).cache();
    }
    
    private static Dataset<Row> orderInfo(SparkSession spark) {
        StructField[] structField = {
                new StructField("order_id", DataTypes.LongType,true,Metadata.empty()),
                new StructField("product_id", DataTypes.LongType,true,Metadata.empty())
        };
        StructType schema = new StructType(structField);
        return spark.createDataFrame(Arrays.asList(
                RowFactory.create(1L,1L),
                RowFactory.create(1L,2L),
                RowFactory.create(1L,3L),
                
                RowFactory.create(2L,2L),
                RowFactory.create(2L,3L),
                
                RowFactory.create(3L,1L),
                
                RowFactory.create(4L,3L),
                RowFactory.create(4L,4L),
                
                
                RowFactory.create(5L,4L),
                RowFactory.create(5L,5L)
        ),schema).cache();
    }
    
    //        int minProductId = 1;
    //        int maxProductId = 1000;
    //
    //
    //        JavaSparkContext sc = new JavaSparkContext(spark.sparkContext());
    //        JavaRDD<List<String>> orderInfo = sc.parallelize(Arrays.asList(
    //                Arrays.asList("999感冒灵", "急支糖浆", "喉疾灵"),
    //                Arrays.asList("急支糖浆", "泰诺"),
    //                Arrays.asList("999感冒灵"),
    //                Arrays.asList("喉疾灵", "泰诺"),
    //                Arrays.asList("喉疾灵", "美林")
    //
    //        )).cache();
    //
    //        orderInfo.count();
    //        FPGrowth fpg = new FPGrowth()
    //                .setMinSupport(0.1);
    //
    //        FPGrowthModel<String> model = fpg.run(orderInfo);
    //
    //        for (FPGrowth.FreqItemset<String> itemset: model.freqItemsets().toJavaRDD().collect()) {
    //            log.info("[" + itemset.javaItems() + "], " + itemset.freq());
    //        }
    //
    //        double minConfidence = 0.7;
    //        for (AssociationRules.Rule<String> rule
    //                : model.generateAssociationRules(minConfidence).toJavaRDD().collect()) {
    //            log.info(
    //                    rule.javaAntecedent() + " => " + rule.javaConsequent() + ", " + rule.confidence());
    //        }
    
}
