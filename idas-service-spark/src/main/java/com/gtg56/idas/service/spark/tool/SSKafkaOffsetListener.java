package com.gtg56.idas.service.spark.tool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtg56.idas.common.util.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.spark.sql.streaming.SourceProgress;
import org.apache.spark.sql.streaming.StreamingQueryListener;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 结构流Kafka偏移量监听器
 */
@Slf4j
@AllArgsConstructor
public class SSKafkaOffsetListener extends StreamingQueryListener {
    
    private final String streamingName;
    private final FileSystem fs;
    private final Path offsetPath;
    
    @Override
    public void onQueryStarted(QueryStartedEvent event) {
        log.info("query {} start as id {}", streamingName, event.id());
    }
    
    /**
     * 当Trigger成功执行后，spark将调用此方法将偏移量传入
     * 需要写入offsetPath以供重启后重新从此位置读取
     *
     * @param event 处理事件
     */
    @Override
    public void onQueryProgress(QueryProgressEvent event) {
        Map<String, Map<String, Long>> topicPartitionOffsets = new HashMap<>();
        
        SourceProgress[] sources = event.progress().sources();
        for (SourceProgress source : sources) {
            String offsetJSON = source.endOffset();
            JSONObject topics = JSONObject.parseObject(offsetJSON);
            
            topics.keySet().forEach(topic -> {
                JSONObject partitions = topics.getJSONObject(topic);
                Map<String, Long> partitionOffsets = topicPartitionOffsets.getOrDefault(topic, new HashMap<>());
                partitions.keySet().forEach(partition -> {
                    long offset = ((Number) partitions.get(partition)).longValue();
                    partitionOffsets.put(partition, Math.max(offset, partitionOffsets.getOrDefault(partition, 0L)));
                });
                topicPartitionOffsets.put(topic, partitionOffsets);
            });
        }
        String offset = JSON.toJSONString(topicPartitionOffsets);
        log.debug("query {} batchId {} offset {}", streamingName, event.progress().batchId(), offset);
        
        try {
            FSDataOutputStream fos = fs.createFile(offsetPath).recursive().overwrite(true).build();
            StreamUtil.writeStream(offset.getBytes(), fos);
        } catch (IOException e) {
            log.warn("write offset to " + offsetPath.toString() + " fail", e);
        }
    }
    
    @Override
    public void onQueryTerminated(QueryTerminatedEvent event) {
        log.info("query {} id {} terminated", streamingName, event.id());
    }
}
