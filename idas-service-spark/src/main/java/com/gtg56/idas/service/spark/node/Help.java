package com.gtg56.idas.service.spark.node;

import com.alibaba.fastjson.JSONArray;
import com.gtg56.idas.common.entity.dto.NodeArgsDTO;
import com.gtg56.idas.common.node.NodeArg;
import com.gtg56.idas.common.node.WorkloadNode;
import com.gtg56.idas.service.define.IWorkloadProviderService;

import java.util.Collections;
import java.util.List;

public class Help extends WorkloadNode {
    private static final long serialVersionUID = -5102017142626167677L;
    
    private final IWorkloadProviderService providerService;
    
    public Help(IWorkloadProviderService providerService) {
        this.providerService = providerService;
    }
    
    @Override
    public List<NodeArg> acceptArgs() {
        return Collections.singletonList(NodeArg.of("nodeName",false,"all"));
    }
    
    @Override
    public String describe() {
        return "show workload node usage";
    }
    
    @Override
    public boolean requireSpark() {
        return false;
    }
    
    @Override
    public String execution(NodeArgsDTO args) {
        String nodeName = args.get("nodeName");
        JSONArray data = new JSONArray();
    
        List<String> nodeNames = providerService.listProviding();
        if("all".equals(nodeName)) {
            for(String nn : nodeNames) {
                WorkloadNode node = providerService.provideNode(nn).build();
                data.add(node.usage());
            }
        } else {
            WorkloadNode node = providerService.provideNode(nodeName).build();
            data.add(node.usage());
        }
    
        return dataReturn(data);
    }
}
