package com.gtg56.idas.service.spark.tool;

import lombok.Builder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.sql.types.StructType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Link on 2020/1/2.
 */
@Builder(builderMethodName = "helper",builderClassName = "DDLBuildHelper",buildMethodName = "toBuilder")
public class DDLBuilder {
    @Builder.Default
    private String namespace = null;
    private String tableName;
    private String comment;

    @Builder.Default
    private boolean temp = false;
    @Builder.Default
    private boolean external = false;
    @Builder.Default
    private boolean hive = false;
    @Builder.Default
    private boolean validateNotExists = false;

    private StructType structType;
    private String dataType;
    private String storedPath;
    @Builder.Default
    private List<String> partitionColumns = new ArrayList<>();
    @Builder.Default
    private Map<String,String> tableProperties = new HashMap<>();

    public String build() {
        StringBuilder sb = new StringBuilder("CREATE ");

        if(temp) sb.append("TEMPORARY ");
        if(external) sb.append("EXTERNAL ");

        sb.append("TABLE ");

        if(validateNotExists) sb.append("IF NOT EXISTS ");

        if(StringUtils.isNotBlank(namespace)) sb.append(namespace).append(".");

        sb.append(tableName).append(" ");

        sb.append("(").append(structType.toDDL()).append(") ");
        if(StringUtils.isNotBlank(comment)) sb.append("COMMENT '").append(comment).append("' ");


        if(hive) {
            if(CollectionUtils.isNotEmpty(partitionColumns)) {
                sb.append("PARTITIONED BY (").append(StringUtils.join(partitionColumns,",")).append(") ");
            }
            sb.append("STORED BY ").append(dataType).append(" ");
        } else {
            sb.append("USING ").append(dataType).append(" ");
            if(CollectionUtils.isNotEmpty(partitionColumns)) {
                sb.append("PARTITIONED BY (").append(StringUtils.join(partitionColumns,",")).append(") ");
            }
        }

        sb.append("LOCATION '").append(storedPath).append("' ");

        if(MapUtils.isNotEmpty(tableProperties)) {
            sb.append("TBLPROPERTIES (");

            List<String> properties = tableProperties.entrySet().stream().map(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();

                return key + "='" + value + "'";
            }).collect(Collectors.toList());

            sb.append(StringUtils.join(properties,",")).append(") ");
        }

        if(hive)
            sb.append(";");
        return sb.toString();
    }
}
