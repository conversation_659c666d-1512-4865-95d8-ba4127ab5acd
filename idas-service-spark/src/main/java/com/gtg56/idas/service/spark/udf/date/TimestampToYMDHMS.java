package com.gtg56.idas.service.spark.udf.date;

import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.spark.udf.AbstractSparkSqlUdf;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;

import java.util.Date;

public class TimestampToYMDHMS extends AbstractSparkSqlUdf implements UDF1<Long,String> {
    private static final long serialVersionUID = -6751406954910391558L;
    
    @Override
    public String registerName() {
        return "ts_to_ymdhms";
    }
    
    @Override
    public DataType returnType() {
        return DataTypes.StringType;
    }
    
    @Override
    public String call(Long ts) throws Exception {
        if (ts == null) return null;
        return DateUtil.ymdhms().format(new Date(ts));
    }
}
