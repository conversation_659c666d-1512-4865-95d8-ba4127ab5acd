package com.gtg56.idas.service.spark.test.entity;

import com.gtg56.idas.common.entity.base.ImABean;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.Date;

/**
 * Created by Link on 2020/1/9.
 */
@Data
public class PeopleBean implements ImABean {
    private static final long serialVersionUID = 6903001129262215376L;

    private Integer id;
    private String name;
    private Integer age;
    private String job;
    private String locate;
    private Long createTime;
    private Long updateTime;

    public static PeopleBean random(int id) {
        PeopleBean bean = new PeopleBean();
        bean.setId(id);
        bean.setName(RandomStringUtils.random(10,true,false));
        bean.setAge(RandomUtils.nextInt(18,99));
        bean.setCreateTime(new Date().getTime() - 10000);
        bean.setUpdateTime(bean.getCreateTime() + 11000);

        String job = "";
        switch (RandomUtils.nextInt(0,5)) {
            case 0 : {job = "doctor";break;}
            case 1 : {job = "nurse";break;}
            case 2 : {job = "police";break;}
            case 3 : {job = "fireman";break;}
            default : {job = "programmer";break;}
        }
        bean.setJob(job);

        String locate="";
        switch (RandomUtils.nextInt(0,6)) {
            case 0 : {locate = "china";break;}
            case 1 : {locate = "japan";break;}
            case 2 : {locate = "us";break;}
            case 3 : {locate = "uk";break;}
            case 4 : {locate = "german";break;}
            default : {locate = "russia";break;}
        }
        bean.setLocate(locate);
        return bean;
    }
}
