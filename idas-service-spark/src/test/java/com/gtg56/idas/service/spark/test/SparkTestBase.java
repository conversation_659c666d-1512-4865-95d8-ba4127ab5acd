package com.gtg56.idas.service.spark.test;

import com.gtg56.idas.common.util.SpringUtil;
import com.gtg56.idas.service.spark.Application;
import com.gtg56.idas.service.spark.test.entity.PeopleBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.apache.log4j.lf5.LogLevel;
import org.apache.spark.api.java.function.FilterFunction;
import org.apache.spark.api.java.function.MapFunction;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.catalog.Table;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.springframework.context.ApplicationContext;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Link on 2020/1/9.
 */
@Slf4j
public abstract class SparkTestBase implements Serializable {
    private static final long serialVersionUID = 5932662445740008973L;

    protected static SparkSession spark;

    @BeforeClass
    public static void initSpark() {
        Application.main(new String[0]);
        spark = SpringUtil.getBean(SparkSession.class);
    }

    @After
    public void after() {
        List<Table> tmpTables = spark.catalog().listTables().filter((FilterFunction<Table>) Table::isTemporary).collectAsList();

        if(tmpTables.size() > 0) {
            List<String> tmpTableNames = tmpTables.stream().map(Table::name).collect(Collectors.toList());
            log.info("cleanup temp table : {}", StringUtils.join(tmpTableNames, " , "));
            tmpTableNames.forEach(tableName -> spark.catalog().dropTempView(tableName));
        }
    }
    
    protected static Dataset<Row> genRangePeople(int count) {
        return spark.range(count)
                .map(
                        (MapFunction<Long, PeopleBean>) id ->
                                PeopleBean.random(id.intValue()),
                        Encoders.bean(PeopleBean.class)
                )
                .toDF();
    }
}
