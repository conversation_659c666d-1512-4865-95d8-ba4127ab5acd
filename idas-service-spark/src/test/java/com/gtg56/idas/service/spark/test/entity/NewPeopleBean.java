package com.gtg56.idas.service.spark.test.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.RandomStringUtils;

@EqualsAndHashCode(callSuper = true)
@Data
public class NewPeopleBean extends PeopleBean {
    private static final long serialVersionUID = -7325022286643538564L;
    
    private String message;
    
    @SneakyThrows
    public static NewPeopleBean random(int id) {
        PeopleBean people = PeopleBean.random(id);
        NewPeopleBean newPeopleBean = new NewPeopleBean();
        
        BeanUtils.copyProperties(newPeopleBean,people);
        
        newPeopleBean.setMessage(RandomStringUtils.random(10,true,false));
        return newPeopleBean;
    }
}
