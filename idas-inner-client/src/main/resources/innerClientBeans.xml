<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application id="app" name="idas-inner-client" version="1.0" architecture="client"/>
    <import resource="classpath:/spring/dubbo.xml"/>
    <dubbo:reference id="sparkService" interface="com.gtg56.idas.service.define.ISparkService"/>
</beans>
