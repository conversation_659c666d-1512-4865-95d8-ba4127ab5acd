package com.gtg56.idas.client.inner;

import com.gtg56.idas.common.entity.dto.SparkWorkloadNodeInstanceStatusDTO;
import com.gtg56.idas.common.node.NodeStatus;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.define.ISparkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;

import java.util.Arrays;

@Slf4j
@SpringBootApplication
@DubboComponentScan
@PropertySource(value = {"classpath:/application.properties"})
@ImportResource(value = {"classpath:/innerClientBeans.xml"})
public class SparkInnerClient {
    
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(SparkInnerClient.class, args);
        ISparkService sparkService = context.getBean(ISparkService.class);
        
        if(args.length > 0) {
            String command = args[0];
            String[] arguments = new String[args.length - 1];
            System.arraycopy(args,1,arguments,0,arguments.length);
            try {
                log.info("args : {}",Arrays.toString(arguments));
                String instanceId = sparkService.startNodeByCli(command, arguments, "");
                if(StringUtils.isBlank(instanceId)) {
                    log.error("workload submit fail, return null instance id");
                    System.exit(1);
                }
                log.info("workload submit success , instance id {}", instanceId);
                
                Thread.sleep(1000);
                SparkWorkloadNodeInstanceStatusDTO status = sparkService.getStatus(instanceId);
                if(status == null) {
                    log.error("cannot get instance status of {}",instanceId);
                    System.exit(1);
                }
                
                boolean done = false;
                while (!done) {
                    status = sparkService.getStatus(instanceId);
                    if(status != null) {
                        log.info("status {}",status.getStatus());
                        done = status.getEndTime() != null;
                    }
                    Thread.sleep(1000);
                }
                log.info("workload done");
                printStatus(status);
                if(NodeStatus.SUCCESS_END.equals(status.getStatus())) {
                    log.info("workload return json : \n {}",status.getReturnJson());
                    System.exit(0);
                } else {
                    log.error("workload end with exception \n {}",status.getLastExceptionLocalized());
                    System.exit(1);
                }
            } catch (Exception e) {
                log.error("",e);
                System.exit(1);
            }
        } else {
            log.error("no arguments");
            System.exit(1);
        }
    }
    
    private static void printStatus(SparkWorkloadNodeInstanceStatusDTO dto) {
        log.info("\n####################### workload report ####################### \n" +
                        "    instance id:          {} \n" +
                        "    workload name:        {} \n" +
                        "    driver ip:            {} \n" +
                        "    submit time:          {} \n" +
                        "    execution time:       {} \n" +
                        "    end time:             {} \n" +
                        "    total cost:           {} \n" +
                        "    schedule delay:       {} \n" +
                        "    execution duration:   {} \n" +
                        "    final status:         {} \n" +
                        "########################## report end #########################",
                        dto.getInstanceId(),
                        dto.getNodeName(),
                        dto.getDriverIp(),
                        DateUtil.ymdhmss().format(dto.getSubmitTime()),
                        DateUtil.ymdhmss().format(dto.getExecutionTime()),
                        DateUtil.ymdhmss().format(dto.getEndTime()),
                        DateUtil.durationBeauty(dto.getEndTime().getTime() - dto.getSubmitTime().getTime()),
                        DateUtil.durationBeauty(dto.getExecutionTime().getTime() - dto.getSubmitTime().getTime()),
                        DateUtil.durationBeauty(dto.getEndTime().getTime() - dto.getExecutionTime().getTime()),
                        dto.getStatus());
    }
}
