#!/bin/bash
SHELL_FOLDER=$(dirname $(readlink -f "$0"))
MODULE_DIR="${SHELL_FOLDER}/../"
TARGET_DIR="${MODULE_DIR}/target"
LIB_DIR="${TARGET_DIR}/lib"

for f in ${LIB_DIR}/*.jar; do
    echo "$CLASSPATH"|grep "$f" > /dev/null 2>&1 || CLASSPATH=$CLASSPATH:$f;
done

CLASSPATH=$CLASSPATH:$TARGET_DIR/idas-inner-client-1.0-SNAPSHOT.jar

java -classpath "$CLASSPATH" com.gtg56.idas.client.inner.SparkInnerClient "$@"
