package com.gtg56.idas.service.sensor;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtg56.idas.common.consts.CommonConsts;
import com.gtg56.idas.common.consts.SensorConsts;
import com.gtg56.idas.common.consts.TSDBAggregator;
import com.gtg56.idas.common.dao.hbase.OverLimitHandlingDAO;
import com.gtg56.idas.common.entity.dto.FDAReportDTO;
import com.gtg56.idas.common.entity.dto.auth.DataPermissionDTO;
import com.gtg56.idas.common.entity.dto.auth.SensorAuthSubject;
import com.gtg56.idas.common.entity.dto.lark.UserDTO;
import com.gtg56.idas.common.entity.dto.sensor.*;
import com.gtg56.idas.common.entity.hbase.OverLimitHandling;
import com.gtg56.idas.common.entity.jdbc.FDAReport;
import com.gtg56.idas.common.entity.jdbc.UserAuth;
import com.gtg56.idas.common.entity.jdbc.WarehouseSensor;
import com.gtg56.idas.common.entity.query.*;
import com.gtg56.idas.common.service.IDataPermissionService;
import com.gtg56.idas.common.service.IFDAReportService;
import com.gtg56.idas.common.service.IWarehouseSensorService;
import com.gtg56.idas.common.tool.http.WarehouseSensorClient;
import com.gtg56.idas.common.tool.http.ZJFDAReportClient;
import com.gtg56.idas.common.util.AssertUtil;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.ISensorService;
import javafx.util.Duration;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(
        interfaceClass = ISensorService.class, protocol = "dubbo", registry = "zookeeper", timeout = 10000,
        methods = {
                @Method(name = "listSensorLastRecord", timeout = 60000),
                @Method(name = "listSensorHistoryRecord", timeout = 60000, retries = 1),
                @Method(name = "listOverlimitHandling", timeout = 60000),
                @Method(name = "stat", timeout = 60000, retries = 1),
                @Method(name = "listFDAReportPage", timeout = 60000),
        },
        filter = "-exception"
)
public class SensorService implements ISensorService {
    
    @Resource(name = "warehouseSensorService")
    private IWarehouseSensorService warehouseSensorService;
    
    @Resource(name = "tsdbClient")
    private WarehouseSensorClient tsdbClient;
    
    @Resource(name = "overLimitHandlingDAO")
    private OverLimitHandlingDAO overLimitHandlingDAO;
    
    @Resource
    private IAuthService authService;
    
    @Resource(name = "fdaReportService")
    private IFDAReportService fdaReportService;
    
    @Resource(name = "zjFDAClient")
    private ZJFDAReportClient zjFDAClient;

    @Resource(name = "dataPermissionService")
    private IDataPermissionService dataPermissionService;
    
    @Override
    public List<WarehouseDTO> listWarehouse() {
        return warehouseSensorService
                .listWarehouse()
                .stream()
                .map(ws -> {
                    WarehouseDTO dto = new WarehouseDTO();
                    BeanUtils.copyProperties(ws, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }


    //TODO
    @Override
    public List<WarehouseSensor> findSensor(WarehouseSensorQuery query) {
        //无登录用户，如广交大屏
        List<WarehouseSensor> result = warehouseSensorService.findBy(query);
        if(query.getUser() == null){
            return result;
        }
        //1-全部授权
        Set<String> warehouseCodes = authWarehouseCodes(query.getUser());
        if (warehouseCodes.contains(CommonConsts.AUTH_ALL)) {
            return result;
        }
        //2-部分授权
        List<DataPermissionDTO> dataPermissionDTOList = dataPermissionService.listVOByUserId(query.getUser().getId());
        Set<String> sensorCodeSet = dataPermissionDTOList.stream()
                    .map(DataPermissionDTO::getSensorCode)
                    .collect(Collectors.toSet());
        result = result.stream().filter(sensor -> sensorCodeSet.contains(sensor.getSensorCode())).collect(Collectors.toList());
        //
        return result;
    }
    
    @Override
    public SensorResultDTO listSensorLastRecord(WarehouseSensorLastQuery query) {
        List<WarehouseSensor> warehouseSensors = warehouseSensorService.listByCompares(query.getCompares());

        Stream<SensorRecordDTO> lastRecords = tsdbClient.getLastRecordBatch(warehouseSensors).parallelStream();

        lastRecords = serverSideFilter(lastRecords, query);

        List<SensorRecordDTO> res = lastRecords.collect(Collectors.toList());
        //将warehouseSensors 生成warehouse 为key，WarehouseSensor 为value的map

        Map<String, String> warehouseSensorMap = new HashMap<>();
        for(WarehouseSensor warehouseSensor: warehouseSensors){
            warehouseSensorMap.put(warehouseSensor.getWarehouseCode(), warehouseSensor.getWarehouseName());
        }

        List<SensorRecordDTO> newRes = new ArrayList<>();
        for (SensorRecordDTO dto : res) {
            dto.setWarehouseName(warehouseSensorMap.get(dto.getWarehouseCode()));
            newRes.add(dto);
        }

        // 仓库名称含有“停用” 将不显示显示在实时数据里
        try{
            newRes = newRes.stream().filter(dto -> !dto.getWarehouseName().contains("停用")).collect(Collectors.toList());
        }catch (Exception ee){
            ee.printStackTrace();
        }

        SensorResultDTO result = new SensorResultDTO();
        result.setDetail(newRes);
        result.setStat(buildStat(newRes));

        return result;
    }
    
    @Override
    public SensorResultDTO listSensorHistoryRecord(WarehouseSensorHistoryQuery query) {
        AssertUtil.notNull(query.getStartTime(), "测量时间不能为空");
        Date now = new Date();
        if (query.getEndTime() == null || query.getEndTime().after(now)) {
            query.setEndTime(now);
        }
    
        Stream<SensorRecordDTO> base = listHistory(query.getCompares(), query.getStartTime(), query.getEndTime());
    
        base = serverSideFilter(base, query);
    
        List<SensorRecordDTO> details = base
                .peek(dto -> dto.setRecordTime(new Date(dto.getRecordTime().getTime() + (long) Duration.minutes(30).toMillis())))
                .sorted(Comparator.comparing(SensorRecordDTO::getRecordTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        SensorResultDTO result = new SensorResultDTO();
        result.setDetail(details);
        result.setStat(buildStat(details));
    
        return result;
    }
    
    private Stream<SensorRecordDTO> serverSideFilter(Stream<SensorRecordDTO> data, WarehouseSensorLastQuery query) {
        Stream<SensorRecordDTO> base = data;
        if (StringUtils.isNotBlank(query.getTemperatureStatus())) {
            base = base.filter(dto -> {
                if (SensorConsts.STATUS_OVER_LIMIT.equals(query.getTemperatureStatus())) {
                    return SensorConsts.STATUS_HIGH.equals(dto.getTemperatureStatus())
                            || SensorConsts.STATUS_LOW.equals(dto.getTemperatureStatus());
                } else {
                    return query.getTemperatureStatus().equals(dto.getTemperatureStatus());
                }
            });
        }
    
        if (StringUtils.isNotBlank(query.getHumidityStatus())) {
            base = base.filter(dto -> {
                if (SensorConsts.STATUS_OVER_LIMIT.equals(query.getHumidityStatus())) {
                    return SensorConsts.STATUS_HIGH.equals(dto.getHumidityStatus())
                            || SensorConsts.STATUS_LOW.equals(dto.getHumidityStatus());
                } else {
                    return query.getHumidityStatus().equals(dto.getHumidityStatus());
                }
            });
        }
    
        if (query.getTemperatureLow() != null) {
            base = base.filter(dto -> dto.getTemperature() != null && query.getTemperatureLow().doubleValue() <= dto.getTemperature().doubleValue());
        }
        if (query.getTemperatureHigh() != null) {
            base = base.filter(dto -> dto.getTemperature() != null && query.getTemperatureHigh().doubleValue() >= dto.getTemperature().doubleValue());
        }
        if (query.getHumidityLow() != null) {
            base = base.filter(dto -> dto.getHumidity() != null && query.getHumidityLow().doubleValue() <= dto.getHumidity().doubleValue());
        }
        if (query.getHumidityHigh() != null) {
            base = base.filter(dto -> dto.getHumidity() != null && query.getHumidityHigh().doubleValue() >= dto.getHumidity().doubleValue());
        }
        return base;
    }
    
    private SensorRecordStatDTO buildStat(List<SensorRecordDTO> details) {
        SensorRecordStatDTO stat = new SensorRecordStatDTO();
        
        if (!details.isEmpty()) {
            
            // 温度统计
            Comparator<SensorRecordDTO> temComp = Comparator.comparing(SensorRecordDTO::getTemperature);
            // 最高
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .filter(dto -> dto.getTemperature() != null)
                    .max(temComp)
                    .ifPresent(max -> {
                        stat.setTemperatureMax(max.getTemperature());
                        stat.setTemperatureMaxSensorCode(max.getSensorCode());
                    });
    
            // 最低
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .filter(dto -> dto.getTemperature() != null)
                    .min(temComp)
                    .ifPresent(min -> {
                        stat.setTemperatureMin(min.getTemperature());
                        stat.setTemperatureMinSensorCode(min.getSensorCode());
                    });
            // 平均
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .map(SensorRecordDTO::getTemperature)
                    .filter(Objects::nonNull)
                    .mapToDouble(BigDecimal::doubleValue)
                    .average()
                    .ifPresent(avg -> stat.setTemperatureAvg(BigDecimal.valueOf(avg).setScale(2, RoundingMode.CEILING)));
    
            // 湿度统计
            Comparator<SensorRecordDTO> humComp = Comparator.comparing(SensorRecordDTO::getHumidity);
            // 最高
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .filter(dto -> dto.getHumidity() != null)
                    .max(humComp)
                    .ifPresent(max -> {
                        stat.setHumidityMax(max.getHumidity());
                        stat.setHumidityMaxSensorCode(max.getSensorCode());
                    });
            // 最低
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .filter(dto -> dto.getHumidity() != null)
                    .min(humComp)
                    .ifPresent(min -> {
                        stat.setHumidityMin(min.getHumidity());
                        stat.setHumidityMinSensorCode(min.getSensorCode());
                    });
            // 平均
            details.parallelStream()
                    .filter(Objects::nonNull)
                    .map(SensorRecordDTO::getHumidity)
                    .filter(Objects::nonNull)
                    .mapToDouble(BigDecimal::doubleValue)
                    .average()
                    .ifPresent(avg -> stat.setHumidityAvg(BigDecimal.valueOf(avg).setScale(2, RoundingMode.CEILING)));
    
        }
        return stat;
    }
    
    @Override
    public List<OverLimitHandlingDTO> listOverlimitHandling(WarehouseOverlimitHandlingQuery query) {
        AssertUtil.notNull(query.getStartTime(), "测量时间不能为空");
    
        if (query.getEndTime() == null) {
            query.setEndTime(new Date());
        }
    
        Stream<OverLimitHandling> base = query.getCompares().parallelStream()
                .map(SensorCompare::getSensorCode)
                .flatMap(sensorCode ->
                        overLimitHandlingDAO.scan(sensorCode, query.getStartTime(), query.getEndTime()).parallelStream()
                );
        if (query.getHandleStartTime() != null) {
            base = base.filter(olh -> olh.getHandleTime().after(query.getHandleStartTime()));
        }
        if (query.getHandleEndTime() != null) {
            base = base.filter(olh -> olh.getHandleTime().before(query.getHandleEndTime()));
        }
        return base.map(olh -> {
            OverLimitHandlingDTO dto = new OverLimitHandlingDTO();
            BeanUtils.copyProperties(olh, dto);
            return dto;
        }).sorted(Comparator.comparing(OverLimitHandlingDTO::getRecordTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SensorRecordDTO> listRaw(WarehouseSensorRawQuery query) {
        AssertUtil.notNull(query.getStartTime(), "测量时间不能为空");
        AssertUtil.notNull(query.getEndTime(), "测量时间不能为空");
        AssertUtil.isNotEmpty(query.getCompares(), "测点不能为空");

        return warehouseSensorService.listByCompares(query.getCompares())
                .parallelStream()
                .flatMap(sensor ->
                        tsdbClient.getRecords(sensor, query.getStartTime(), query.getEndTime(), TSDBAggregator.NONE, null)
                                .parallelStream()
                ).filter(Objects::nonNull)
                .sorted(Comparator.comparing(SensorRecordDTO::getRecordTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }
    
    @Override
    public SensorStatResultDTO stat(WarehouseStatQuery query) {
        Date startTime = query.getStartTime();
        Date now = new Date();
        if (query.getEndTime() == null || query.getEndTime().after(now)) {
            query.setEndTime(now);
        }
        Date endTime = query.getEndTime();
        String type = query.getType();
    
        SensorStatResultDTO stat = new SensorStatResultDTO();
      //  List<SensorRecordDTO> details1 = listHistory(query.getCompares(), startTime, endTime).collect(Collectors.toList());
        List<SensorStatResultDTO.Detail> details =
                listHistory(query.getCompares(), startTime, endTime)
                        .map(dto -> {
                            SensorStatResultDTO.Detail detail = new SensorStatResultDTO.Detail();
                            BeanUtils.copyProperties(dto, detail);
                            if (SensorConsts.TYPE_HUMIDITY.equals(type)) {
                                detail.setValue(dto.getHumidity());
                            } else {
                                detail.setValue(dto.getTemperature());
                            }
                            return detail;
                        })
                        .peek(dto -> dto.setRecordTime(new Date(dto.getRecordTime().getTime() + (long) Duration.minutes(30).toMillis())))
                        .sorted(Comparator.comparing(SensorStatResultDTO.Detail::getRecordTime))
                        .collect(Collectors.toList());
    
        stat.setDetails(details);
        details.parallelStream()
                .filter(d -> d.getValue() != null)
                .mapToDouble(d -> d.getValue().doubleValue())
                .average()
                .ifPresent(avg -> stat.setAvg(BigDecimal.valueOf(avg).setScale(2, RoundingMode.CEILING)));
        details.parallelStream()
                .filter(d -> d.getValue() != null)
                .mapToDouble(d -> d.getValue().doubleValue())
                .min()
                .ifPresent(min -> stat.setMin(BigDecimal.valueOf(min).setScale(2, RoundingMode.CEILING)));
        details.parallelStream()
                .filter(d -> d.getValue() != null)
                .mapToDouble(d -> d.getValue().doubleValue())
                .max().
                ifPresent(max -> stat.setMax(BigDecimal.valueOf(max).setScale(2, RoundingMode.CEILING)));
    
        return stat;
    }
    
    @Override
    public PageInfo<FDAReportDTO> listFDAReportPage(FDAReportQuery query) {
        PageInfo<FDAReport> page = fdaReportService.findPage(query);
        List<FDAReportDTO> result = page.getList().parallelStream()
                .peek(report -> {
                    if (report.getStatusFlag() == null) {
        
                        // 浙江药监
                        if (SensorConsts.ZJFDA_CODE.equals(report.getFdaCode())) {
                            processZJFDA(report);
                        }
                    }
                })
                .map(fdaReportService::decompress)
                .collect(Collectors.toList());

        PageInfo<FDAReportDTO> ret = new PageInfo<>();
        BeanUtils.copyProperties(page, ret, "list");
        ret.setList(result);
        return ret;
    }

    @Override
    public WarehouseSensor edit(WarehouseSensor sensor) {
        AssertUtil.notNull(sensor, "传感器参数为空");
        AssertUtil.isNotBlank(sensor.getWarehouseCode(), "仓库编码不能为空");
        AssertUtil.isNotBlank(sensor.getRegionCode(), "区域编码不能为空");
        AssertUtil.isNotBlank(sensor.getSensorCode(), "传感器编码不能为空");

        WarehouseSensor inDB = warehouseSensorService.getBy(sensor.getWarehouseCode(), sensor.getRegionCode(), sensor.getSensorCode());
        AssertUtil.notNull(inDB, "查无记录");
        BeanUtils.copyProperties(sensor, inDB, "id", "createTime", "modifyTime");
        inDB.setModifyTime(new Date());
        warehouseSensorService.update(inDB);

        return inDB;
    }

    private void processZJFDA(FDAReport report) {
        String reportExtInfo = report.getReportExtInfo();
        JSONObject json = JSONObject.parseObject(reportExtInfo);
        String fileTypeNumber = json.getString("fileTypeNumber");
        Date reportTime = report.getReportTime();
        List<ZJFDAReportClient.QueryRecord> res = zjFDAClient.query(
                new Date(reportTime.getTime() - (long) Duration.hours(2).toMillis()),
                new Date(reportTime.getTime() + (long) Duration.hours(2).toMillis()),
                fileTypeNumber
        );
    
        if (res.size() > 0) {
            String opResult = res.get(0).getResult();
            if (opResult.startsWith(ZJFDAReportClient.SUCCESS_CODE)) {
                report.setStatusMsg(CommonConsts.CHN_SUCCESS);
                report.setStatusFlag(CommonConsts.INT_FLAG_SUCCESS);
            } else {
                report.setStatusMsg(opResult);
                report.setStatusFlag(CommonConsts.INT_FLAG_FAIL);
            }
            report.baseSet();
            fdaReportService.update(report);
        } else {
            report.setStatusFlag(CommonConsts.INT_FLAG_FAIL);
            report.setStatusMsg("药监查无记录，请核实");
            // 超过3天则入库
            if (Math.abs(DateUtil.dayDiff(reportTime, new Date())) > 3) {
                report.baseSet();
                fdaReportService.update(report);
            }
        }
    }
    
    private Stream<SensorRecordDTO> listHistory(List<SensorCompare> compares, Date startTime, Date endTime) {
        return warehouseSensorService.listByCompares(compares)
                .parallelStream()
                .flatMap(sensor ->
                        tsdbClient.getRecords(
                                sensor, startTime, endTime,
                                SensorConsts.HISTORY_AGGREGATOR, SensorConsts.HISTORY_DOWNSAMPLE)
                                .parallelStream()
                ).filter(Objects::nonNull);
    }
    
    private Set<String> authWarehouseCodes(UserDTO user) {
        if (user.isAdmin()) {
            return listWarehouse().stream()
                    .map(WarehouseDTO::getWarehouseCode)
                    .collect(Collectors.toSet());
        }
        
        UserAuthQuery query = new UserAuthQuery()
                .setUserId(user.getId())
                .setUsername(user.getUsername())
                .setAuthRealm(SensorConsts.AUTH_REALM);
        List<UserAuth> userAuths = authService.find(query);
        if (userAuths.isEmpty()) return Collections.emptySet();
        UserAuth userAuth = userAuths.get(0);
        
        String authSubject = userAuth.getAuthSubject();
        SensorAuthSubject sensorAuthSubject = JSONObject.parseObject(authSubject, SensorAuthSubject.class);
        if (sensorAuthSubject.getWarehouseCodes() != null) {
            return new HashSet<>(sensorAuthSubject.getWarehouseCodes());
        } else {
            return Collections.emptySet();
        }
    }
//
//    @SneakyThrows
//    public static void main(String[] args) {
//        WarehouseSensorClient client = new WarehouseSensorClient("http://10.1.9.63:4242");
//        WarehouseSensor ws = new WarehouseSensor()
//                .setWarehouseCode("NHYYWLZX001")
//                .setRegionCode("1000-4")
//                .setSensorCode("NHYYWLZX001-10006515")
//                .setTemperatureHighLimit(BigDecimal.valueOf(100))
//                .setTemperatureLowLimit(BigDecimal.valueOf(0))
//                .setHumidityHighLimit(BigDecimal.valueOf(100))
//                .setHumidityLowLimit(BigDecimal.valueOf(0));
//        List<SensorRecordDTO> res = client.getRecords(ws, DateUtil.tsdb().parse("2021/03/18 00:00:00"), DateUtil.tsdb().parse("2021/03/18 23:59:59"), TSDBDownSample._30MIN_LAST_NULL);
//        res.forEach(System.out::println);
//    }
}
