package com.gtg56.idas.service.sensor.datacheck;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckResultDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.service.IDataCheckResultDetailService;
import com.gtg56.idas.service.define.ICheckResultDetailService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@DubboService(
        interfaceClass = ICheckResultDetailService.class, protocol = "dubbo", registry = "zookeeper", timeout = 10000,
        filter = "-exception"
)
public class CheckResultDetailService implements ICheckResultDetailService {

    @Resource(name = "dataCheckResultDetailService")
    private IDataCheckResultDetailService dataCheckResultDetailService;

    @Override
    public DataCheckResultDetail saveData(DataCheckResultDetail dataCheckResultDetail) {
        return dataCheckResultDetailService.saveData(dataCheckResultDetail);
    }

    @Override
    public DataCheckResultDetail updateData(DataCheckResultDetail dataCheckResultDetail) {
        return dataCheckResultDetailService.updateData(dataCheckResultDetail);
    }

    @Override
    public DataCheckResultDetail getById(Long id) {
        return dataCheckResultDetailService.findById(id);
    }


    @Override
    public DataCheckResultDetail removeById(Long id) {
        dataCheckResultDetailService.deleteById(id);
        return null;
    }

    @Override
    public PageObject<DataCheckResultDetail> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataCheckResultDetailService.pageByMap(var1,var2);
    }

    @Override
    public PageObject<DataCheckResultDetail> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataCheckResultDetailService.pageByQuery(var1,var2);
    }

    @Override
    public List<DataCheckResultDetail> listByIds(String ids) {
        return dataCheckResultDetailService.findByIds(ids);
    }

    @Override
    public List<DataCheckResultDetail> findDataCheckResultDetailList(Long dataCheckConfigDetailId, String startDate, String endDate) {
        return dataCheckResultDetailService.findDataCheckResultDetailList(dataCheckConfigDetailId,startDate,endDate);
    }
}
