package com.gtg56.idas.service.sensor.datacheck;

import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.service.IDataCheckConfigService;
import com.gtg56.idas.service.define.IAuthService;
import com.gtg56.idas.service.define.ICheckConfigService;
import com.gtg56.idas.service.define.ISensorService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
@DubboService(
        interfaceClass = ICheckConfigService.class, protocol = "dubbo", registry = "zookeeper", timeout = 10000,
        filter = "-exception"
)
public class CheckConfigService implements ICheckConfigService {

    @Resource(name = "dataCheckConfigService")
    private IDataCheckConfigService dataCheckConfigService;


    @Override
    public DataCheckConfig saveData(DataCheckConfig dataCheckConfig) {
        return dataCheckConfigService.saveData(dataCheckConfig);
    }

    @Override
    public DataCheckConfig updateData(DataCheckConfig dataCheckConfig) {
        return dataCheckConfigService.updateData(dataCheckConfig);
    }

    @Override
    public DataCheckConfig getById(Long id) {

        return dataCheckConfigService.findById(id);
    }

    @Override
    public DataCheckConfig removeById(Long id) {
        dataCheckConfigService.deleteById(id);
        return null;
    }

    @Override
    public PageObject<DataCheckConfig> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataCheckConfigService.pageByMap(var1,var2);
    }

    @Override
    public PageObject<DataCheckConfig> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataCheckConfigService.pageByQuery(var1,var2);
    }

    @Override
    public List<DataCheckConfig> listByIds(String ids) {
        return dataCheckConfigService.findByIds(ids);
    }

    @Override
    public List<DataCheckConfig> findAll() {
        return dataCheckConfigService.findAll();
    }

    @Override
    public DataCheckConfig updateStatus(String id, String status) {
        return dataCheckConfigService.updateStatus(id,status);
    }
}
