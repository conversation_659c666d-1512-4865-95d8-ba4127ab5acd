package com.gtg56.idas.service.sensor.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: idas
 * @description: excel探测点导入warehouse_sensor表
 * @author: zonghua_liang
 * @create: 2025-04-21 14:23
 **/
public class SensorExcel2Sql {

    static String sensorFile = "/Users/<USER>/Desktop/广交物流/202504神山仓温湿度对接/广交温湿度平台测点配置20250421.xlsx";
    static String INSERT_SQL = "INSERT INTO `idas`.`warehouse_sensor`" +
            "(`corp_code`, `warehouse_code`, `warehouse_name`, `region_code`, `region_name`," +
            " `sensor_code_origin`, `sensor_code`, `sensor_name`, `sensor_type`, `temperature_high_limit`, " +
            " `temperature_low_limit`, `humidity_high_limit`, `humidity_low_limit`, `sensor_function`, `create_time`," +
            " `modify_time`, `status`) " +
            " VALUES (" +
            " '%s', '%s', '%s', '%s', '%s'," +
            " '%s', '%s', '%s', '%s', %s," +
            " %s, %s, %s, '%s', NOW()," +
            " NOW(), %s" +
            ");";
    public static void main(String[] args) {
        //1、读取excel文件，转换成SensorData对象
        EasyExcel.read(sensorFile, SensorData.class, new ReadListener<SensorData>() {
            @Override
            public void invoke(SensorData data, AnalysisContext context) {
                String mySensorCode = data.getWarehouseCode() + "-" + data.getSensorCodeOrigin();
                String status = "是".equals(data.getStatus()) ? "1" : "0";
                String sql = String.format(INSERT_SQL,
                        data.getCorpCode(),
                        data.getWarehouseCode(),
                        data.getWarehouseName(),
                        data.getRegionCode(),
                        data.getRegionName(),
                        data.getSensorCodeOrigin(),
                        mySensorCode,
                        data.getSensorName(),
                        data.getSensorType(),
                        data.getTemperatureHighLimit(),
                        data.getTemperatureLowLimit(),
                        data.getHumidityHighLimit(),
                        data.getHumidityLowLimit(),
                        data.getSensorFunction(),
                        status);
                System.out.println(sql);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();

        //2、每行数据生成一个insert sql

    }


    @Getter
    @Setter
    public static class SensorData {
        @ExcelProperty(value = "厂家编码")
        String corpCode;
        @ExcelProperty(value = "仓库编码")
        String warehouseCode;
        @ExcelProperty(value = "仓库名称")
        String warehouseName;
        @ExcelProperty(value = "库房编码")
        String regionCode;
        @ExcelProperty(value = "库房名称")
        String regionName;
        @ExcelProperty(value = "测点终端编码（厂商）")
        String sensorCodeOrigin;
        @ExcelProperty(value = "测点终端名称")
        String sensorName;
        @ExcelProperty(value = "测点终端类型（厂商定义）")
        String sensorType;
        @ExcelProperty(value = "温度上限")
        String temperatureHighLimit;
        @ExcelProperty(value = "温度下限")
        String temperatureLowLimit;
        @ExcelProperty(value = "湿度上限")
        String humidityHighLimit;
        @ExcelProperty(value = "湿度下限")
        String humidityLowLimit;
        @ExcelProperty(value = "传感器功能（SENSOR：仓库测点；CAR：冷藏车；BOX：保温箱；OTHER：其他）")
        String sensorFunction;
        @ExcelProperty(value = "是否启用")
        String status;
    }
}