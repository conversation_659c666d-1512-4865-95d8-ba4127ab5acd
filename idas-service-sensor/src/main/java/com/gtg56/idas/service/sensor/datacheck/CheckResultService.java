package com.gtg56.idas.service.sensor.datacheck;

import com.gtg56.idas.common.convert.DataCheckConfigDetailConvert;
import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.dto.sensor.SensorStatResultDTO;
import com.gtg56.idas.common.entity.dto.sensor.WarehouseSensorDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResult;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.entity.query.SensorCompare;
import com.gtg56.idas.common.entity.query.WarehouseStatQuery;
import com.gtg56.idas.common.service.IDataCheckResultDetailService;
import com.gtg56.idas.common.service.IDataCheckResultService;
import com.gtg56.idas.common.util.BeanUtil;
import com.gtg56.idas.common.util.DateUtil;
import com.gtg56.idas.service.define.ICheckConfigDetailService;
import com.gtg56.idas.service.define.ICheckConfigService;
import com.gtg56.idas.service.define.ICheckResultService;
import com.gtg56.idas.service.define.ISensorService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@DubboService(
        interfaceClass = ICheckResultService.class, protocol = "dubbo", registry = "zookeeper", timeout = 10000000,
        filter = "-exception"
)
public class CheckResultService implements ICheckResultService {

    /**
     * 分割符
     */
    private final static String SPLIT_CODE = "@@";

    /**
     * 一个监测点一天的记录数
     */
    private final static int RECORDS_OF_DAY = 47;

    @Resource(name = "sensorService")
    private ISensorService sensorService;

    @Resource(name = "checkConfigService")
    private ICheckConfigService checkConfigService;

    @Resource(name = "checkConfigDetailService")
    private ICheckConfigDetailService checkConfigDetailService;

    @Resource(name = "dataCheckResultService")
    private IDataCheckResultService dataCheckResultService;

    @Resource(name = "dataCheckResultDetailService")
    private IDataCheckResultDetailService dataCheckResultDetailService;



    @Override
    public DataCheckResult saveData(DataCheckResult dataCheckResult) {
        return dataCheckResultService.saveData(dataCheckResult);
    }

    @Override
    public DataCheckResult updateData(DataCheckResult dataCheckResult) {
        return dataCheckResultService.updateData(dataCheckResult);
    }

    @Override
    public DataCheckResult getById(Long id) {
        return dataCheckResultService.findById(id);
    }

    @Override
    public List<DataCheckResult> listByIds(String ids) {
        return dataCheckResultService.findByIds(ids);
    }



    @Override
    public DataCheckResult removeById(Long id) {
        dataCheckResultService.deleteById(id);
        return null;
    }

    @Override
    public PageObject<DataCheckResult> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataCheckResultService.pageByMap(var1,var2);
    }

    @Override
    public PageObject<DataCheckResult> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataCheckResultService.pageByQuery(var1,var2);
    }

    @SneakyThrows
    @Override
    public List<DataCheckResult> doCheckData(DataCheckConfig dataCheckConfig) {

        Date stratDate = dataCheckConfig.getStartDate();
        Date endDate = dataCheckConfig.getEndDate();

        List<DataCheckResult> dataCheckResultList = new ArrayList<>();
        dataCheckConfig = checkConfigService.getById(dataCheckConfig.getId());

        if (dataCheckConfig == null || "0".equals(dataCheckConfig.getCheckType())) {
            return null;
        }


        //判断
        if("2".equals(dataCheckConfig.getCheckType())&& (stratDate ==  null || endDate == null)) {//2 指定时间段
            stratDate = dataCheckConfig.getStartDate();
            endDate = DateUtil.dayAdd(dataCheckConfig.getEndDate(), 1).getTime();
        }else if("1".equals(dataCheckConfig.getCheckType()) && (stratDate ==  null || endDate == null)) {//1 固定天数
            if (dataCheckConfig.getDays() <= 0){
                dataCheckConfig.setDays(1);
            }
            //往前计算时间
            Date today = DateUtil.today();//当天0点
            stratDate = DateUtil.dayAdd(today, -dataCheckConfig.getDays()).getTime();
            endDate = today;
            System.out.println("stratDate:"+stratDate);
            System.out.println("endDate:"+endDate);
        }

        System.out.println("stratDate:"+stratDate);
        System.out.println("endDate:"+endDate);

        List<DataCheckConfigDetailDTO> dataCheckConfigDetailList = checkConfigDetailService.findByConfigId(dataCheckConfig.getId());
        for(DataCheckConfigDetailDTO dataCheckConfigDetailDTO:dataCheckConfigDetailList){
            DataCheckResult dataCheckResult = doCheckData(dataCheckConfigDetailDTO, stratDate, endDate);
            if (dataCheckResult != null){
                dataCheckResultList.add(dataCheckResult);
            }
        }

        return dataCheckResultList;
    }



    //支持明细级别检测
    private DataCheckResult doCheckData(DataCheckConfigDetailDTO dataCheckConfigDetailDTO, Date startDate, Date endDate){

        //用于统计执行时间
        Date startTime = new Date();

        //用于返回结果
        DataCheckResult  dataCheckResultForReturn = new DataCheckResult();
        //每条配置明细对应一条检查结果
        DataCheckResult  dataCheckResult = new DataCheckResult();
        WarehouseStatQuery query = new WarehouseStatQuery();

        query.setStartTime(startDate);
        query.setEndTime(endDate);
        query.setType("TEMPERATURE");

        List<WarehouseSensorDTO> warehouseSensorDTOList = dataCheckConfigDetailDTO.getWarehouseSensorDTOList();

        List<SensorCompare> sensorCompareList = new ArrayList<>();
        for (WarehouseSensorDTO warehouseSensorDTO:warehouseSensorDTOList){
            SensorCompare sensorCompare = new SensorCompare();
            sensorCompare.setWarehouseCode(warehouseSensorDTO.getWarehouseCode());
            sensorCompare.setRegionCode(warehouseSensorDTO.getRegionCode());
            sensorCompare.setSensorCode(warehouseSensorDTO.getSensorCode());
            sensorCompareList.add(sensorCompare);
        }

        query.setCompares(sensorCompareList);

        List<DataCheckResultDetail> dataCheckResultDetailForExistsList = dataCheckResultDetailService.findDataCheckResultDetailList(Long.valueOf(dataCheckConfigDetailDTO.getId()),DateUtil.ymd().format(startDate),DateUtil.ymd().format(endDate));

        Map<String,DataCheckResultDetail> dataCheckResultDetailForExistsMap = new HashMap<>();
        for(DataCheckResultDetail detail:dataCheckResultDetailForExistsList) {

            String recordDate = DateUtil.formatToDateStr(detail.getTradeDate());
            String key = recordDate + "@@" + detail.getSensorCode();

            dataCheckResultDetailForExistsMap.put(key,detail);
        }

        //新增-新
        List<DataCheckResultDetail> dataCheckResultDetailForAddList = new ArrayList<>();

        //更新-旧
        List<DataCheckResultDetail> dataCheckResultDetailForUpdateList = new ArrayList<>();


        //查询监测点历史数据
        SensorStatResultDTO sensorStatResultDTO = sensorService.stat(query);

        List<DataCheckResultDetail> dataCheckResultDetailList  = new ArrayList<>();
        //找到监测点历史记录
        if(sensorStatResultDTO != null && CollectionUtils.isNotEmpty(sensorStatResultDTO.getDetails())){
            //处理明细数据--有异常数据
            dataCheckResultDetailList  = this.doCheckData(dataCheckConfigDetailDTO, startDate, endDate,warehouseSensorDTOList,sensorStatResultDTO);

            if(CollectionUtils.isNotEmpty(dataCheckResultDetailList)){//
                dataCheckResult.setCheckStatus(1);
                for (DataCheckResultDetail detail:dataCheckResultDetailList){


                    String sensorCode = detail.getSensorCode();
                    String recordDate = DateUtil.formatToDateStr(detail.getTradeDate());
                    String key = recordDate + "@@" + sensorCode;
                    DataCheckResultDetail detailExists = dataCheckResultDetailForExistsMap.get(key);

                    if(detailExists != null){
                        detailExists.setRecheckTime(new Date());
                        dataCheckResultDetailForUpdateList.add(detailExists);
                    }else {
                        dataCheckResultDetailForAddList.add(detail);
                    }
                }

            }else{
                dataCheckResult.setCheckStatus(0);
                //检测正常但历史异常，更新历史记录全部为正常
                if(CollectionUtils.isNotEmpty(dataCheckResultDetailForExistsList)){
                    for(DataCheckResultDetail detail:dataCheckResultDetailForExistsList){
                        detail.setCheckStatus(0);
                        detail.setMissQuantity(0);
                        detail.setRecheckTime(new Date());
                        dataCheckResultDetailForUpdateList.add(detail);
                    }
                }
            }

        }else {//无法找到监测点数据

            //构建全部异常数据
            dataCheckResult.setCheckStatus(1);//设置异常
            for (Date date = startDate; date.before(endDate); date = DateUtil.dayAdd(date, 1).getTime()) {
                for (WarehouseSensorDTO warehouseSensorDTO: warehouseSensorDTOList){

                    String sensorCode = warehouseSensorDTO.getSensorCode();
                    String recordDate = DateUtil.formatToDateStr(date);
                    String key = recordDate + "@@" + sensorCode;
                    DataCheckResultDetail detailExists = dataCheckResultDetailForExistsMap.get(key);

                    if(detailExists != null){
                        detailExists.setRecheckTime(new Date());
                        dataCheckResultDetailForUpdateList.add(detailExists);
                    }else {

                        DataCheckResultDetail detail = new DataCheckResultDetail();
                        detail.setCheckStatus(1);
                        detail.setCheckTime(new Date());
                        detail.setDataCheckConfigDetailId(Long.valueOf(dataCheckConfigDetailDTO.getId()));
                        detail.setSensorCode(warehouseSensorDTO.getSensorCode());
                        detail.setTradeDate(date);
                        detail.setMissQuantity(RECORDS_OF_DAY);
                        if(warehouseSensorDTO != null){
                            detail.setWarehouseSensorId(Long.valueOf(warehouseSensorDTO.getId()));;
                            detail.setWarehouseCode(warehouseSensorDTO.getWarehouseCode());
                            detail.setRegionCode(warehouseSensorDTO.getRegionCode());
                        }
                        dataCheckResultDetailForAddList.add(detail);
                    }
                }
            }
        }

        int totalQuantity = warehouseSensorDTOList.size() * DateUtil.dayDiff(startDate,endDate);
        int unnormalQuantity = 0;
        int normalQuantity = totalQuantity;
        if(dataCheckResult.getCheckStatus() != 0){
            unnormalQuantity = dataCheckResultDetailForAddList.size()+dataCheckResultDetailForUpdateList.size();
            normalQuantity = totalQuantity - unnormalQuantity;
        }

        Date endTime = new Date();
        dataCheckResult.setDataCheckConfigId(dataCheckConfigDetailDTO.getDataCheckConfigId());
        dataCheckResult.setDataCheckConfigDetailId(Long.valueOf(dataCheckConfigDetailDTO.getId()));
        dataCheckResult.setCheckStartDate(startDate);
        dataCheckResult.setCheckEndDate(endDate);
        dataCheckResult.setStartTime(startTime);
        dataCheckResult.setEndTime(endTime);
        dataCheckResult.setNormalQuantity(normalQuantity);
        dataCheckResult.setUnnormalQuantity(unnormalQuantity);
        Long useTime = (endTime.getTime() - startTime.getTime())/1000;
        dataCheckResult.setUseTime(useTime);

        dataCheckResult.setCreatorId(0L);
        dataCheckResult.setExcuteTime(startTime);
        BeanUtils.copyProperties(dataCheckResult, dataCheckResultForReturn);
        dataCheckResult = this.saveData(dataCheckResult);

        System.out.println("dataCheckResultDetailForAddList  add  : " + dataCheckResultDetailForAddList.size());
        dataCheckResultDetailForAddList.forEach(detail->{
            dataCheckResultDetailService.saveData(detail);
        });

        System.out.println("dataCheckResultDetailForUpdateList  update  : " + dataCheckResultDetailForUpdateList.size());
        dataCheckResultDetailForUpdateList.forEach(detail->{
            dataCheckResultDetailService.updateData(detail);
        });

        return dataCheckResultForReturn;
    }


    @SneakyThrows
    private List<DataCheckResultDetail> doCheckData(DataCheckConfigDetailDTO dataCheckConfigDetailDTO, Date startDate, Date endDate, List<WarehouseSensorDTO> warehouseSensorDTOList, SensorStatResultDTO sensorStatResultDTO) {

        int size = sensorStatResultDTO.getDetails().size();
        System.out.println("=================================:"+size);

        //方便后续保存数据时使用
        Map<String,WarehouseSensorDTO> warehouseSensorDTOMap = new HashMap<>();
        for (WarehouseSensorDTO warehouseSensorDTO:warehouseSensorDTOList){
            warehouseSensorDTOMap.put(warehouseSensorDTO.getSensorCode(),warehouseSensorDTO);
        }

        List<String> dateList = DateUtil.getDateListBetween(DateUtil.ymd().format(startDate),DateUtil.ymd().format(endDate));

        //将获取到的历史数据以：  日期@@sensorCode 为key , 相同的存放同一个 detailList ,以便检查每个监测点每个日期的数据量(正常为48)
        List<SensorStatResultDTO.Detail> detailList = new ArrayList<>();
        Map<String,List<SensorStatResultDTO.Detail>> dataMap = new HashMap<>();



        //1.将date@@SENCODE 格式初始化列表
        for (String date: dateList){
            for(SensorStatResultDTO.Detail detail:sensorStatResultDTO.getDetails()) {
                String key = date + "@@" + detail.getSensorCode();
                dataMap.put(key,new ArrayList<>());
            }
        }
        //2.处理数据
        for(SensorStatResultDTO.Detail detail:sensorStatResultDTO.getDetails()) {

            String recordDate = DateUtil.formatToDateStr(detail.getRecordTime());
            String key = recordDate + "@@" + detail.getSensorCode();

            detailList = dataMap.get(key);
            if (CollectionUtils.isEmpty(detailList)) {
                detailList = new ArrayList<>();
            }
            detailList.add(detail);

            dataMap.put(key,detailList);
        }



        List<DataCheckResultDetail> dataCheckResultDetailList = new ArrayList<>();

        for (String key: dataMap.keySet()){
            List<SensorStatResultDTO.Detail> resultDetailList = dataMap.get(key);
            resultDetailList =  resultDetailList.stream().filter(x->x.getValue() != null).collect(Collectors.toList());
            if (resultDetailList != null){
                if (resultDetailList.size() < RECORDS_OF_DAY){
                    System.out.println(key+" 缺失数据记录数:"+(RECORDS_OF_DAY -resultDetailList.size()));
                    resultDetailList.forEach(detail->{
                        System.out.println(detail.getWarehouseCode()+"@@@"+detail.getSensorCode()+"@@@"+detail.getRecordTime());
                    });
                    String[] dateCode = key.split(SPLIT_CODE);
                    String recordDate = dateCode[0];
                    String sensorCode = dateCode[1];

                    Date tradeDate = DateUtils.parseDate(recordDate,"yyyy-MM-dd");
                    if (DateUtil.ymd().format(tradeDate).equals(DateUtil.ymd().format(DateUtil.today()))){//当天不记录
                        continue;
                    }

                    WarehouseSensorDTO warehouseSensorDTO = warehouseSensorDTOMap.get(sensorCode);
                    DataCheckResultDetail detail = new DataCheckResultDetail();
                    detail.setCheckStatus(1);
                    detail.setCheckTime(new Date());
                    detail.setDataCheckConfigDetailId(Long.valueOf(dataCheckConfigDetailDTO.getId()));
                    detail.setSensorCode(sensorCode);
                    detail.setTradeDate(tradeDate);
                    detail.setMissQuantity(RECORDS_OF_DAY -resultDetailList.size());
                    if(warehouseSensorDTO != null){
                        detail.setWarehouseSensorId(Long.valueOf(warehouseSensorDTO.getId()));;
                        detail.setWarehouseCode(warehouseSensorDTO.getWarehouseCode());
                        detail.setRegionCode(warehouseSensorDTO.getRegionCode());
                    }
                    dataCheckResultDetailList.add(detail);
                }else{
                    System.out.println(key+" 数据完整"+resultDetailList.size());
                    //打印明细记录，方便调试
                    resultDetailList.forEach(x->{
                          System.out.println(x.getWarehouseCode()+"@@@"+x.getSensorCode()+"@@@"+x.getRecordTime()+" "+x.getValue());
                    });
                }
            }
        }

        return dataCheckResultDetailList;
    }

    @Override
    public List<DataCheckResult> doCheckData(DataCheckResult dataCheckResult) {
        dataCheckResult = dataCheckResultService.findById(dataCheckResult.getId());
        DataCheckConfigDetail dataCheckConfigDetail = checkConfigDetailService.getById(dataCheckResult.getDataCheckConfigDetailId());
        DataCheckConfigDetailDTO dataCheckConfigDetailDTO = BeanUtil.transform(dataCheckConfigDetail,DataCheckConfigDetailDTO.class,DataCheckConfigDetailConvert.toVO());
        this.doCheckData(dataCheckConfigDetailDTO,dataCheckResult.getCheckStartDate(),dataCheckResult.getCheckEndDate());
        return null;
    }
}
