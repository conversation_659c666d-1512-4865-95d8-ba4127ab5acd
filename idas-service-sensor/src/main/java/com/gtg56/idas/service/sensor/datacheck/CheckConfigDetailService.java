package com.gtg56.idas.service.sensor.datacheck;

import com.gtg56.idas.common.entity.dto.sensor.DataCheckConfigDetailDTO;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfig;
import com.gtg56.idas.common.entity.jdbc.DataCheckConfigDetail;
import com.gtg56.idas.common.entity.jdbc.DataCheckResultDetail;
import com.gtg56.idas.common.service.IDataCheckConfigDetailService;
import com.gtg56.idas.common.service.IDataCheckConfigService;
import com.gtg56.idas.service.define.ICheckConfigDetailService;
import com.gtg56.idas.service.define.ICheckConfigService;
import com.gtg56.lark.common.base.query.BaseQuery;
import com.gtg56.lark.web.PageObject;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@DubboService(
        interfaceClass = ICheckConfigDetailService.class, protocol = "dubbo", registry = "zookeeper", timeout = 10000,
        filter = "-exception"
)
public class CheckConfigDetailService implements ICheckConfigDetailService {

    @Resource(name = "dataCheckConfigDetailService")
    private IDataCheckConfigDetailService dataCheckConfigDetailService;

    @Override
    public DataCheckConfigDetail saveData(DataCheckConfigDetail dataCheckConfigDetail) {
        return dataCheckConfigDetailService.saveData(dataCheckConfigDetail);
    }

    @Override
    public DataCheckConfigDetail updateData(DataCheckConfigDetail dataCheckConfigDetail) {
        return dataCheckConfigDetailService.updateData(dataCheckConfigDetail);
    }

    @Override
    public DataCheckConfigDetail getById(Long id) {
        return dataCheckConfigDetailService.findById(id);
    }

    @Override
    public DataCheckConfigDetail removeById(Long id) {
        dataCheckConfigDetailService.deleteById(id);
        return null;
    }

    @Override
    public PageObject<DataCheckConfigDetail> pageByMap(Map<String, Object> var1, PageObject var2) {
        return dataCheckConfigDetailService.pageByMap(var1,var2);
    }

    @Override
    public PageObject<DataCheckConfigDetail> pageByQuery(BaseQuery var1, PageObject var2) {
        return dataCheckConfigDetailService.pageByQuery(var1,var2);
    }

    @Override
    public List<DataCheckConfigDetail> listByIds(String ids) {
        return dataCheckConfigDetailService.findByIds(ids);
    }

    @Override
    public List<DataCheckConfigDetailDTO> findByConfigId(Long dataCheckConfigId) {
        return dataCheckConfigDetailService.findByConfigId(dataCheckConfigId);
    }
}
