<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="idas-service-sensor" version="1.0" architecture="biz"/>

    <dubbo:reference id="authService" interface="com.gtg56.idas.service.define.IAuthService" lazy="true" check="false"/>

    <import resource="classpath:/spring/dubbo.xml"/>
    <import resource="classpath:/spring/redis.xml"/>
    <import resource="classpath:/spring/hbase.xml"/>

    <bean id="tsdbClient" class="com.gtg56.idas.common.tool.http.WarehouseSensorClient">
        <constructor-arg name="baseAddress" value="${tsdb.address}"/>
    </bean>

    <bean id="zjFDAClient" class="com.gtg56.idas.common.tool.http.ZJFDAReportClient">
        <constructor-arg name="baseAddress" value="http://wsdjg.zjfda.gov.cn:8080"/>

        <property name="cropCode" value="JY33010401"/>
        <property name="password" value="681410"/>
        <property name="code" value="c713302682797b23dba09841e89ac88958b7a2f703f89b6f6ba940f383c130a4"/>
    </bean>
</beans>